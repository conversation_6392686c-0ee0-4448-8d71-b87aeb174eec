using Bootis.Estoque.Application.Requests.MovimentoEstoque.Cadastrar;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.UnitTests.Fixtures.MovimentoEstoque;

public static class CadastrarMovimentoEstoqueCommandFakes
{
    public static CadastrarRequest CriarCommandValidoEntrada()
    {
        return new CadastrarRequest
        {
            DataLancamento = DateTime.UtcNow,
            Quantidade = 1,
            EmpresaId = 2.ToGuid(),
            LoteId = 3.ToGuid(),
            TipoOperacao = TipoOperacao.Entrada,
            LocalEstoqueId = 4.ToGuid(),
            OperacaoEstoqueId = 5.ToGuid(),
            UnidadeMedidaId = UnidadeMedidaAbreviacao.kg
        };
    }

    public static CadastrarRequest CriarCommandValidoSaida()
    {
        return new CadastrarRequest
        {
            DataLancamento = DateTime.UtcNow,
            Quantidade = 1,
            EmpresaId = 2.ToGuid(),
            LoteId = 3.ToGuid(),
            TipoOperacao = TipoOperacao.Saida,
            LocalEstoqueId = 4.ToGuid(),
            OperacaoEstoqueId = 5.ToGuid(),
            UnidadeMedidaId = UnidadeMedidaAbreviacao.kg
        };
    }

    public static CadastrarRequest CriarCommandValidoSemOperacaoEstoque()
    {
        return new CadastrarRequest
        {
            DataLancamento = DateTime.UtcNow,
            Quantidade = 1,
            EmpresaId = 2.ToGuid(),
            LoteId = 3.ToGuid(),
            TipoOperacao = TipoOperacao.Entrada,
            LocalEstoqueId = 4.ToGuid(),
            OperacaoEstoqueId = 5.ToGuid(),
            UnidadeMedidaId = UnidadeMedidaAbreviacao.kg
        };
    }

    public static CadastrarRequest CriarCommandValidoSaida_SemUnidadeMedida()
    {
        return new CadastrarRequest
        {
            DataLancamento = DateTime.UtcNow,
            Quantidade = 1,
            EmpresaId = 2.ToGuid(),
            LoteId = 3.ToGuid(),
            TipoOperacao = TipoOperacao.Saida,
            LocalEstoqueId = 4.ToGuid(),
            OperacaoEstoqueId = 5.ToGuid(),
            UnidadeMedidaId = 0
        };
    }
}
