using Bootis.Catalogo.Application.Requests.Produto.Cadastrar;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoEmbalagem;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.Produto;

public class CadastrarEmbalagemCommandFake
{
    public static CadastrarEmbalagemRequest CreateCommandValido()
    {
        return new CadastrarEmbalagemRequest
        {
            ClasseProdutoId = TipoClasseProdutoAbreviacao.Embalagem,
            ControlaLote = true,
            ControlaQualidade = true,
            Descricao = "Produto",
            DescricaoRotulo = "Rotulo",
            Etiqueta = false,
            FornecedorId = Uuid.NewSequential(),
            SubGrupoId = Uuid.NewSequential(),
            MargemLucro = 100,
            UnidadeEstoqueId = UnidadeMedidaAbreviacao.un,
            UsoContinuo = false,
            ValorCusto = 50,
            ValorVenda = 100,
            DesativarProjecaoEstoque = false,
            EstoqueMinimo = 1,
            EstoqueMaximo = 10,
            ClassificacaoEmbalagemId = Uuid.NewSequential(),
            Volume = 10,
            EmbalagemAssociacao = new List<ProdutoEmbalagemAssociacaoDto>
            {
                new()
                {
                    QuantidadeEmbalagem = 1
                }
            },
            NumeroCapsulaAssociacao = new List<ProdutoEmbalagemCapsulaTamanhoAssociacaoDto>
            {
                new()
                {
                    NumeroCapsulaId = Guid.Empty,
                    QuantidadeCapsula = 1,
                }
            }
        };
    }

    public static CadastrarEmbalagemRequest CreateCommandValidoSemFornecedor()
    {
        return new CadastrarEmbalagemRequest
        {
            ClasseProdutoId = TipoClasseProdutoAbreviacao.Embalagem,
            ControlaLote = true,
            ControlaQualidade = true,
            Descricao = "Produto",
            DescricaoRotulo = "Rotulo",
            Etiqueta = false,
            FornecedorId = null,
            SubGrupoId = Uuid.NewSequential(),
            MargemLucro = 100,
            UnidadeEstoqueId = UnidadeMedidaAbreviacao.un,
            UsoContinuo = false,
            ValorCusto = 50,
            ValorVenda = 100,
            DesativarProjecaoEstoque = false,
            EstoqueMinimo = 1,
            EstoqueMaximo = 10,
            ClassificacaoEmbalagemId = Uuid.NewSequential(),
            Volume = 10,
            EmbalagemAssociacao = new List<ProdutoEmbalagemAssociacaoDto>
            {
                new()
                {
                    QuantidadeEmbalagem = null,
                    ProdutoEmbalagemId = null
                }
            },
            NumeroCapsulaAssociacao = new List<ProdutoEmbalagemCapsulaTamanhoAssociacaoDto>
            {
                new()
                {
                    QuantidadeCapsula = null,
                    NumeroCapsulaId = null
                }
            }
        };
    }

    public static CadastrarEmbalagemRequest CreateCommandValidoSemUnidadeMedida()
    {
        return new CadastrarEmbalagemRequest
        {
            ClasseProdutoId = TipoClasseProdutoAbreviacao.Embalagem,
            ControlaLote = true,
            ControlaQualidade = true,
            Descricao = "Produto",
            DescricaoRotulo = "Rotulo",
            Etiqueta = false,
            FornecedorId = null,
            SubGrupoId = Uuid.NewSequential(),
            MargemLucro = 100,
            UsoContinuo = false,
            ValorCusto = 50,
            ValorVenda = 100,
            DesativarProjecaoEstoque = false,
            EstoqueMinimo = 1,
            EstoqueMaximo = 10,
            ClassificacaoEmbalagemId = Uuid.NewSequential(),
            Volume = 10,
            EmbalagemAssociacao = new List<ProdutoEmbalagemAssociacaoDto>
            {
                new()
                {
                    QuantidadeEmbalagem = 1,
                    ProdutoEmbalagemId = Uuid.NewSequential()
                }
            },
            NumeroCapsulaAssociacao = new List<ProdutoEmbalagemCapsulaTamanhoAssociacaoDto>
            {
                new()
                {
                    QuantidadeCapsula = 1,
                    NumeroCapsulaId = 1.ToGuid()
                }
            }
        };
    }
}
