using Bootis.Organizacional.Application.Requests.Seed.Cadastrar;
using Bootis.Organizacional.Application.UseCases.Seed;
using Bootis.Organizacional.Domain.Statics;
using Bootis.Organizacional.UnitTests.Fixtures;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;
using DomainAggregate = Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate;

namespace Bootis.Organizacional.UnitTests.Application.Seed;

[Collection(nameof(AccountManagerDomainCollection))]
public class CadastrarPermissoesEmpresaRequestHandlerTests : BaseTest
{
    private readonly CadastrarPermissoesEmpresaRequestHandler _handler;
    private readonly Mock<DomainAggregate.IGrupoRepository> _mockGrupoRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public CadastrarPermissoesEmpresaRequestHandlerTests()
    {
        _mockGrupoRepository = new Mock<DomainAggregate.IGrupoRepository>();
        _handler = new CadastrarPermissoesEmpresaRequestHandler(unitOfWork.Object, UserContext.Object,
            _mockGrupoRepository.Object);
    }

    [Fact]
    public async Task ExecutarSeed_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = new CadastrarPermissoesEmpresaRequest
        {
            UserId = Uuid.NewSequential(),
            TenantId = Uuid.NewSequential(),
            GroupTenantId = Uuid.NewSequential()
        };

        var perms = PermissoesStatic.GetPermissoes();
        var model = new DomainAggregate.Grupo("Administrativo", "Administrativo");

        foreach (var permissao in perms) model.AdicionarPermissao(permissao.Id);

        _mockGrupoRepository.Setup(l => l.Add(model))
            .Verifiable();

        unitOfWork
            .Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert

        _mockGrupoRepository.Verify(x => x.Add(It.Is<DomainAggregate.Grupo>(x => x.Permissoes.Count == perms.Count())));
    }
}
