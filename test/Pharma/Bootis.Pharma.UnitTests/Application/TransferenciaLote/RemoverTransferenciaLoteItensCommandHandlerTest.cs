using Bootis.Estoque.Application.Requests.TransferenciaLote.Remover;
using Bootis.Estoque.Application.UseCases.TransferenciaLote;
using Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;
using TransferenciaLoteItens = Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLoteItens;

namespace Bootis.Estoque.UnitTests.Application.TransferenciaLote;

public class RemoverTransferenciaLoteItensCommandHandlerTest : BaseTest
{
    private readonly RemoverItensRequestHandler _handler;
    private readonly Mock<ITransferenciaLoteRepository> _transferenciaLoteRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public RemoverTransferenciaLoteItensCommandHandlerTest()
    {
        _transferenciaLoteRepository = new Mock<ITransferenciaLoteRepository>();

        _handler = new RemoverItensRequestHandler(unitOfWork.Object, _transferenciaLoteRepository.Object);
    }

    [Fact]
    public async Task RemoverTransferenciaLoteItens_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = new RemoverItensRequest(Uuid.NewSequential());

        var transferenciaLote = new Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote(
            UsuarioFakes.GerarUsuarioValido().Id, LocalEstoqueFake.CreateLocalEstoqueValido(),
            LocalEstoqueFake.CreateLocalEstoqueValido(), "Transferencia");

        var transferenciaLoteItem = new TransferenciaLoteItens(ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            LoteFake.CreateLoteValido().Id, 1, UnidadeMedidaAbreviacao.UI, 1.ToGuid(), 1.ToGuid(), transferenciaLote);

        _transferenciaLoteRepository.Setup(l => l.ObterItensPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(transferenciaLoteItem));

        _transferenciaLoteRepository.Setup(l => l.Remove(transferenciaLoteItem))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _transferenciaLoteRepository.Verify(
            l => l.Remove(It.Is<TransferenciaLoteItens>(m => m.Id == transferenciaLoteItem.Id)), Times.Once);
    }

    [Fact]
    public async Task RemoverTransferenciaLoteItens_ExecutadoComErro()
    {
        //Arrange
        var cmd = new RemoverItensRequest(Uuid.NewSequential());

        var transferenciaLote = new Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote(
            UsuarioFakes.GerarUsuarioValido().Id, LocalEstoqueFake.CreateLocalEstoqueValido(),
            LocalEstoqueFake.CreateLocalEstoqueValido(), "Transferencia");

        var transferenciaLoteItem = new TransferenciaLoteItens(ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            LoteFake.CreateLoteValido().Id, 1, UnidadeMedidaAbreviacao.UI, 1.ToGuid(), 1.ToGuid(), transferenciaLote);

        _transferenciaLoteRepository.Setup(l => l.ObterItensPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult<TransferenciaLoteItens>(default));

        _transferenciaLoteRepository.Setup(l => l.Remove(transferenciaLoteItem))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _transferenciaLoteRepository.Verify(
            l => l.Remove(It.Is<TransferenciaLoteItens>(m => m.Id == transferenciaLoteItem.Id)), Times.Never);
    }
}
