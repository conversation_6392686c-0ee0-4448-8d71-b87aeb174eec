using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Application.UseCases.TransferenciaLote;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;
using Bootis.Estoque.UnitTests.Fixtures.TransferenciaLote;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using MediatR;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.TransferenciaLote;

public class CadastrarTransferenciaLoteCommandHandlerTest : BaseTest
{
    private readonly CadastrarRequestHandler _handler;
    private readonly Mock<ILocalEstoqueRepository> _localEstoqueRepository;
    private readonly Mock<ILoteRepository> _loteRepository;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Mock<IProdutoRepository> _produtoRepository;
    private readonly Mock<ISaldoEstoqueRepository> _saldoEstoqueRepository;
    private readonly Mock<IOperacaoEstoqueRepository> _operacaoEstoqueRepository;
    private readonly Mock<ITransferenciaLoteRepository> _transferenciaLoteRepository;
    private readonly Mock<IUsuarioRepository> _usuarioRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public CadastrarTransferenciaLoteCommandHandlerTest()
    {
        _mediatorMock = new Mock<IMediator>();
        _transferenciaLoteRepository = new Mock<ITransferenciaLoteRepository>();
        _localEstoqueRepository = new Mock<ILocalEstoqueRepository>();
        _usuarioRepositoryMock = new Mock<IUsuarioRepository>();
        _produtoRepository = new Mock<IProdutoRepository>();
        _loteRepository = new Mock<ILoteRepository>();
        _saldoEstoqueRepository = new Mock<ISaldoEstoqueRepository>();
        _operacaoEstoqueRepository = new Mock<IOperacaoEstoqueRepository>();
        _handler = new CadastrarRequestHandler(unitOfWork.Object, UserContext.Object,
            _transferenciaLoteRepository.Object,
            _localEstoqueRepository.Object,
            _usuarioRepositoryMock.Object,
            _produtoRepository.Object,
            _loteRepository.Object,
            _saldoEstoqueRepository.Object,
            _operacaoEstoqueRepository.Object,
            _mediatorMock.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarTransferenciaLote_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = CadastrarTransferenciaLoteCommandFake.CadastrarTransferenciaLotesCommandValido();
        var localEstoqueOrigem = LocalEstoqueFake.CreateLocalEstoqueValido();
        var localEstoqueDestino = LocalEstoqueFake.CreateLocalEstoqueValido();
        var lotes = new List<Lote> { LoteFake.CreateLoteValido() };
        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();
        var produtos = new List<Produto>
        {
            ProdutoFake.CreateProdutoFake_Embalagem()
        };

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueOrigemId)).Returns(
            Task.FromResult(localEstoqueOrigem));

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueDestinoId)).Returns(
            Task.FromResult(localEstoqueDestino));

        _usuarioRepositoryMock.Setup(l => l.GetByIdAsync(UserContext.Object.UserId))
            .Returns(
                Task.FromResult(UsuarioFakes.GerarUsuarioValido()));

        _transferenciaLoteRepository.Setup(l =>
                l.Add(It.IsAny<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>()))
            .Verifiable();

        _produtoRepository.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtos));

        _loteRepository.Setup(l => l.ObterLotesPorIdAsync(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(lotes));

        _saldoEstoqueRepository.Setup(l =>
            l.ObterSaldoEstoquePorLoteLocalEstoqueIdAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
            Task.FromResult(saldoEstoque));

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _transferenciaLoteRepository.Verify(
            l => l.Add(It.Is<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>(m =>
                m.Observacao == cmd.Observacao)),
            Times.Once);
    }

    [Fact]
    public async Task CadastrarTransferenciaLote_LocaisEstoqueIguais_ExecutadoComErro()
    {
        //Arrange
        var cmd = CadastrarTransferenciaLoteCommandFake.CreateCommandValido_LocaisEstoqueIguais();

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _transferenciaLoteRepository.Verify(
            l => l.Add(It.Is<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>(m =>
                m.LocalEstoqueDestino.Id == cmd.LocalDeEstoqueDestinoId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarTransferenciaLote_ExecutadoComErro_LocalEstoqueOrigemGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarTransferenciaLoteCommandFake.CreateCommandValido();

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueOrigemId)).Returns(
            Task.FromResult<Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _transferenciaLoteRepository.Verify(
            l => l.Add(It.Is<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>(m =>
                m.LocalEstoqueDestino.Id == cmd.LocalDeEstoqueDestinoId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarTransferenciaLote_ExecutadoComErro_LocalEstoqueDestinoGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarTransferenciaLoteCommandFake.CreateCommandValido();
        var localEstoqueOrigem = LocalEstoqueFake.CreateLocalEstoqueValido();

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueOrigemId)).Returns(
            Task.FromResult(localEstoqueOrigem));

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueDestinoId)).Returns(
            Task.FromResult<Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _transferenciaLoteRepository.Verify(
            l => l.Add(It.Is<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>(m =>
                m.LocalEstoqueDestino.Id == cmd.LocalDeEstoqueDestinoId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarTransferenciaLote_ExecutadoComErro_UsuarioGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarTransferenciaLoteCommandFake.CreateCommandValido();
        var localEstoqueOrigem = LocalEstoqueFake.CreateLocalEstoqueValido();
        var localEstoqueDestino = LocalEstoqueFake.CreateLocalEstoqueValido();

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueOrigemId)).Returns(
            Task.FromResult(localEstoqueOrigem));

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueDestinoId)).Returns(
            Task.FromResult(localEstoqueDestino));

        _usuarioRepositoryMock.Setup(l => l.GetByIdAsync(UserContext.Object.UserId))
            .Returns(
                Task.FromResult<Usuario>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _transferenciaLoteRepository.Verify(
            l => l.Add(It.Is<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>(m =>
                m.LocalEstoqueDestino.Id == cmd.LocalDeEstoqueDestinoId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarTransferenciaLote_ExecutadoComErro_ProdutoGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarTransferenciaLoteCommandFake.CreateCommandValido();
        var localEstoqueOrigem = LocalEstoqueFake.CreateLocalEstoqueValido();
        var localEstoqueDestino = LocalEstoqueFake.CreateLocalEstoqueValido();
        var produto = new List<Produto>();

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueOrigemId)).Returns(
            Task.FromResult(localEstoqueOrigem));

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueDestinoId)).Returns(
            Task.FromResult(localEstoqueDestino));

        _usuarioRepositoryMock.Setup(l => l.GetByIdAsync(UserContext.Object.UserId))
            .Returns(
                Task.FromResult(UsuarioFakes.GerarUsuarioValido()));

        _transferenciaLoteRepository.Setup(l =>
                l.Add(It.IsAny<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>()))
            .Verifiable();

        _produtoRepository.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produto));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
    }

    [Fact]
    public async Task CadastrarTransferenciaLote_ExecutadoComErro_LoteGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarTransferenciaLoteCommandFake.CreateCommandValido();
        var localEstoqueOrigem = LocalEstoqueFake.CreateLocalEstoqueValido();
        var localEstoqueDestino = LocalEstoqueFake.CreateLocalEstoqueValido();
        var lotes = new List<Lote>();
        var produtos = new List<Produto>
        {
            ProdutoFake.CreateProdutoFake_CapsulaPronta()
        };

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueOrigemId)).Returns(
            Task.FromResult(localEstoqueOrigem));

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueDestinoId)).Returns(
            Task.FromResult(localEstoqueDestino));

        _usuarioRepositoryMock.Setup(l => l.GetByIdAsync(UserContext.Object.UserId))
            .Returns(
                Task.FromResult(UsuarioFakes.GerarUsuarioValido()));

        _transferenciaLoteRepository.Setup(l =>
                l.Add(It.IsAny<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>()))
            .Verifiable();

        _produtoRepository.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtos));

        _loteRepository.Setup(l => l.ObterLotesPorIdAsync(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(lotes));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
    }


    [Fact]
    public async Task CadastrarTransferenciaLote_ExecutadoComErro_SaldoEstoqueGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarTransferenciaLoteCommandFake.CreateCommandValido();
        var localEstoqueOrigem = LocalEstoqueFake.CreateLocalEstoqueValido();
        var localEstoqueDestino = LocalEstoqueFake.CreateLocalEstoqueValido();
        var lotes = new List<Lote> { LoteFake.CreateLoteValido() };
        var produtos = new List<Produto>
        {
            ProdutoFake.CreateProdutoFake_CapsulaPronta()
        };

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueOrigemId)).Returns(
            Task.FromResult(localEstoqueOrigem));

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueDestinoId)).Returns(
            Task.FromResult(localEstoqueDestino));

        _usuarioRepositoryMock.Setup(l => l.GetByIdAsync(UserContext.Object.UserId))
            .Returns(
                Task.FromResult(UsuarioFakes.GerarUsuarioValido()));

        _transferenciaLoteRepository.Setup(l =>
                l.Add(It.IsAny<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>()))
            .Verifiable();

        _produtoRepository.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtos));

        _loteRepository.Setup(l => l.ObterLotesPorIdAsync(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(lotes));

        _saldoEstoqueRepository.Setup(l =>
            l.ObterSaldoEstoquePorLoteLocalEstoqueIdAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
            Task.FromResult<SaldoEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
    }

    [Fact]
    public async Task CadastrarTransferenciaLote_ExecutadoComErro_LoteNaoPossuiQuantidadeProduto()
    {
        //Arrange
        var cmd = CadastrarTransferenciaLoteCommandFake
            .CadastrarTransferenciaLotesCommandValido_QuantidadeMaiorQueSaldoEstoque();
        var localEstoqueOrigem = LocalEstoqueFake.CreateLocalEstoqueValido();
        var localEstoqueDestino = LocalEstoqueFake.CreateLocalEstoqueValido();
        var lotes = new List<Lote> { LoteFake.CreateLoteValido() };
        var produtos = new List<Produto>
        {
            ProdutoFake.CreateProdutoFake_CapsulaPronta()
        };
        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueOrigemId)).Returns(
            Task.FromResult(localEstoqueOrigem));

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalDeEstoqueDestinoId)).Returns(
            Task.FromResult(localEstoqueDestino));

        _usuarioRepositoryMock.Setup(l => l.GetByIdAsync(UserContext.Object.UserId))
            .Returns(
                Task.FromResult(UsuarioFakes.GerarUsuarioValido()));

        _transferenciaLoteRepository.Setup(l =>
                l.Add(It.IsAny<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>()))
            .Verifiable();

        _produtoRepository.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtos));

        _loteRepository.Setup(l => l.ObterLotesPorIdAsync(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(lotes));

        _saldoEstoqueRepository.Setup(l =>
            l.ObterSaldoEstoquePorLoteLocalEstoqueIdAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
            Task.FromResult(saldoEstoque));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
    }
}
