using Bootis.Estoque.Application.Requests.TransferenciaLote.Remover;
using Bootis.Estoque.Application.UseCases.TransferenciaLote;
using Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Application.TransferenciaLote;

public class RemoverTransferenciaLoteCommandHandlerTest : BaseTest
{
    private readonly RemoverRequestHandler _handler;
    private readonly Mock<ITransferenciaLoteRepository> _transferenciaLoteRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();


    public RemoverTransferenciaLoteCommandHandlerTest()
    {
        _transferenciaLoteRepository = new Mock<ITransferenciaLoteRepository>();
        _handler = new RemoverRequestHandler(unitOfWork.Object, _transferenciaLoteRepository.Object);
    }

    [Fact]
    public async Task RemoverTransferenciaLote_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = new RemoverRequest(Uuid.NewSequential());

        var transferenciaLote = new Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote(
            UsuarioFakes.GerarUsuarioValido().Id, LocalEstoqueFake.CreateLocalEstoqueValido(),
            LocalEstoqueFake.CreateLocalEstoqueValido(), "Transferencia");

        _transferenciaLoteRepository.Setup(l => l.ObterPorIdAsync(cmd.TransferenciaLoteId))
            .Returns(Task.FromResult(transferenciaLote));

        _transferenciaLoteRepository.Setup(l => l.Remove(transferenciaLote))
            .Verifiable();
        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _transferenciaLoteRepository.Verify(
            l => l.Remove(
                It.Is<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>(m =>
                    m.Id == transferenciaLote.Id)),
            Times.Once);
    }

    [Fact]
    public async Task RemoverTransferenciaLote_ExecutadoComErro()
    {
        //Arrange
        var cmd = new RemoverRequest(Uuid.NewSequential());

        var transferenciaLote = new Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote(
            UsuarioFakes.GerarUsuarioValido().Id, LocalEstoqueFake.CreateLocalEstoqueValido(),
            LocalEstoqueFake.CreateLocalEstoqueValido(), "Transferencia");

        _transferenciaLoteRepository.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
            .Returns(Task.FromResult(transferenciaLote));

        _transferenciaLoteRepository.Setup(l => l.Remove(transferenciaLote))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _transferenciaLoteRepository.Verify(
            l => l.Remove(
                It.Is<Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote>(m =>
                    m.Id == transferenciaLote.Id)),
            Times.Never);
    }
}
