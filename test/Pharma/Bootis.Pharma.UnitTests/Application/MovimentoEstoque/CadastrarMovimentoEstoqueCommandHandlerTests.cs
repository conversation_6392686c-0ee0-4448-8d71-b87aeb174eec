using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Application.UseCases.MovimentoEstoque;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Estoque.UnitTests.Fixtures.MovimentoEstoque;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;
using Movimento = Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate;

namespace Bootis.Estoque.UnitTests.Application.MovimentoEstoque;

public class CadastrarMovimentoEstoqueCommandHandlerTests : BaseTest
{
    private readonly Mock<IEmpresaRepository> _empresaRepository;
    private readonly CadastrarRequestHandler _handler;
    private readonly Mock<ILocalEstoqueRepository> _localEstoqueRepositoryMock;
    private readonly Mock<ILoteRepository> _loteRepositoryMock;
    private readonly Mock<Movimento.IMovimentoEstoqueRepository> _movimentoEstoqueRepositoryMock;
    private readonly Mock<IOperacaoEstoqueRepository> _operacaoEstoqueRepositoryMock;

    private readonly Mock<IProdutoRepository> _produtoRepositoryMock;
    private readonly Mock<ISaldoEstoqueRepository> _saldoEstoqueRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public CadastrarMovimentoEstoqueCommandHandlerTests()
    {
        _movimentoEstoqueRepositoryMock = new Mock<Movimento.IMovimentoEstoqueRepository>();
        _loteRepositoryMock = new Mock<ILoteRepository>();
        _localEstoqueRepositoryMock = new Mock<ILocalEstoqueRepository>();
        _operacaoEstoqueRepositoryMock = new Mock<IOperacaoEstoqueRepository>();
        _saldoEstoqueRepositoryMock = new Mock<ISaldoEstoqueRepository>();

        _produtoRepositoryMock = new Mock<IProdutoRepository>();
        _empresaRepository = new Mock<IEmpresaRepository>();
        _handler = new CadastrarRequestHandler(unitOfWork.Object, UserContext.Object,
            _movimentoEstoqueRepositoryMock.Object,
            _loteRepositoryMock.Object, _localEstoqueRepositoryMock.Object, _operacaoEstoqueRepositoryMock.Object,
            _saldoEstoqueRepositoryMock.Object, _produtoRepositoryMock.Object, _empresaRepository.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarMovimentoEstoque_MovimentoEntrada_ExecutadoComSucesso()
    {
        //Arrange
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var usuario = UsuarioFakes.GerarUsuarioValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var movimento = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Entrada, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        var lote = LoteFake.CreateLoteValido();

        var operacaoEstoque = new OperacaoEstoque("Teste", TipoOperacao.Entrada, true, true, true, true);

        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoEntrada();

        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();
        saldoEstoque.AtualizarUnidadeMedida(UnidadeMedidaAbreviacao.g);

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(lote));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _operacaoEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int?>())).Returns(
            Task.FromResult(operacaoEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(saldoEstoque));

        _saldoEstoqueRepositoryMock.Setup(l => l.Update(It.IsAny<SaldoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _produtoRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(produto));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Add(It.IsAny<Movimento.MovimentoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await _handler.Handle(cmd, default);

        //Assert
        Assert.Equal(executionResult, movimento.Id);
        _movimentoEstoqueRepositoryMock.Verify(l => l.Add(It.Is<Movimento.MovimentoEstoque>(m =>
                m.EmpresaId == cmd.EmpresaId
                && m.UnidadeMedidaId == produto.UnidadeEstoqueId)),
            Times.Once);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarMovimentoEstoque_MovimentoEntrada_ExecutadoComSucesso_ComOperacaoEstoque()
    {
        //Arrange
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var usuario = UsuarioFakes.GerarUsuarioValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var movimento = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Entrada, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        var lote = LoteFake.CreateLoteValido();

        var operacaoEstoque = new OperacaoEstoque("Teste", TipoOperacao.Entrada, true, true, true, true);

        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoSemOperacaoEstoque();

        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();
        saldoEstoque.AtualizarUnidadeMedida(UnidadeMedidaAbreviacao.g);

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(lote));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _operacaoEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int?>())).Returns(
            Task.FromResult(operacaoEstoque));

        _operacaoEstoqueRepositoryMock
            .Setup(l => l.ObterOperacaoEstoquePorDescricaoETipo("Movimento Estoque", cmd.TipoOperacao)).Returns(
                Task.FromResult(operacaoEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(saldoEstoque));

        _saldoEstoqueRepositoryMock.Setup(l => l.Update(It.IsAny<SaldoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _produtoRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(produto));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Add(It.IsAny<Movimento.MovimentoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await _handler.Handle(cmd, default);

        //Assert
        Assert.Equal(executionResult, movimento.Id);
        _movimentoEstoqueRepositoryMock.Verify(l => l.Add(It.Is<Movimento.MovimentoEstoque>(m =>
                m.EmpresaId == cmd.EmpresaId
                && m.UnidadeMedidaId == produto.UnidadeEstoqueId)),
            Times.Once);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarMovimentoEstoque_MovimentoSaida_ExecutadoComSucesso()
    {
        //Arrange
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var usuario = UsuarioFakes.GerarUsuarioValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var movimento = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Saida, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        var lote = LoteFake.CreateLoteValido();

        var operacaoEstoque = new OperacaoEstoque("Teste", TipoOperacao.Entrada, true, true, true, true);

        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoSaida();

        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();
        saldoEstoque.AtualizarUnidadeMedida(UnidadeMedidaAbreviacao.g);

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(lote));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _operacaoEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int?>())).Returns(
            Task.FromResult(operacaoEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(saldoEstoque));

        _saldoEstoqueRepositoryMock.Setup(l => l.Update(It.IsAny<SaldoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _produtoRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(produto));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Add(It.IsAny<Movimento.MovimentoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await _handler.Handle(cmd, default);

        //Assert
        Assert.Equal(executionResult, movimento.Id);
        _movimentoEstoqueRepositoryMock.Verify(l => l.Add(It.Is<Movimento.MovimentoEstoque>(m =>
                m.EmpresaId == cmd.EmpresaId
                && m.UnidadeMedidaId == cmd.UnidadeMedidaId)),
            Times.Once);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarMovimentoEstoque_MovimentoEntradaSemSaldo_ExecutadoComSucesso()
    {
        //Arrange
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var usuario = UsuarioFakes.GerarUsuarioValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var movimento = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Entrada, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        var lote = LoteFake.CreateLoteValido();

        var operacaoEstoque = new OperacaoEstoque("Teste", TipoOperacao.Entrada, true, true, true, true);

        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoEntrada();

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(lote));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _operacaoEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int?>())).Returns(
            Task.FromResult(operacaoEstoque));

        _empresaRepository.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(EmpresaFakes.GerarEmpresaValido()));

        _saldoEstoqueRepositoryMock.Setup(l => l.Update(It.IsAny<SaldoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _produtoRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(produto));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Add(It.IsAny<Movimento.MovimentoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await _handler.Handle(cmd, default);

        //Assert
        Assert.Equal(executionResult, movimento.Id);
        _movimentoEstoqueRepositoryMock.Verify(l => l.Add(It.Is<Movimento.MovimentoEstoque>(m =>
                m.EmpresaId == cmd.EmpresaId
                && m.UnidadeMedidaId == produto.UnidadeEstoqueId)),
            Times.Once);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarMovimentoEstoque_MovimentoSaidaComUnidadeDiferente_ExecutadoComSucesso()
    {
        //Arrange
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var empresa = EmpresaFakes.GerarEmpresaValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var usuario = UsuarioFakes.GerarUsuarioValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var movimento = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Saida, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.kg, Uuid.NewSequential());

        var lote = LoteFake.CreateLoteValido();

        var operacaoEstoque = new OperacaoEstoque("Teste", TipoOperacao.Entrada, true, true, true, true);

        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoSaida();

        var saldoEstoque =
            new SaldoEstoque(empresa, produto, lote.Id, 1000, UnidadeMedidaAbreviacao.g, localEstoque.Id);

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(lote));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _operacaoEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int?>())).Returns(
            Task.FromResult(operacaoEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(saldoEstoque));

        _saldoEstoqueRepositoryMock.Setup(l => l.Update(It.IsAny<SaldoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _produtoRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(produto));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Add(It.IsAny<Movimento.MovimentoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await _handler.Handle(cmd, default);

        //Assert
        Assert.Equal(executionResult, movimento.Id);
        _movimentoEstoqueRepositoryMock.Verify(l => l.Add(It.Is<Movimento.MovimentoEstoque>(m =>
                m.EmpresaId == cmd.EmpresaId
                && m.UnidadeMedidaId == cmd.UnidadeMedidaId)),
            Times.Once);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarMovimentoEstoque_SemOperacaoEstoque_ExecutadoComSucesso()
    {
        //Arrange
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var usuario = UsuarioFakes.GerarUsuarioValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var movimento = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Entrada, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        var lote = LoteFake.CreateLoteValido();

        var operacaoEstoque = new OperacaoEstoque("Teste", TipoOperacao.Entrada, true, true, true, true);

        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoSemOperacaoEstoque();

        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();
        saldoEstoque.AtualizarUnidadeMedida(UnidadeMedidaAbreviacao.g);

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(lote));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(saldoEstoque));

        _saldoEstoqueRepositoryMock.Setup(l => l.Update(It.IsAny<SaldoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _produtoRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(produto));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Add(It.IsAny<Movimento.MovimentoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await _handler.Handle(cmd, default);

        //Assert
        Assert.Equal(executionResult, movimento.Id);
        _movimentoEstoqueRepositoryMock.Verify(l => l.Add(It.Is<Movimento.MovimentoEstoque>(m =>
                m.EmpresaId == cmd.EmpresaId
                && m.UnidadeMedidaId == produto.UnidadeEstoqueId)),
            Times.Once);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarMovimentoEstoque_ComOperacaoEstoque_ExecutadoComSucesso()
    {
        //Arrange
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var usuario = UsuarioFakes.GerarUsuarioValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var movimento = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Entrada, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        var lote = LoteFake.CreateLoteValido();

        var operacaoEstoque = new OperacaoEstoque("Teste", TipoOperacao.Entrada, true, true, true, true);

        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoEntrada();

        var saldoEstoque = new Mock<SaldoEstoque>();

        saldoEstoque.SetupGet(l => l.Id).Returns(1.ToGuid());

        saldoEstoque.Object.AtualizarUnidadeMedida(UnidadeMedidaAbreviacao.g);

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(lote));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(saldoEstoque.Object));

        _saldoEstoqueRepositoryMock.Setup(l => l.Update(It.IsAny<SaldoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _produtoRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(produto));

        _operacaoEstoqueRepositoryMock
            .Setup(l => l.ObterOperacaoEstoquePorDescricaoETipo(It.IsAny<string>(), cmd.TipoOperacao)).Returns(
                Task.FromResult(operacaoEstoque));

        _operacaoEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(cmd.OperacaoEstoqueId)).Returns(
            Task.FromResult(operacaoEstoque));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Add(It.IsAny<Movimento.MovimentoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await _handler.Handle(cmd, default);

        //Assert
        Assert.Equal(executionResult, movimento.Id);
        _movimentoEstoqueRepositoryMock.Verify(l => l.Add(It.Is<Movimento.MovimentoEstoque>(m =>
                m.EmpresaId == cmd.EmpresaId
                && m.UnidadeMedidaId == produto.UnidadeEstoqueId)),
            Times.Once);
    }

    [Fact]
    public async Task CadastrarMovimentoEstoque_ExecutadoComErro_LoteIdNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoEntrada();

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(cmd.LoteId)).Returns(
            Task.FromResult<Lote>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Add(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == cmd.EmpresaId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarMovimentoEstoque_ExecutadoComErro_ProdutoIdNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoEntrada();

        var lote = LoteFake.CreateLoteValido();

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(cmd.LoteId)).Returns(
            Task.FromResult(lote));

        _produtoRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult<Produto>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Add(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == cmd.EmpresaId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarMovimentoEstoque_ExecutadoComErro_LocalEstoqueIdNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoEntrada();

        var lote = LoteFake.CreateLoteValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(cmd.LoteId)).Returns(
            Task.FromResult(lote));

        _produtoRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(produto));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult<Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Add(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == cmd.EmpresaId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarMovimentoEstoque_ExecutadoComErro_EmpresaIdNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoEntrada();

        var lote = LoteFake.CreateLoteValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(cmd.LoteId)).Returns(
            Task.FromResult(lote));

        _produtoRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(produto));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(cmd.LoteId, cmd.LocalEstoqueId))
            .Returns(Task.FromResult<SaldoEstoque>(default));

        _empresaRepository.Setup(l => l.ObterPorIdAsync(UserContext.Object.TenantId)).Returns(
            Task.FromResult<Empresa>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Add(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == cmd.EmpresaId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarMovimentoEstoque_ExecutadoComErro_SaldoEstoqueIdNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoSaida();

        var lote = LoteFake.CreateLoteValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(cmd.LoteId)).Returns(
            Task.FromResult(lote));

        _produtoRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(produto));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(cmd.LoteId, cmd.LocalEstoqueId))
            .Returns(Task.FromResult<SaldoEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Add(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == cmd.EmpresaId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarMovimentoEstoque_ExecutadoComErro_OperacaoEstoqueIdNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoEntrada();

        var lote = LoteFake.CreateLoteValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();
        saldoEstoque.AtualizarUnidadeMedida(UnidadeMedidaAbreviacao.g);

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(cmd.LoteId)).Returns(
            Task.FromResult(lote));

        _produtoRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(produto));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
            .Returns(Task.FromResult(saldoEstoque));

        _operacaoEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(cmd.OperacaoEstoqueId)).Returns(
            Task.FromResult<OperacaoEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Add(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == cmd.EmpresaId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarMovimentoEstoque_ExecutadoComErro_ConversaoUnidadeMedidaIdNaoEncontrado()
    {
        //Arrange
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var usuario = UsuarioFakes.GerarUsuarioValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var movimento = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Entrada, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        movimento.AtualizarQuantidade(2);

        var lote = LoteFake.CreateLoteValido();

        var operacaoEstoque = new OperacaoEstoque("Teste", TipoOperacao.Entrada, true, true, true, true);

        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoSaida_SemUnidadeMedida();

        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();

        saldoEstoque.AtualizarUnidadeMedida(UnidadeMedidaAbreviacao.g);
        saldoEstoque.AtualizarSaldo(0);

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(lote));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(saldoEstoque));

        _saldoEstoqueRepositoryMock.Setup(l => l.Update(It.IsAny<SaldoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _produtoRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(produto));

        _operacaoEstoqueRepositoryMock
            .Setup(l => l.ObterOperacaoEstoquePorDescricaoETipo(It.IsAny<string>(), cmd.TipoOperacao)).Returns(
                Task.FromResult(operacaoEstoque));

        _operacaoEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(cmd.OperacaoEstoqueId)).Returns(
            Task.FromResult(operacaoEstoque));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Add(It.IsAny<Movimento.MovimentoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Add(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == cmd.EmpresaId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarMovimentoEstoque_ExecutadoComErro_UnidadeMedidaIdNaoEncontrado()
    {
        //Arrange
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var movimento = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Entrada, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        movimento.AtualizarQuantidade(2);

        var lote = LoteFake.CreateLoteValido();

        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoSaida_SemUnidadeMedida();

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(lote));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _produtoRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(produto));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Add(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == cmd.EmpresaId)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarMovimentoEstoque_ExecutadoComErro_SaldoEstoqueInsuficiente()
    {
        //Arrange
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var usuario = UsuarioFakes.GerarUsuarioValido();

        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();

        var movimento = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Entrada, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        movimento.AtualizarQuantidade(2);

        var lote = LoteFake.CreateLoteValido();

        var operacaoEstoque = new OperacaoEstoque("Teste", TipoOperacao.Entrada, true, true, true, true);

        var cmd = CadastrarMovimentoEstoqueCommandFakes.CriarCommandValidoSaida();

        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();

        saldoEstoque.AtualizarUnidadeMedida(UnidadeMedidaAbreviacao.g);
        saldoEstoque.AtualizarSaldo(0);

        _loteRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(lote));

        _localEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(localEstoque));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(saldoEstoque));

        _saldoEstoqueRepositoryMock.Setup(l => l.Update(It.IsAny<SaldoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _produtoRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>())).Returns(
            Task.FromResult(produto));

        _operacaoEstoqueRepositoryMock
            .Setup(l => l.ObterOperacaoEstoquePorDescricaoETipo(It.IsAny<string>(), cmd.TipoOperacao)).Returns(
                Task.FromResult(operacaoEstoque));

        _operacaoEstoqueRepositoryMock.Setup(l => l.GetByIdAsync(cmd.OperacaoEstoqueId)).Returns(
            Task.FromResult(operacaoEstoque));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Add(It.IsAny<Movimento.MovimentoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Add(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == cmd.EmpresaId)),
            Times.Never);
    }
}
