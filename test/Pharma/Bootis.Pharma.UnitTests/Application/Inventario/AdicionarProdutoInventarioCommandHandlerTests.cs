using Bootis.Estoque.Application.Interfaces;
using Bootis.Estoque.Application.UseCases.Inventario;
using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.UnitTests.Fixtures.Inventario;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;
using Bootis.Shared.UnitTests.Extensions;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.Inventario;

public class AdicionarProdutoInventarioCommandHandlerTests : BaseTest
{
    private readonly AdicionarProdutoRequestHandler _handler;
    private readonly Mock<IInventarioRepository> _inventarioRepositoryMock;
    private readonly Mock<ILocalEstoqueRepository> _localEstoqueRepositoryMock;
    private readonly Mock<ILoteRepository> _loteRepositoryMock;
    private readonly Mock<ISaldoEstoqueRepository> _saldoEstoqueRepositoryMock;
    private readonly Mock<IServicoEstoque> _servicoEstoqueMock;
    private readonly Mock<IUsuarioRepository> _usuarioRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AdicionarProdutoInventarioCommandHandlerTests()
    {
        _inventarioRepositoryMock = new Mock<IInventarioRepository>();
        _saldoEstoqueRepositoryMock = new Mock<ISaldoEstoqueRepository>();
        _localEstoqueRepositoryMock = new Mock<ILocalEstoqueRepository>();
        _loteRepositoryMock = new Mock<ILoteRepository>();
        _usuarioRepositoryMock = new Mock<IUsuarioRepository>();
        _servicoEstoqueMock = new Mock<IServicoEstoque>();
        _handler = new AdicionarProdutoRequestHandler(unitOfWork.Object, _inventarioRepositoryMock.Object,
            _saldoEstoqueRepositoryMock.Object,
            _localEstoqueRepositoryMock.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AdicionarProdutoLancamentoInventario_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = AdicionarProdutoInventarioCommandFake.CreateAdicionarProdutoInventarioCommandFake();
        var inventario = InventarioFake.CriarInventarioValido();
        var locaisEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var lotesIds = new Dictionary<Guid, Guid> { { cmd.Produtos.First().LoteId, 2.ToGuid() } };
        var usuarioId = 1.ToGuid();
        var saldosEstoque = new List<SaldoEstoque>
        {
            SaldoEstoqueFake.CreateSaldoEstoqueValido()
        };
        var saldosEstoqueNovo = new List<SaldoEstoque>
        {
            SaldoEstoqueFake.CreateSaldoEstoqueValido()
        };

        saldosEstoqueNovo.First().SetPropertyValue(nameof(SaldoEstoque.Id), 1.ToGuid());
        saldosEstoqueNovo.First().SetPropertyValue(nameof(SaldoEstoque.LoteId), 2.ToGuid());

        inventario.IniciarLancamento(saldosEstoque, usuarioId);

        _inventarioRepositoryMock.Setup(l => l.ObterInventarioPorIdAsync(cmd.InventarioId)).Returns(
            Task.FromResult(inventario));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(locaisEstoque));

        _saldoEstoqueRepositoryMock.Setup(l =>
                l.ObterSaldosEstoquePorLoteLocalEstoqueAsync(It.IsAny<List<Guid>>(), It.IsAny<Guid>()))
            .Returns(
                Task.FromResult(saldosEstoqueNovo));

        _inventarioRepositoryMock.Setup(l => l.Update(inventario))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _inventarioRepositoryMock.Verify(
            l => l.Update(It.Is<Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario>(m =>
                m.SequenciaGroupTenant == inventario.SequenciaGroupTenant)),
            Times.Once);
    }

    [Fact]
    public async Task AdicionarProdutoLancamentoInventario_StatusInvalido_ExecutadoComErro()
    {
        //Arrange
        var cmd = AdicionarProdutoInventarioCommandFake.CreateAdicionarProdutoInventarioCommandFake();
        var inventario = InventarioFake.CriarInventarioValido();

        _inventarioRepositoryMock.Setup(l => l.ObterInventarioPorIdAsync(cmd.InventarioId)).Returns(
            Task.FromResult(inventario));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _inventarioRepositoryMock.Verify(
            l => l.Update(It.Is<Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario>(m =>
                m.SequenciaGroupTenant == inventario.SequenciaGroupTenant)),
            Times.Never);
    }
}