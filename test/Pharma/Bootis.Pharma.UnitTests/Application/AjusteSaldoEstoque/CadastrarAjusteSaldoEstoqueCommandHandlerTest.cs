using Bootis.Estoque.Application.UseCases.AjusteSaldoEstoque;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.UnitTests.Fixtures.AjusteSaldoEstoque;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;
using MediatR;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.AjusteSaldoEstoque;

public class CadastrarAjusteSaldoEstoqueCommandHandlerTest : BaseTest
{
    private readonly CadastrarRequestHandler _handler;
    private readonly Mock<ILocalEstoqueRepository> _localEstoqueRepositoryMock;
    private readonly Mock<ILoteRepository> _loteRepositoryMock;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Mock<ISaldoEstoqueRepository> _saldoEstoqueRepositoryMock;
    private readonly Mock<IUserContext> _userContext = new();
    private readonly Mock<IOperacaoEstoqueRepository> _operacaoEstoqueRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public CadastrarAjusteSaldoEstoqueCommandHandlerTest()
    {
        _localEstoqueRepositoryMock = new Mock<ILocalEstoqueRepository>();
        _mediatorMock = new Mock<IMediator>();
        _saldoEstoqueRepositoryMock = new Mock<ISaldoEstoqueRepository>();
        _loteRepositoryMock = new Mock<ILoteRepository>();
        _operacaoEstoqueRepositoryMock = new Mock<IOperacaoEstoqueRepository>();
        _userContext = new Mock<IUserContext>();
        _handler = new CadastrarRequestHandler(unitOfWork.Object, _mediatorMock.Object,
            _localEstoqueRepositoryMock.Object,
            _saldoEstoqueRepositoryMock.Object, _operacaoEstoqueRepositoryMock.Object, _userContext.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarAjusteSaldoEstoque_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = CadastrarAjusteSaldoEstoqueCommandFake.CriarCommandValido();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var usuario = 1.ToGuid();
        var loteId = 1.ToGuid();

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));

        _saldoEstoqueRepositoryMock.Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(loteId, localEstoque.Id))
            .Returns(
                Task.FromResult(SaldoEstoqueFake.CreateSaldoEstoqueValido()));

        _saldoEstoqueRepositoryMock.Setup(l =>
                l.Add(It.IsAny<Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate.AjusteSaldoEstoque>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _saldoEstoqueRepositoryMock
            .Verify(
                l => l.Add(It.Is<Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate.AjusteSaldoEstoque>(m =>
                    m.QuantidadeDoAjuste == cmd.NovoSaldo - SaldoEstoqueFake.CreateSaldoEstoqueValido().Saldo)),
                Times.Once);
    }

    [Fact]
    public async Task CadastrarAjusteSaldoEstoque_ExecutadoComErro_LocalEstoqueGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarAjusteSaldoEstoqueCommandFake.CriarCommandValido();

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult<Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _saldoEstoqueRepositoryMock
            .Verify(
                l => l.Add(It.Is<Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate.AjusteSaldoEstoque>(m =>
                    m.UnidadeMedidaId == cmd.UnidadeMedidaId)),
                Times.Never);
    }

    [Fact]
    public async Task CadastrarAjusteSaldoEstoque_ExecutadoComErro_LoteGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarAjusteSaldoEstoqueCommandFake.CriarCommandValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));

        _loteRepositoryMock.Setup(l => l.ObterLotePorIdAsync(cmd.LoteId)).Returns(
            Task.FromResult<Lote>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _saldoEstoqueRepositoryMock
            .Verify(
                l => l.Add(It.Is<Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate.AjusteSaldoEstoque>(m =>
                    m.UnidadeMedidaId == cmd.UnidadeMedidaId)),
                Times.Never);
    }

    [Fact]
    public async Task CadastrarAjusteSaldoEstoque_ExecutadoComErro_UsuarioIdInvalido()
    {
        //Arrange
        var cmd = CadastrarAjusteSaldoEstoqueCommandFake.CriarCommandValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var loteId = 1.ToGuid();

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));


        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _saldoEstoqueRepositoryMock
            .Verify(
                l => l.Add(It.Is<Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate.AjusteSaldoEstoque>(m =>
                    m.UnidadeMedidaId == cmd.UnidadeMedidaId)),
                Times.Never);
    }

    [Fact]
    public async Task CadastrarAjusteSaldoEstoque_ExecutadoComErro_SaldoEstoqueIdNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarAjusteSaldoEstoqueCommandFake.CriarCommandValido();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var loteId = 1.ToGuid();

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));

        _saldoEstoqueRepositoryMock.Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(loteId, localEstoque.Id))
            .Returns(Task.FromResult<SaldoEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _saldoEstoqueRepositoryMock
            .Verify(
                l => l.Add(It.Is<Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate.AjusteSaldoEstoque>(m =>
                    m.UnidadeMedidaId == cmd.UnidadeMedidaId)),
                Times.Never);
    }
}
