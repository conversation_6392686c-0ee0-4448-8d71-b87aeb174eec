using Bootis.Catalogo.Application.UseCases.EmbalagemClassificacao;
using Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.UnitTests.Fixtures.EmbalagemClassificacao;
using Bootis.Estoque.UnitTests.Fixtures.Produto;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Pharma.UnitTests.Common.Fakes.ModeloRotulo;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;

namespace Bootis.Producao.UnitTests.Application.EmbalagemClassificacao;

public class RemoverProdutosVinculadosCommandHandlerTests : BaseTest
{
    private readonly Mock<IEmbalagemClassificacaoRepository> _embalagemClassificacaoRepositoryMock;
    private readonly Mock<IProdutoRepository> _produtoRepositoryMock;
    private readonly RemoverProdutosVinculadosRequestHandler _removerEmbalagemClassificacaoCommandHandler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public RemoverProdutosVinculadosCommandHandlerTests()
    {
        _embalagemClassificacaoRepositoryMock = new Mock<IEmbalagemClassificacaoRepository>();
        _produtoRepositoryMock = new Mock<IProdutoRepository>();

        unitOfWork.Setup(l => l.GetRepository<IEmbalagemClassificacaoRepository>())
            .Returns(_embalagemClassificacaoRepositoryMock.Object);
        unitOfWork.Setup(l => l.GetRepository<IProdutoRepository>())
            .Returns(_produtoRepositoryMock.Object);

        _removerEmbalagemClassificacaoCommandHandler = new RemoverProdutosVinculadosRequestHandler(unitOfWork.Object);
    }

    [Fact]
    public async Task RemoverProdutosVinculados_ExecutadoComSucesso()
    {
        //Arrange
        var embalagemClassificacao = EmbalagemClassificacaoFake.CreateEmbalagemClassificacao_Ativo();
        var cmd = RemoverProdutosVinculadosCommandFake.CreateRemoverProdutosVinculadosCommandValido();
        var cmdInterno = CadastrarEmbalagemCommandFake.CreateCommandValido();
        var produto = ProdutoFake.CreateProdutoFake_Embalagem();
        var produtosEmbalagem = new List<ProdutoEmbalagem>
            { ProdutoEmbalagemFake.CreateProdutoEmbalagemValido(produto) };
        var produtos = new List<Produto> { produto };
        var capsulasTamanho = new List<CapsulaTamanho> { CapsulaTamanhoFake.CreateCapsulaTamanho() };
        var modelosRotulos = new List<ModeloRotulo>(){ ModeloRotuloFake.CreateModeloRotulo() };
        var formasFarmaceuticas = new List<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>() { FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo() };

        cmd.ProdutosIds.Add(produto.Id);
        produto.AtualizarProdutoEmbalagem(embalagemClassificacao, 1, cmdInterno.EmbalagemAssociacao, produtosEmbalagem,
            cmdInterno.NumeroCapsulaAssociacao, cmdInterno.ModeloRotuloAssociacao, capsulasTamanho, formasFarmaceuticas, modelosRotulos);

        _embalagemClassificacaoRepositoryMock
            .Setup(l => l.ObterPorIdAsync(cmd.ClassificacaoEmbalagemId)).Returns(
                Task.FromResult(embalagemClassificacao));

        _produtoRepositoryMock.Setup(l => l.ObterProdutosEmbalagemPorIdsAsync(It.IsAny<IEnumerable<Guid>>()))
            .Returns(
                Task.FromResult(produtos));

        _embalagemClassificacaoRepositoryMock
            .Setup(l => l.ValidarVinculoComProdutoEmbalagemAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(true));

        _embalagemClassificacaoRepositoryMock.Setup(l => l.Remove(embalagemClassificacao)).Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _removerEmbalagemClassificacaoCommandHandler.Handle(cmd, default);

        //Assert
    }

    [Fact]
    public async Task RemoverProdutosVinculados_ExecutadoComErro_EmbalagemClassificacaoGuidNaoEncontrado()
    {
        //Arrange
        var cmd = RemoverProdutosVinculadosCommandFake.CreateRemoverProdutosVinculadosCommandValido();

        _embalagemClassificacaoRepositoryMock
            .Setup(l => l.ObterPorIdAsync(cmd.ClassificacaoEmbalagemId)).Returns(
                Task.FromResult<Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate.EmbalagemClassificacao>(
                    default));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _removerEmbalagemClassificacaoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
    }

    [Fact]
    public async Task RemoverProdutosVinculados_ExecutadoComErro_ProdutoGuidNaoEncontrado()
    {
        //Arrange
        var embalagemClassificacao = EmbalagemClassificacaoFake.CreateEmbalagemClassificacao_Ativo();
        var cmd = RemoverProdutosVinculadosCommandFake.CreateRemoverProdutosVinculadosCommandValido();
        var produtos = new List<Produto>();
        cmd.ProdutosIds.Add(Uuid.NewSequential());

        _embalagemClassificacaoRepositoryMock
            .Setup(l => l.ObterPorIdAsync(cmd.ClassificacaoEmbalagemId)).Returns(
                Task.FromResult(embalagemClassificacao));

        _produtoRepositoryMock.Setup(l => l.ObterProdutosEmbalagemPorIdsAsync(It.IsAny<IEnumerable<Guid>>()))
            .Returns(
                Task.FromResult(produtos));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _removerEmbalagemClassificacaoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
    }

    [Fact]
    public async Task RemoverProdutosVinculados_ExecutadoComErro_VinculoInexistente()
    {
        //Arrange
        var embalagemClassificacao = EmbalagemClassificacaoFake.CreateEmbalagemClassificacao_Ativo();
        var cmd = RemoverProdutosVinculadosCommandFake.CreateRemoverProdutosVinculadosCommandValido();
        var cmdInterno = CadastrarEmbalagemCommandFake.CreateCommandValido();
        var produto = ProdutoFake.CreateProdutoFake_Embalagem();
        var produtosEmbalagem = new List<ProdutoEmbalagem>
            { ProdutoEmbalagemFake.CreateProdutoEmbalagemValido(produto) };
        var produtos = new List<Produto> { produto };
        var capsulasTamanho = new List<CapsulaTamanho> { CapsulaTamanhoFake.CreateCapsulaTamanho() };
        var modelosRotulos = new List<ModeloRotulo>() { ModeloRotuloFake.CreateModeloRotulo() };
        var formasFarmaceuticas = new List<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>() { FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo() };

        cmd.ProdutosIds.Add(produto.Id);
        produto.AtualizarProdutoEmbalagem(embalagemClassificacao, 1, cmdInterno.EmbalagemAssociacao, produtosEmbalagem,
            cmdInterno.NumeroCapsulaAssociacao, cmdInterno.ModeloRotuloAssociacao, capsulasTamanho, formasFarmaceuticas, modelosRotulos);

        _embalagemClassificacaoRepositoryMock
            .Setup(l => l.ObterPorIdAsync(cmd.ClassificacaoEmbalagemId)).Returns(
                Task.FromResult(embalagemClassificacao));

        _produtoRepositoryMock.Setup(l => l.ObterProdutosEmbalagemPorIdsAsync(It.IsAny<IEnumerable<Guid>>()))
            .Returns(
                Task.FromResult(produtos));

        _embalagemClassificacaoRepositoryMock
            .Setup(l => l.ValidarVinculoComProdutoEmbalagemAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(false));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _removerEmbalagemClassificacaoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
    }
}
