using Bootis.Estoque.Application.UseCases.Perda;
using Bootis.Estoque.Domain.AggregatesModel.MotivoPerdaAggregate;
using Bootis.Estoque.UnitTests.Fixtures.Perda;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.UnitTests;
using Moq;
using DomainAggregate = Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;

namespace Bootis.Estoque.UnitTests.Application.Perda;

public class AtualizarLancamentoPerdaCommandHandlerTest : BaseTest
{
    private readonly AtualizarRequestHandler _handler;
    private readonly Mock<DomainAggregate.IPerdaRepository> _lancamentoPerdaRepositoryMock;

    private readonly Mock<IMotivoPerdaRepository> _motivoPerdaRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarLancamentoPerdaCommandHandlerTest()
    {
        _lancamentoPerdaRepositoryMock = new Mock<DomainAggregate.IPerdaRepository>();
        _motivoPerdaRepositoryMock = new Mock<IMotivoPerdaRepository>();
        _handler = new AtualizarRequestHandler(unitOfWork.Object, _lancamentoPerdaRepositoryMock.Object,
            _motivoPerdaRepositoryMock.Object);
    }

    [Fact]
    public async Task AtualizarLancamentoPerdaCommand_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = AtualizarLancamentoPerdaCommandFake.CreateCommandValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var usuario = UsuarioFakes.GerarUsuarioValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(),
            LoteFake.CreateLoteValido().Id, ProdutoFake.CreateProdutoMateriaPrimaFake().Id, localEstoque.Id, 10,
            UnidadeMedidaAbreviacao.UI, MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o", usuario.Id, 1.ToGuid());

        _motivoPerdaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.MotivoPerdaId)).Returns(
            Task.FromResult(MotivoPerdaFake.CreateMotivoPerdaValido()));

        _lancamentoPerdaRepositoryMock.Setup(l => l.ObterPorId(cmd.PerdaId)).Returns(
            Task.FromResult(model));

        _lancamentoPerdaRepositoryMock.Setup(l => l.Update(model))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _lancamentoPerdaRepositoryMock.Verify(
            l => l.Update(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarLancamentoPerdaCommand_ExecutadoComErro_LancamentoPerdaGuidNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarLancamentoPerdaCommandFake.CreateCommandValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(), LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            localEstoque.Id, 10, UnidadeMedidaAbreviacao.UI,
            MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o", UsuarioFakes.GerarUsuarioValido().Id, 1.ToGuid());

        _lancamentoPerdaRepositoryMock.Setup(l => l.ObterPorId(cmd.PerdaId)).Returns(
            Task.FromResult<DomainAggregate.Perda>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _lancamentoPerdaRepositoryMock.Verify(
            l => l.Update(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)), Times.Never);
    }

    [Fact]
    public async Task AtualizarLancamentoPerdaCommand_ExecutadoComErro_MotivoPerdaGuidNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarLancamentoPerdaCommandFake.CreateCommandValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(), LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            localEstoque.Id, 10, UnidadeMedidaAbreviacao.UI,
            MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o", UsuarioFakes.GerarUsuarioValido().Id, 1.ToGuid());

        _lancamentoPerdaRepositoryMock.Setup(l => l.ObterPorId(cmd.PerdaId)).Returns(
            Task.FromResult(model));

        _motivoPerdaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.MotivoPerdaId)).Returns(
            Task.FromResult<MotivoPerda>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _lancamentoPerdaRepositoryMock.Verify(
            l => l.Update(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)), Times.Never);
    }
}
