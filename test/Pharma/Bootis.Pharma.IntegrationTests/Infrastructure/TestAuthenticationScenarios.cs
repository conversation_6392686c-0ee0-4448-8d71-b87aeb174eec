namespace Bootis.Pharma.IntegrationTests.Infrastructure;

public static class TestAuthenticationScenarios
{
    public const string Force401Token = "FORCE_401";

    public const string Force403Token = "FORCE_403";

    public const string UserWithoutPermissions = "test-user-no-permissions";

    public const string TestTenantId = "11111111-1111-1111-1111-111111111111";
    public const string TestGroupTenantId = "22222222-2222-2222-2222-222222222222";
    public const string TestUserId = "33333333-3333-3333-3333-333333333333";

    public static string GetValidAdminToken(JwtTokenGenerator tokenGenerator)
    {
        return tokenGenerator.GenerateToken(
            TestUserId,
            TestTenantId,
            "Admin", "User"
        );
    }

    public static string GetValidUserToken(JwtTokenGenerator tokenGenerator)
    {
        return tokenGenerator.GenerateToken(
            <PERSON>UserId,
            TestTenantId,
            "User"
        );
    }

    public static string GetTokenWithoutRoles(JwtTokenGenerator tokenGenerator)
    {
        return tokenGenerator.GenerateToken(
            UserWithoutPermissions,
            TestTenantId
        );
    }
}