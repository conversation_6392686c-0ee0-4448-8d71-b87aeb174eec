<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
        <LangVersion>default</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Bogus" Version="35.6.3"/>
        <PackageReference Include="coverlet.collector" Version="6.0.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="coverlet.msbuild" Version="6.0.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="FluentAssertions" Version="8.6.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.9"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0"/>
        <PackageReference Include="Moq" Version="4.20.72"/>
        <PackageReference Include="Moq.AutoMock" Version="3.5.0"/>
        <PackageReference Include="OneOf" Version="3.0.271"/>
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\src\Identity\Bootis.Identity.Domain\Bootis.Identity.Domain.csproj"/>
        <ProjectReference Include="..\..\..\src\Identity\Bootis.Identity.Application\Bootis.Identity.Application.csproj"/>
        <ProjectReference Include="..\..\..\src\Identity\Bootis.Identity.Contracts\Bootis.Identity.Contracts.csproj"/>
        <ProjectReference Include="..\..\Shared\Bootis.Shared.UnitTests\Bootis.Shared.UnitTests.csproj" />
    </ItemGroup>

</Project>