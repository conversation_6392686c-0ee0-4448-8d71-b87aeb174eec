<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{2f2875d1-1b5a-430f-b735-6dfd87304756}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Bogus" Version="35.6.3"/>
        <PackageReference Include="coverlet.collector" Version="6.0.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.9"/>
        <PackageReference Include="Microsoft.Extensions.Localization.Abstractions" Version="9.0.9"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1"/>
        <PackageReference Include="Moq" Version="4.20.72"/>
        <PackageReference Include="Moq.AutoMock" Version="3.5.0"/>
        <PackageReference Include="xunit" Version="2.9.3"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\src\Shared\Bootis.Shared.Api\Bootis.Shared.Api.csproj"/>
        <ProjectReference Include="..\..\..\src\Shared\Bootis.Shared.Infrastructure\Bootis.Shared.Infrastructure.csproj"/>
        <ProjectReference Include="..\..\..\src\Shared\Bootis.Shared.Domain\Bootis.Shared.Domain.csproj"/>
        <ProjectReference Include="..\..\..\src\Shared\Bootis.Shared.Application\Bootis.Shared.Application.csproj"/>
    </ItemGroup>

</Project>

