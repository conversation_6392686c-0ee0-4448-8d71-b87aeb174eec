using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bootis.Setup.Api.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveCapsulaTamanhoAndAjusteFornecedor : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "capsulas_tamanho");

            migrationBuilder.Sql(@"
                ALTER TABLE status_atendimento
                ALTER COLUMN acao_venda_secundaria_id TYPE integer
                USING (CASE WHEN acao_venda_secundaria_id IS NULL THEN NULL ELSE 0 END);");

            migrationBuilder.Sql(@"
                ALTER TABLE status_atendimento
                ALTER COLUMN acao_venda_primaria_id TYPE integer
                USING (CASE WHEN acao_venda_primaria_id IS NULL THEN NULL ELSE 0 END);");

            migrationBuilder.Sql(@"
                ALTER TABLE produtos_tipo_capsula
                ALTER COLUMN tipo_capsula_id TYPE uuid
                USING (gen_random_uuid());");

            migrationBuilder.Sql(@"
                ALTER TABLE produtos_tipo_capsula
                ALTER COLUMN produto_id TYPE uuid
                USING (gen_random_uuid());");

            migrationBuilder.Sql(@"
                ALTER TABLE produtos_tipo_capsula
                ALTER COLUMN numero_capsula_id TYPE uuid
                USING (gen_random_uuid());");

            migrationBuilder.Sql(@"
                ALTER TABLE produtos_tipo_capsula
                ALTER COLUMN capsula_cor_id TYPE uuid
                USING (gen_random_uuid());");

            migrationBuilder.Sql(@"
                ALTER TABLE produtos_materia_prima
                ALTER COLUMN unidade_prescricao_id TYPE integer
                USING (CASE WHEN unidade_prescricao_id IS NULL THEN NULL ELSE 1 END);");

            migrationBuilder.Sql(@"
                ALTER TABLE produtos_materia_prima
                ALTER COLUMN tipo_componente_id TYPE integer
                USING (CASE WHEN tipo_componente_id IS NULL THEN NULL ELSE 1 END);");

            migrationBuilder.AddColumn<string>(
                name: "observacao_rotulo_armazenagem",
                table: "produtos_materia_prima",
                type: "text",
                nullable: true);

            migrationBuilder.Sql(@"
                ALTER TABLE produtos
                ALTER COLUMN unidade_estoque_id TYPE integer
                USING (CASE WHEN unidade_estoque_id IS NULL THEN NULL ELSE 1 END);");

            migrationBuilder.Sql(@"
                ALTER TABLE produtos
                ALTER COLUMN classe_produto_id TYPE integer
                USING (CASE WHEN classe_produto_id IS NULL THEN NULL ELSE 1 END);");

            migrationBuilder.Sql(@"
                ALTER TABLE posologias
                ALTER COLUMN unidade_medida_id TYPE integer
                USING (CASE WHEN unidade_medida_id IS NULL THEN NULL ELSE 1 END);");

            migrationBuilder.Sql(@"
                ALTER TABLE fornecedores
                ALTER COLUMN tipo_fornecedor_id TYPE integer
                USING (CASE WHEN tipo_fornecedor_id IS NULL THEN NULL ELSE 1 END);");

            migrationBuilder.Sql(@"
                ALTER TABLE formas_farmaceutica
                ALTER COLUMN unidade_medida_id TYPE integer
                USING (CASE WHEN unidade_medida_id IS NULL THEN NULL ELSE 1 END);");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "observacao_rotulo_armazenagem",
                table: "produtos_materia_prima");

            migrationBuilder.AlterColumn<Guid>(
                name: "acao_venda_secundaria_id",
                table: "status_atendimento",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "acao_venda_primaria_id",
                table: "status_atendimento",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "tipo_capsula_id",
                table: "produtos_tipo_capsula",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "produto_id",
                table: "produtos_tipo_capsula",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "numero_capsula_id",
                table: "produtos_tipo_capsula",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "capsula_cor_id",
                table: "produtos_tipo_capsula",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<Guid>(
                name: "unidade_prescricao_id",
                table: "produtos_materia_prima",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "tipo_componente_id",
                table: "produtos_materia_prima",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "unidade_estoque_id",
                table: "produtos",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "classe_produto_id",
                table: "produtos",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "unidade_medida_id",
                table: "posologias",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "tipo_fornecedor_id",
                table: "fornecedores",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "unidade_medida_id",
                table: "formas_farmaceutica",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.CreateTable(
                name: "capsulas_tamanho",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    numero_capsula = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    volume_ml = table.Column<decimal>(type: "numeric", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_capsulas_tamanho", x => x.id);
                });
        }
    }
}
