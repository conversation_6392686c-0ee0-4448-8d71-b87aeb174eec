using Bootis.Setup.Api.Infrastructure;
using Bootis.Setup.Api.Saga;
using Bootis.Setup.Api.StateMachines;
using MassTransit;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using UUIDNext;

namespace Bootis.Setup.Api.Controllers;

[ApiController]
public class SetupController : ControllerBase
{
    [HttpPost]
    [ProducesResponseType<string>(StatusCodes.Status200OK)]
    [Route("start/{groupTenantId}/{tenantId}")]
    public async Task<IActionResult> StartAsync(
        [FromRoute] Guid groupTenantId,
        [FromRoute] Guid tenantId,
        [FromBody] string dataBaseName,
        [FromServices] IPublishEndpoint publishEndpoint,
        [FromServices] IConfiguration configuration)
    {
        var destinationConnectionString = configuration.GetConnectionString("DestinationConnectionString")!;
        var correlationId = Uuid.NewSequential();
        var sourceConnectionString = string.IsNullOrWhiteSpace(dataBaseName)
            ? null
            : ReplaceDatabaseName(destinationConnectionString, dataBaseName);

        await using var conn = new NpgsqlConnection(destinationConnectionString);
        await conn.OpenAsync();

        await using var command = new NpgsqlCommand($"SELECT id FROM empresas WHERE id = '{tenantId}'", conn);

        var companyId = await command.ExecuteScalarAsync() ?? throw new Exception();

        await publishEndpoint.Publish(
            new StartMigration(correlationId, sourceConnectionString, groupTenantId, tenantId, (Guid)companyId));
        return Ok(correlationId);
    }

    [HttpGet]
    [ProducesResponseType<DataMigrationState>(StatusCodes.Status200OK)]
    [ProducesResponseType<string>(StatusCodes.Status404NotFound)]
    [Route("status/{correlationId}")]
    public async Task<IActionResult> GetStatusAsync(
        [FromRoute] Guid correlationId,
        [FromServices] SetupContext dbContext)
    {
        var migrationState = await dbContext.Set<DataMigrationState>()
            .FirstOrDefaultAsync(x => x.CorrelationId == correlationId);

        if (migrationState == null)
            return NotFound($"DataMigrationState with CorrelationId {correlationId} not found.");

        return Ok(migrationState);
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType<string>(StatusCodes.Status400BadRequest)]
    [Route("retry/{correlationId}")]
    public async Task<IActionResult> RetryAsync(
        [FromRoute] Guid correlationId,
        [FromServices] SetupContext dbContext,
        [FromServices] IPublishEndpoint publishEndpoint)
    {
        var migrationState = await dbContext.Set<DataMigrationState>()
            .FirstOrDefaultAsync(x => x.CorrelationId == correlationId);

        if (migrationState == null)
            return BadRequest($"DataMigrationState with CorrelationId {correlationId} not found.");

        if (migrationState.CurrentState != "Failed")
            return BadRequest($"DataMigrationState with CorrelationId {correlationId} invalid state.");

        var eventType = Type.GetType(migrationState.FailedStep);

        if (eventType == null)
            return BadRequest($"Type {migrationState.FailedStep} not found.");

        var nextEvent = Activator.CreateInstance(eventType,
            new { migrationState.CorrelationId, migrationState.SourceConnectionString });

        await publishEndpoint.Publish(nextEvent!);
        return Ok();
    }

    private string ReplaceDatabaseName(string connectionString, string databaseName)
    {
        var builder = new NpgsqlConnectionStringBuilder(connectionString)
        {
            Database = databaseName
        };
        return builder.ConnectionString;
    }
}