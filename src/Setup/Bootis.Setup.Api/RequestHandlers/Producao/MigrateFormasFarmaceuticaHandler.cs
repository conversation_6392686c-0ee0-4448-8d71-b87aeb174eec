using Bootis.Setup.Api.Models.Producao;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Producao;

public record MigrateFormasFarmaceutica(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateFormasFarmaceuticaHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateFormasFarmaceutica, FormaFarmaceutica>(configuration)
{
    private static readonly FormaFarmaceutica[] FormasFarmaceutica =
    [
        new()
        {
            Descricao = "Cápsula", LaboratorioDescricao = "Sólidos", Ativo = true, Ordem = 1,
            PercentualMinimoExcipiente = 40m,
            TipoCalculoDescricao = "Receita", UsoFormaFarmaceuticaDescricao = "Interno", UnidadeMedidaDescricao = "UN",
            Apresentacao = "CAP", ValidadeDias = 180, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Creme", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 2,
            PercentualMinimoExcipiente = 0m,
            TipoCalculoDescricao = "QSP", UsoFormaFarmaceuticaDescricao = "Topico", UnidadeMedidaDescricao = "GRAMA",
            Apresentacao = "CREM", ValidadeDias = 90, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Pomada", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 3,
            PercentualMinimoExcipiente = 0m,
            TipoCalculoDescricao = "QSP", UsoFormaFarmaceuticaDescricao = "Topico", UnidadeMedidaDescricao = "GRAMA",
            Apresentacao = "POM", ValidadeDias = 90, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Gel", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 4,
            PercentualMinimoExcipiente = 0m,
            TipoCalculoDescricao = "QSP", UsoFormaFarmaceuticaDescricao = "Topico", UnidadeMedidaDescricao = "GRAMA",
            Apresentacao = "GEL", ValidadeDias = 90, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Xampu", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 5,
            PercentualMinimoExcipiente = 0m,
            TipoCalculoDescricao = "QSP", UsoFormaFarmaceuticaDescricao = "Topico",
            UnidadeMedidaDescricao = "MILILITRO", Apresentacao = "XAMP", ValidadeDias = 90, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Xarope", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 6,
            PercentualMinimoExcipiente = 0m,
            TipoCalculoDescricao = "QSP", UsoFormaFarmaceuticaDescricao = "Interno",
            UnidadeMedidaDescricao = "MILILITRO", Apresentacao = "XPE", ValidadeDias = 180, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Óvulo", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 7,
            PercentualMinimoExcipiente = 30m,
            TipoCalculoDescricao = "Receita", UsoFormaFarmaceuticaDescricao = "Interno", UnidadeMedidaDescricao = "UN",
            Apresentacao = "OVL", ValidadeDias = 90, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Supositório", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 8,
            PercentualMinimoExcipiente = 30m, TipoCalculoDescricao = "Receita",
            UsoFormaFarmaceuticaDescricao = "Interno",
            UnidadeMedidaDescricao = "UN", Apresentacao = "SUP", ValidadeDias = 90, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Sabonete Líquido", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 9,
            PercentualMinimoExcipiente = 0m, TipoCalculoDescricao = "QSP", UsoFormaFarmaceuticaDescricao = "Topico",
            UnidadeMedidaDescricao = "MILILITRO", Apresentacao = "SAB L", ValidadeDias = 180, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Solução", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 10,
            PercentualMinimoExcipiente = 0m, TipoCalculoDescricao = "QSP", UsoFormaFarmaceuticaDescricao = "Interno",
            UnidadeMedidaDescricao = "MILILITRO", Apresentacao = "SOL", ValidadeDias = 90, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Suspensão", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 10,
            PercentualMinimoExcipiente = 0m, TipoCalculoDescricao = "QSP", UsoFormaFarmaceuticaDescricao = "Interno",
            UnidadeMedidaDescricao = "MILILITRO", Apresentacao = "SUS", ValidadeDias = 90, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Efervescente", LaboratorioDescricao = "Semi-Sólidos", Ativo = true, Ordem = 10,
            PercentualMinimoExcipiente = 0m, TipoCalculoDescricao = "QSP", UsoFormaFarmaceuticaDescricao = "Interno",
            UnidadeMedidaDescricao = "GRAMA", Apresentacao = "EFEV", ValidadeDias = 180, CustoOperacional = 0
        },
        new()
        {
            Descricao = "Biscoito", LaboratorioDescricao = "Sólidos", Ativo = true, Ordem = 11,
            PercentualMinimoExcipiente = 30m,
            TipoCalculoDescricao = "Receita", UsoFormaFarmaceuticaDescricao = "Interno", UnidadeMedidaDescricao = "UN",
            Apresentacao = "BISC", ValidadeDias = 180, CustoOperacional = 0
        }
    ];

    public override async Task Handle(MigrateFormasFarmaceutica request, CancellationToken cancellationToken)
    {
        var dictLaboratorio = await GetDictionaryFromDatabase(
            "SELECT nome_laboratorio, id FROM laboratorios WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictTipoCalculo = new Dictionary<string, int>
        {
            { "Receita", 1 },
            { "QSP", 2 }
        };

        var dictUsoFormaFarmaceutica = new Dictionary<string, int>
        {
            { "Interno", 1 },
            { "Externo", 2 },
            { "Topico", 3 }
        };

        var dictUnidadeMedida = new Dictionary<string, int>
        {
            { "UI", 1 },
            { "UN", 2 },
            { "MILILITRO", 3 },
            { "LITRO", 4 },
            { "MICROGRAMA", 5 },
            { "MILIGRAMA", 6 },
            { "GRAMA", 7 },
            { "KILO", 8 },
            { "UTR", 9 },
            { "UFC", 10 },
            { "MEQ", 11 },
            { "MIL", 12 },
            { "PER", 13 },
            { "MILHAO", 14 },
            { "BILHAO", 15 }
        };

        var dataTable = GetData(request, dictLaboratorio, dictTipoCalculo, dictUsoFormaFarmaceutica, dictUnidadeMedida,
            cancellationToken);

        await InsertOrUpdateData(dataTable, cancellationToken);
    }

    private IAsyncEnumerable<FormaFarmaceutica> GetData(MigrateFormasFarmaceutica request,
        Dictionary<string, Guid> dictLaboratorio,
        Dictionary<string, int> dictTipoCalculo, Dictionary<string, int> dictUsoFormaFarmaceutica,
        Dictionary<string, int> dictUnidadeMedida,
        CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request, dictLaboratorio, dictTipoCalculo, dictUsoFormaFarmaceutica, dictUnidadeMedida)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM formas_farmaceutica",
                cancellationToken,
                formaFarmaceutica =>
                {
                    formaFarmaceutica.LaboratorioId = dictLaboratorio[formaFarmaceutica.LaboratorioDescricao];
                    formaFarmaceutica.TipoCalculo = dictTipoCalculo[formaFarmaceutica.TipoCalculoDescricao];
                    formaFarmaceutica.UsoFormaFarmaceutica =
                        dictUsoFormaFarmaceutica[formaFarmaceutica.UsoFormaFarmaceuticaDescricao];
                    formaFarmaceutica.UnidadeMedidaId = dictUnidadeMedida[formaFarmaceutica.UnidadeMedidaDescricao];
                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<FormaFarmaceutica> GetDataFromDefault(MigrateFormasFarmaceutica request,
        Dictionary<string, Guid> dictLaboratorio,
        Dictionary<string, int> dictTipoCalculo, Dictionary<string, int> dictUsoFormaFarmaceutica,
        Dictionary<string, int> dictUnidadeMedida)
    {
        foreach (var formaFarmaceutica in FormasFarmaceutica)
            yield return formaFarmaceutica with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId,
                LaboratorioId = dictLaboratorio[formaFarmaceutica.LaboratorioDescricao],
                TipoCalculo = dictTipoCalculo[formaFarmaceutica.TipoCalculoDescricao],
                UsoFormaFarmaceutica = dictUsoFormaFarmaceutica[formaFarmaceutica.UsoFormaFarmaceuticaDescricao],
                UnidadeMedidaId = dictUnidadeMedida[formaFarmaceutica.UnidadeMedidaDescricao]
            };

        await Task.CompletedTask;
    }
}