using Bootis.Setup.Api.Models.Producao;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Producao;

public record MigrateLaboratorios(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateLaboratoriosHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateLaboratorios, Laboratorio>(configuration)
{
    private static readonly Laboratorio[] Laboratorio =
    [
        new() { NomeLaboratorio = "Sólidos", LocalEstoqueDescricao = "Laboratório" },
        new() { NomeLaboratorio = "Semi-Sólidos", LocalEstoqueDescricao = "Laboratório" }
    ];

    public override async Task Handle(MigrateLaboratorios request, CancellationToken cancellationToken)
    {
        var dictLocalEstoque = await GetDictionaryFromDatabase(
            "SELECT descricao, id FROM locais_estoque WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dataTable = GetData(request, dictLocalEstoque, cancellationToken);

        await InsertOrUpdateData(dataTable, cancellationToken);
    }

    private IAsyncEnumerable<Laboratorio> GetData(MigrateLaboratorios request,
        Dictionary<string, Guid> dictLocalEstoque,
        CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request, dictLocalEstoque)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM laboratorios", cancellationToken,
                laboratorio =>
                {
                    laboratorio.LocalEstoqueId = dictLocalEstoque[laboratorio.LocalEstoqueDescricao];
                    laboratorio.EmpresaId = request.CompanyId;
                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<Laboratorio> GetDataFromDefault(MigrateLaboratorios request,
        Dictionary<string, Guid> dictLocalEstoque)
    {
        foreach (var laboratorio in Laboratorio)
            yield return laboratorio with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId,
                LocalEstoqueId = dictLocalEstoque[laboratorio.LocalEstoqueDescricao],
                EmpresaId = request.CompanyId
            };

        await Task.CompletedTask;
    }
}