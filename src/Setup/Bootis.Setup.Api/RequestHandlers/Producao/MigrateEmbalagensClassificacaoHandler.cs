using Bootis.Setup.Api.Models.Producao;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Producao;

public record MigrateEmbalagensClassificacao(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateEmbalagensClassificacaoHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateEmbalagensClassificacao, EmbalagemClassificacao>(configuration)
{
    private static readonly EmbalagemClassificacao[] EmbalagemClassificacao =
    [
        new() { Descricao = "Pote Cápsulas" }, new() { Descricao = "Blister" },
        new() { Descricao = "Pote Creme" }, new() { Descricao = "Bisnaga" },
        new() { Descricao = "Frasco" }, new() { Descricao = "Caixa" },
        new() { Descricao = "Vidro Ambar" }, new() { Descricao = "Esmalte" },
        new() { Descricao = "Óvulo" }, new() { Descricao = "Supositório" },
        new() { Descricao = "Sache" }, new() { Descricao = "Flaconete" },
        new() { Descricao = "Bombom" }, new() { Descricao = "Goma" },
        new() { Descricao = "Tablet" }, new() { Descricao = "Biscoito" }
    ];

    public override async Task Handle(MigrateEmbalagensClassificacao request, CancellationToken cancellationToken)
    {
        var data = GetData(request, cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);
    }

    private IAsyncEnumerable<EmbalagemClassificacao> GetData(MigrateEmbalagensClassificacao request,
        CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM embalagens_classificacao",
                cancellationToken);
    }

    private static async IAsyncEnumerable<EmbalagemClassificacao> GetDataFromDefault(
        MigrateEmbalagensClassificacao request)
    {
        foreach (var embalagemClassificacao in EmbalagemClassificacao)
            yield return embalagemClassificacao with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId
            };

        await Task.CompletedTask;
    }
}