using System.Text;
using Bootis.Setup.Api.Models.Producao;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Producao;

public record MigratePosologias(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigratePosologiasHandler(IConfiguration configuration)
    : BaseRequestHandler<MigratePosologias, Posologia>(configuration)
{
    private static readonly List<Posologia> Posologia = [];

    public override async Task Handle(MigratePosologias request, CancellationToken cancellationToken)
    {
        var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Docs", "SeedsCsv",
            "Posologias.csv");

        var lines = File.ReadAllLines(filePath, Encoding.GetEncoding("ISO-8859-1"));

        foreach (var t in lines)
        {
            var values = t.Split(',');

            var posologia = new Posologia
            {
                Id = Uuid.NewSequential(),
                Descricao = values[0],
                FormaFarmaceuticaDescricao = values[1],
                QuantidadeDosePorPeriodo = decimal.Parse(values[2]),
                UnidadeMedidaDescricao = values[3],
                PeriodoDescricao = values[4]
            };

            Posologia.Add(posologia);
        }

        var dictFormaFarmaceutica = await GetDictionaryFromDatabase(
            "SELECT descricao, id FROM formas_farmaceutica WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictUnidadeMedida = new Dictionary<string, int>
        {
            { "UI", 1 },
            { "UN", 2 },
            { "MILILITRO", 3 },
            { "LITRO", 4 },
            { "MICROGRAMA", 5 },
            { "MILIGRAMA", 6 },
            { "GRAMA", 7 },
            { "KILO", 8 },
            { "UTR", 9 },
            { "UFC", 10 },
            { "MEQ", 11 },
            { "MIL", 12 },
            { "PER", 13 },
            { "MILHAO", 14 },
            { "BILHAO", 15 }
        };

        var dictPeriodo = new Dictionary<string, int>
        {
            { "Dia", 1 },
            { "Semana", 2 },
            { "Mes", 3 }
        };

        var dataTable = GetData(request, dictFormaFarmaceutica, dictUnidadeMedida, dictPeriodo, cancellationToken);

        await InsertOrUpdateData(dataTable, cancellationToken);

        await InsertOrUpdateSequenceControlAsync(request, nameof(Posologia), "posologias", cancellationToken);
    }

    private IAsyncEnumerable<Posologia> GetData(MigratePosologias request,
        Dictionary<string, Guid> dictFormaFarmaceutica,
        Dictionary<string, int> dictUnidadeMedida, Dictionary<string, int> dictPeriodo,
        CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request, dictFormaFarmaceutica, dictUnidadeMedida, dictPeriodo)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM posologias", cancellationToken,
                posologia =>
                {
                    posologia.FormaFarmaceuticaId = dictFormaFarmaceutica[posologia.FormaFarmaceuticaDescricao];
                    posologia.UnidadeMedidaId = dictUnidadeMedida[posologia.UnidadeMedidaDescricao];
                    posologia.Periodo = dictPeriodo[posologia.PeriodoDescricao];
                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<Posologia> GetDataFromDefault(MigratePosologias request,
        Dictionary<string, Guid> dictFormaFarmaceutica,
        Dictionary<string, int> dictUnidadeMedida, Dictionary<string, int> dictPeriodo)
    {
        foreach (var posologia in Posologia)
            yield return posologia with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId,
                FormaFarmaceuticaId = dictFormaFarmaceutica[posologia.FormaFarmaceuticaDescricao],
                UnidadeMedidaId = dictUnidadeMedida[posologia.UnidadeMedidaDescricao],
                Periodo = dictPeriodo[posologia.PeriodoDescricao]
            };

        await Task.CompletedTask;
    }
}