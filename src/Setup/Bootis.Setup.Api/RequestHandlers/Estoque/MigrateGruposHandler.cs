using Bootis.Setup.Api.Models.Estoque;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Estoque;

public record MigrateGrupos(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateGruposHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateGrupos, Grupo>(configuration)
{
    private static readonly Grupo[] Grupos =
    [
        new() { Descricao = "Matéria-Prima" },
        new() { Descricao = "Cápsulas" },
        new() { Descricao = "Embalagens" },
        new() { Descricao = "Acabado" },
        new() { Descricao = "Homepatia" },
        new() { Descricao = "Floral" },
        new() { Descricao = "Uso, Consumo e EPI" },
        new() { Descricao = "Serviço" }
    ];

    public override async Task Handle(MigrateGrupos request, CancellationToken cancellationToken)
    {
        var data = GetData(request, cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);
    }

    private IAsyncEnumerable<Grupo> GetData(MigrateGrupos request, CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM produto_grupos",
                cancellationToken);
    }

    private static async IAsyncEnumerable<Grupo> GetDataFromDefault(MigrateGrupos request)
    {
        foreach (var grupo in Grupos)
            yield return grupo with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId
            };

        await Task.CompletedTask;
    }
}