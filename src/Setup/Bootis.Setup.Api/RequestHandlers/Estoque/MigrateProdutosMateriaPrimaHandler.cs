using System.Text;
using Bootis.Setup.Api.Models.Estoque;

namespace Bootis.Setup.Api.RequestHandlers.Estoque;

public record MigrateProdutosMateriaPrima(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateProdutosMateriaPrimaHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateProdutosMateriaPrima, ProdutoMateriaPrima>(configuration)
{
    private static readonly List<ProdutoMateriaPrima> ProdutosMateriaPrima = [];

    public override async Task Handle(MigrateProdutosMateriaPrima request, CancellationToken cancellationToken)
    {
        var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Docs", "SeedsCsv",
            "ProdutosMateriaPrima.csv");

        var lines = File.ReadAllLines(filePath, Encoding.GetEncoding("ISO-8859-1"));

        foreach (var t in lines)
        {
            var values = t.Split(',');

            var produto = new ProdutoMateriaPrima
            {
                ProdutoDescricao = values[0],
                NumeroDcb = string.IsNullOrWhiteSpace(values[1]) ? null : int.Parse(values[1]),
                NumeroCas = values[2],
                UnidadePrescricaoDescricao = values[3],
                TipoComponenteDescricao = values[4],
                SomenteLaboratorio = bool.Parse(values[5]),
                IsPellets = bool.Parse(values[6]),
                IsExcipiente = bool.Parse(values[7]),
                IsQsp = bool.Parse(values[8]),
                SomenteDiluido = bool.Parse(values[9]),
                DiasValidade = string.IsNullOrWhiteSpace(values[10]) ? null : int.Parse(values[10]),
                ExigeCapsulaGastroresistente = bool.Parse(values[11]),
                IsMonodroga = bool.Parse(values[12]),
                ToleranciaPesagemUp = string.IsNullOrWhiteSpace(values[13]) ? null : decimal.Parse(values[13]),
                ToleranciaPesagemDown = string.IsNullOrWhiteSpace(values[14]) ? null : decimal.Parse(values[14]),
                PesoMolecularSal = decimal.Parse(values[15]),
                PesoMolecularBase = decimal.Parse(values[16]),
                Valencia = string.IsNullOrWhiteSpace(values[17]) ? null : int.Parse(values[17]),
                FatorEquivalencia = decimal.Parse(values[15]) / decimal.Parse(values[16])
            };

            ProdutosMateriaPrima.Add(produto);
        }

        var dictProduto = await GetDictionaryFromDatabase(
            "SELECT descricao, id FROM produtos WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId AND classe_produto_id = 1",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictDcbs = await GetDictionaryFromDatabase(
            "SELECT numero_dcb, id FROM dcbs",
            reader => reader.GetInt32(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictCas = await GetDictionaryFromDatabase(
            "SELECT numero_cas, id FROM cas",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictUnidadeMedida = new Dictionary<string, int>
        {
            { "Ui", 1 },
            { "Unidade", 2 },
            { "Mililitro", 3 },
            { "Litro", 4 },
            { "Micrograma", 5 },
            { "Miligrama", 6 },
            { "Grama", 7 },
            { "Kilo", 8 },
            { "Utr", 9 },
            { "Ufc", 10 },
            { "Meq", 11 },
            { "Mil", 12 },
            { "Per", 13 },
            { "Milhao", 14 },
            { "Bilhao", 15 }
        };

        var dictTipoComponente = new Dictionary<string, int>
        {
            { "Normal", 1 },
            { "Qsp", 2 },
            { "Qs", 3 }
        };

        var data = GetData(request, dictProduto, dictDcbs, dictCas, dictUnidadeMedida, dictTipoComponente,
            cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);
    }

    private IAsyncEnumerable<ProdutoMateriaPrima> GetData(MigrateProdutosMateriaPrima request,
        Dictionary<string, Guid> dictProduto, Dictionary<int, Guid> dictDcbs,
        Dictionary<string, Guid> dictCas, Dictionary<string, int> dictUnidadeMedida,
        Dictionary<string, int> dictTipoComponente, CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request, dictProduto, dictDcbs, dictCas, dictUnidadeMedida, dictTipoComponente)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM produtos_materia_prima",
                cancellationToken,
                produtoMateriaPrima =>
                {
                    produtoMateriaPrima.ProdutoId = dictProduto[produtoMateriaPrima.ProdutoDescricao];
                    produtoMateriaPrima.DcbId =
                        dictDcbs.TryGetValue(produtoMateriaPrima.NumeroDcb.GetValueOrDefault(), out var dcbId)
                            ? dcbId
                            : null;
                    produtoMateriaPrima.CasId = dictCas.TryGetValue(produtoMateriaPrima.NumeroCas, out var casId)
                        ? casId
                        : null;
                    produtoMateriaPrima.UnidadePrescricaoId =
                        dictUnidadeMedida[produtoMateriaPrima.UnidadePrescricaoDescricao];
                    produtoMateriaPrima.TipoComponenteId =
                        dictTipoComponente[produtoMateriaPrima.TipoComponenteDescricao];
                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<ProdutoMateriaPrima> GetDataFromDefault(MigrateProdutosMateriaPrima request,
        Dictionary<string, Guid> dictProduto, Dictionary<int, Guid> dictDcbs,
        Dictionary<string, Guid> dictCas, Dictionary<string, int> dictUnidadeMedida,
        Dictionary<string, int> dictTipoComponente)
    {
        foreach (var produtoMateriaPrima in ProdutosMateriaPrima)
            yield return produtoMateriaPrima with
            {
                ProdutoId = dictProduto[produtoMateriaPrima.ProdutoDescricao],
                DcbId = dictDcbs.TryGetValue(produtoMateriaPrima.NumeroDcb.GetValueOrDefault(), out var dcbId)
                    ? dcbId
                    : null,
                CasId = dictCas.TryGetValue(produtoMateriaPrima.NumeroCas, out var casId) ? casId : null,
                UnidadePrescricaoId = dictUnidadeMedida[produtoMateriaPrima.UnidadePrescricaoDescricao],
                TipoComponenteId = dictTipoComponente[produtoMateriaPrima.TipoComponenteDescricao]
            };

        await Task.CompletedTask;
    }
}