using System.Text;
using Bootis.Setup.Api.Models.Estoque;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Estoque;

public record MigrateProdutos(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateProdutosHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateProdutos, Produto>(configuration)
{
    private static readonly List<Produto> Produtos = [];

    public override async Task Handle(MigrateProdutos request, CancellationToken cancellationToken)
    {
        var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Docs", "SeedsCsv",
            "Produtos.csv");

        var lines = File.ReadAllLines(filePath, Encoding.GetEncoding("ISO-8859-1"));

        foreach (var t in lines)
        {
            var values = t.Split(',');

            var produto = new Produto
            {
                Id = Uuid.NewSequential(),
                SequenciaGroupTenant = int.Parse(values[0]),
                Descricao = values[1],
                DescricaoRotulo = values[2],
                UnidadeEstoqueDescricao = values[3],
                FornecedorIdentificacao = values[4],
                SubGrupoDescricao = values[5],
                ControlaLote = bool.Parse(values[6]),
                UsoContinuo = bool.Parse(values[7]),
                Etiqueta = bool.Parse(values[8]),
                ControlaQualidade = bool.Parse(values[9]),
                ClasseProdutoDescricao = values[10]
            };

            Produtos.Add(produto);
        }

        var dictFornecedor = await GetDictionaryFromDatabase(
            "SELECT COALESCE(cnpj, cpf) AS identificacao, id FROM fornecedores WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictSubGrupo = await GetDictionaryFromDatabase(
            "SELECT descricao, id FROM sub_grupos WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictUnidadeMedida = new Dictionary<string, int>
        {
            { "Ui", 1 },
            { "Unidade", 2 },
            { "Mililitro", 3 },
            { "Litro", 4 },
            { "Micrograma", 5 },
            { "Miligrama", 6 },
            { "Grama", 7 },
            { "Kilo", 8 },
            { "Utr", 9 },
            { "Ufc", 10 },
            { "Meq", 11 },
            { "Mil", 12 },
            { "Per", 13 },
            { "Milhao", 14 },
            { "Bilhao", 15 }
        };

        var dictClasseProduto = new Dictionary<string, int>
        {
            { "MateriaPrima", 1 },
            { "Embalagem", 2 },
            { "ProdutoAcabado", 3 },
            { "Servico", 4 },
            { "CapsulaPronta", 5 },
            { "UsoConsumo", 6 },
            { "TipoCapsula", 7 }
        };

        var data = GetData(request, dictFornecedor, dictSubGrupo, dictUnidadeMedida, dictClasseProduto,
            cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);

        await InsertOrUpdateSequenceControlAsync(request, "Produto", "produtos", cancellationToken);
    }

    private IAsyncEnumerable<Produto> GetData(MigrateProdutos request, Dictionary<string, Guid> dictFornecedor,
        Dictionary<string, Guid> dictSubGrupo,
        Dictionary<string, int> dictUnidadeMedida, Dictionary<string, int> dictClasseProduto,
        CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request, dictFornecedor, dictSubGrupo, dictUnidadeMedida, dictClasseProduto)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM produtos", cancellationToken,
                produto =>
                {
                    produto.FornecedorId =
                        dictFornecedor.TryGetValue(produto.FornecedorIdentificacao, out var value) ? value : null;
                    produto.SubGrupoId = dictSubGrupo[produto.SubGrupoDescricao];
                    produto.UnidadeEstoqueId = dictUnidadeMedida[produto.UnidadeEstoqueDescricao];
                    produto.ClasseProdutoId = dictClasseProduto[produto.ClasseProdutoDescricao];
                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<Produto> GetDataFromDefault(MigrateProdutos request,
        Dictionary<string, Guid> dictFornecedor,
        Dictionary<string, Guid> dictSubGrupo, Dictionary<string, int> dictUnidadeMedida,
        Dictionary<string, int> dictClasseProduto)
    {
        foreach (var produto in Produtos)
            yield return produto with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId,
                FornecedorId =
                dictFornecedor.TryGetValue(produto.FornecedorIdentificacao, out var value) ? value : null,
                SubGrupoId = dictSubGrupo[produto.SubGrupoDescricao],
                UnidadeEstoqueId = dictUnidadeMedida[produto.UnidadeEstoqueDescricao],
                ClasseProdutoId = dictClasseProduto[produto.ClasseProdutoDescricao]
            };

        await Task.CompletedTask;
    }
}