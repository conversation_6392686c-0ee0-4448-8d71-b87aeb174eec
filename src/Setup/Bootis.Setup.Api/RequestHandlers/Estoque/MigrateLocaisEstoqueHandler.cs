using Bootis.Setup.Api.Models.Estoque;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Estoque;

public record MigrateLocaisEstoque(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateLocaisEstoqueHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateLocaisEstoque, LocalEstoque>(configuration)
{
    private static readonly LocalEstoque[] LocalEstoque =
    [
        new() { Descricao = "Almoxarifado", TipoEstoque = 1, SequenciaGroupTenant = 2, Ativo = false },
        new() { Descricao = "Laboratório", TipoEstoque = 1, SequenciaGroupTenant = 3, Ativo = true },
        new() { Descricao = "Controle de Qualidade", TipoEstoque = 1, SequenciaGroupTenant = 4, Ativo = false }
    ];

    public override async Task Handle(MigrateLocaisEstoque request, CancellationToken cancellationToken)
    {
        var data = GetData(request, cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);

        await InsertOrUpdateSequenceControlAsync(request, nameof(LocalEstoque), "locais_estoque", cancellationToken);
    }

    private IAsyncEnumerable<LocalEstoque> GetData(MigrateLocaisEstoque request, CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM locais_estoque",
                cancellationToken);
    }

    private static async IAsyncEnumerable<LocalEstoque> GetDataFromDefault(MigrateLocaisEstoque request)
    {
        foreach (var localEstoque in LocalEstoque)
            yield return localEstoque with
            {
                Id = Uuid.NewSequential(),
                EmpresaId = request.CompanyId,
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId
            };

        await Task.CompletedTask;
    }
}