using System.Text;
using Bootis.Setup.Api.Models.Estoque;

namespace Bootis.Setup.Api.RequestHandlers.Estoque;

public record MigrateProdutosEmbalagem(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateProdutosEmbalagemHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateProdutosEmbalagem, ProdutoEmbalagem>(configuration)
{
    private static readonly List<ProdutoEmbalagem> ProdutosEmbalagem = [];

    public override async Task Handle(MigrateProdutosEmbalagem request, CancellationToken cancellationToken)
    {
        var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Docs", "SeedsCsv",
            "ProdutosEmbalagem.csv");

        var lines = File.ReadAllLines(filePath, Encoding.GetEncoding("ISO-8859-1"));

        foreach (var t in lines)
        {
            var values = t.Split(',');
            var produtoEmbalagem = new ProdutoEmbalagem
            {
                ProdutoDescricao = values[0],
                EmbalagemClassificacaoDescricao = values[1],
                Volume = decimal.Parse(values[2])
            };

            ProdutosEmbalagem.Add(produtoEmbalagem);
        }

        var dictProduto = await GetDictionaryFromDatabase(
            "SELECT descricao, id FROM produtos WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId AND classe_produto_id = 2",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictEmbalagemClassificacao = await GetDictionaryFromDatabase(
            "SELECT descricao, id FROM embalagens_classificacao",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var data = GetData(request, dictProduto, dictEmbalagemClassificacao, cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);
    }

    private IAsyncEnumerable<ProdutoEmbalagem> GetData(
        MigrateProdutosEmbalagem request,
        Dictionary<string, Guid> dictProduto,
        Dictionary<string, Guid> dictEmbalagemClassificacao,
        CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetFromDefault(request, dictProduto, dictEmbalagemClassificacao)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM produtos_embalagem",
                cancellationToken,
                produtoEmbalagem =>
                {
                    produtoEmbalagem.ProdutoId = dictProduto[produtoEmbalagem.ProdutoDescricao];
                    produtoEmbalagem.EmbalagemClassificacaoId =
                        dictEmbalagemClassificacao[produtoEmbalagem.EmbalagemClassificacaoDescricao];

                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<ProdutoEmbalagem> GetFromDefault(
        MigrateProdutosEmbalagem request,
        Dictionary<string, Guid> dictProduto,
        Dictionary<string, Guid> dictEmbalagemClassificacao)
    {
        foreach (var produtoEmbalagem in ProdutosEmbalagem)
            yield return produtoEmbalagem with
            {
                ProdutoId = dictProduto[produtoEmbalagem.ProdutoDescricao],
                EmbalagemClassificacaoId = dictEmbalagemClassificacao[produtoEmbalagem.EmbalagemClassificacaoDescricao]
            };

        await Task.CompletedTask;
    }
}