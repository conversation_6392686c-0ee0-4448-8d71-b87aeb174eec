using Bootis.Setup.Api.Models.Estoque;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Estoque;

public record MigrateSubGrupos(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateSubGruposHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateSubGrupos, SubGrupo>(configuration)
{
    private static readonly SubGrupo[] SubGrupos =
    [
        new() { Descricao = "Químicos", GrupoDescricao = "Matéria-Prima" },
        new() { Descricao = "Fitoterápicos", GrupoDescricao = "Matéria-Prima" },
        new() { Descricao = "Veterinário", GrupoDescricao = "Matéria-Prima" },
        new() { Descricao = "Hormonios", GrupoDescricao = "Matéria-Prima" },
        new() { Descricao = "Controlados", GrupoDescricao = "Matéria-Prima" },
        new() { Descricao = "Óleos Extratos e Tinturas", GrupoDescricao = "Matéria-Prima" },
        new() { Descricao = "Excipientes e Veículos", GrupoDescricao = "Matéria-Prima" },
        new() { Descricao = "Matéria-Prima", GrupoDescricao = "Matéria-Prima" },
        new() { Descricao = "Gelatinosas", GrupoDescricao = "Cápsulas" },
        new() { Descricao = "Vegetal", GrupoDescricao = "Cápsulas" },
        new() { Descricao = "Gastroresistentes", GrupoDescricao = "Cápsulas" },
        new() { Descricao = "Embalagens", GrupoDescricao = "Embalagens" },
        new() { Descricao = "Fabricação Própria", GrupoDescricao = "Acabado" },
        new() { Descricao = "Terceiros", GrupoDescricao = "Acabado" },
        new() { Descricao = "Homepatia", GrupoDescricao = "Homepatia" },
        new() { Descricao = "Floral", GrupoDescricao = "Floral" },
        new() { Descricao = "Uso e Consumo", GrupoDescricao = "Uso, Consumo e EPI" },
        new() { Descricao = "EPI", GrupoDescricao = "Uso, Consumo e EPI" },
        new() { Descricao = "Serviço", GrupoDescricao = "Serviço" }
    ];

    public override async Task Handle(MigrateSubGrupos request, CancellationToken cancellationToken)
    {
        var dictGrupo = await GetDictionaryFromDatabase(
            "SELECT descricao, id FROM produto_grupos WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var data = GetData(request, dictGrupo, cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);
    }

    private IAsyncEnumerable<SubGrupo> GetData(MigrateSubGrupos request, Dictionary<string, Guid> dictGrupo,
        CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request, dictGrupo)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM sub_grupos", cancellationToken,
                subgrupo =>
                {
                    subgrupo.GrupoId = dictGrupo[subgrupo.GrupoDescricao];
                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<SubGrupo> GetDataFromDefault(MigrateSubGrupos request,
        Dictionary<string, Guid> dictGrupo)
    {
        foreach (var subGrupo in SubGrupos)
            yield return subGrupo with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId,
                GrupoId = dictGrupo[subGrupo.GrupoDescricao]
            };

        await Task.CompletedTask;
    }
}