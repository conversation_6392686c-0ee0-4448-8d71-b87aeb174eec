using System.Text;
using Bootis.Setup.Api.Models.Pessoa;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Pessoa;

public record MigrateFornecedores(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateFornecedoresHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateFornecedores, Fornecedor>(configuration)
{
    private static readonly List<Fornecedor> Fornecedores = [];

    public override async Task Handle(MigrateFornecedores request, CancellationToken cancellationToken)
    {
        var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Docs", "SeedsCsv",
            "Fornecedores.csv");

        var lines = File.ReadAllLines(filePath, Encoding.GetEncoding("ISO-8859-1"));

        foreach (var t in lines)
        {
            var values = t.Split(',');

            var fornecedor = new Fornecedor
            {
                Nome = values[0],
                TipoPessoa = int.Parse(values[1]),
                Cpf = values[2],
                Cnpj = values[3],
                RazaoSocial = values[4],
                Observacao = values[5],
                TipoFornecedorId = int.Parse(values[6])
            };

            Fornecedores.Add(fornecedor);
        }

        var data = GetData(request, cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);
    }

    private IAsyncEnumerable<Fornecedor> GetData(MigrateFornecedores request, CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM fornecedores",
                cancellationToken);
    }

    private static async IAsyncEnumerable<Fornecedor> GetDataFromDefault(MigrateFornecedores request)
    {
        foreach (var fornecedor in Fornecedores)
            yield return fornecedor with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId
            };

        await Task.CompletedTask;
    }
}