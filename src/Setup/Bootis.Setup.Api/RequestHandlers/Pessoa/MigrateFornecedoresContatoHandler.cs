using System.Text;
using Bootis.Setup.Api.Models.Pessoa;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Pessoa;

public record MigrateFornecedoresContato(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateFornecedoresContatoHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateFornecedoresContato, FornecedorContato>(configuration)
{
    private static readonly List<FornecedorContato> FornecedorContatos = [];

    public override async Task Handle(MigrateFornecedoresContato request, CancellationToken cancellationToken)
    {
        var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Docs", "SeedsCsv",
            "FornecedorContatos.csv");

        var lines = File.ReadAllLines(filePath, Encoding.GetEncoding("ISO-8859-1"));

        foreach (var t in lines)
        {
            var values = t.Split(',');

            var fornecedorContato = new FornecedorContato
            {
                FornecedorIdentificacao = values[0],
                TipoContatoNome = values[1],
                Identificacao = values[2],
                Principal = bool.Parse(values[3]),
                Observacao = values[4]
            };

            FornecedorContatos.Add(fornecedorContato);
        }

        var dictFornecedor = await GetDictionaryFromDatabase(
            "SELECT cnpj, id FROM fornecedores WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictTipoContato = await GetDictionaryFromDatabase(
            "SELECT nome, id FROM tipos_contato",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            null,
            cancellationToken);

        var dataTable = GetData(request, cancellationToken, dictFornecedor, dictTipoContato);

        await InsertOrUpdateData(dataTable, cancellationToken);
    }

    private IAsyncEnumerable<FornecedorContato> GetData(MigrateFornecedoresContato request,
        CancellationToken cancellationToken,
        Dictionary<string, Guid> dictFornecedor, Dictionary<string, Guid> dictTipoContato)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request, dictFornecedor, dictTipoContato)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM fornecedores_contato",
                cancellationToken,
                fornecedorContato =>
                {
                    fornecedorContato.FornecedorId = dictFornecedor[fornecedorContato.FornecedorIdentificacao];
                    fornecedorContato.TipoContatoId = dictTipoContato[fornecedorContato.TipoContatoNome];
                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<FornecedorContato> GetDataFromDefault(MigrateFornecedoresContato request,
        Dictionary<string, Guid> dictFornecedor, Dictionary<string, Guid> dictTipoContato)
    {
        foreach (var fornecedorContato in FornecedorContatos)
            yield return fornecedorContato with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId,
                FornecedorId = dictFornecedor[fornecedorContato.FornecedorIdentificacao],
                TipoContatoId = dictTipoContato[fornecedorContato.TipoContatoNome]
            };

        await Task.CompletedTask;
    }
}