using Bootis.Setup.Api.Models.Pessoa;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Pessoa;

public record MigrateEspecialidadesPrescritor(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateEspecialidadesPrescritorHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateEspecialidadesPrescritor, EspecialidadePrescritor>(configuration)
{
    private static readonly List<EspecialidadePrescritor> EspecialidadePrescritor =
    [
        new() { Descricao = "Acupunturista" }, new() { Descricao = "Alergista e Imunologista" },
        new() { Descricao = "Anestesiologista" },
        new() { Descricao = "Angiologista" }, new() { Descricao = "Cancerologista" },
        new() { Descricao = "Cardiologista" },
        new() { Descricao = "Cirurgião Cardiovascular" }, new() { Descricao = "Cirurgião da Mão" },
        new() { Descricao = "Cirurgião de Cabeça e Pescoço" },
        new() { Descricao = "Cirurgião Geral" }, new() { Descricao = "Coloproctologista" },
        new() { Descricao = "Dermatologista" },
        new() { Descricao = "Endocrinologista" }, new() { Descricao = "Endoscopista" },
        new() { Descricao = "Gastroenterologista" },
        new() { Descricao = "Geneticista" }, new() { Descricao = "Geriatra" },
        new() { Descricao = "Ginecologista e Obstetra" },
        new() { Descricao = "Hematologista" }, new() { Descricao = "Hemoterapista" }, new() { Descricao = "Homeopata" },
        new() { Descricao = "Infectologista" }, new() { Descricao = "Mastologista" },
        new() { Descricao = "Médico de Família e Comunidade" },
        new() { Descricao = "Médico do Trabalho" }, new() { Descricao = "Médico de Tráfego" },
        new() { Descricao = "Médico Esportivo" },
        new() { Descricao = "Médico Físico e de Reabilitação" }, new() { Descricao = "Médico Intensivo" },
        new() { Descricao = "Médico Legal" },
        new() { Descricao = "Médico Nuclear" }, new() { Descricao = "Médico Preventivo e Social" },
        new() { Descricao = "Nefrologista" },
        new() { Descricao = "Neurocirurgião" }, new() { Descricao = "Neurologista" },
        new() { Descricao = "Nutrologista" },
        new() { Descricao = "Odontologista" }, new() { Descricao = "Oftalmologista" },
        new() { Descricao = "Ortopedista e Traumatologista" },
        new() { Descricao = "Otorrinolaringologista" }, new() { Descricao = "Patologista" },
        new() { Descricao = "Patologista Clínico / Médico Laboratorial" },
        new() { Descricao = "Pediatra" }, new() { Descricao = "Pneumologista" }, new() { Descricao = "Psiquiatra" },
        new() { Descricao = "Radiologista e Diagnóstico por Imagem" },
        new() { Descricao = "Radioterapista" }, new() { Descricao = "Reumatologista" },
        new() { Descricao = "Urologista" }
    ];

    public override async Task Handle(MigrateEspecialidadesPrescritor request, CancellationToken cancellationToken)
    {
        var data = GetData(request, cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);
    }

    private IAsyncEnumerable<EspecialidadePrescritor> GetData(MigrateEspecialidadesPrescritor request,
        CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM especialidades_prescritor",
                cancellationToken);
    }

    private static async IAsyncEnumerable<EspecialidadePrescritor> GetDataFromDefault(
        MigrateEspecialidadesPrescritor request)
    {
        foreach (var especialidadePrescritor in EspecialidadePrescritor)
            yield return especialidadePrescritor with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId
            };

        await Task.CompletedTask;
    }
}