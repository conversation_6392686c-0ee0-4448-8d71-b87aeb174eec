using System.Text;
using Bootis.Setup.Api.Models.Pessoa;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Pessoa;

public record MigrateFornecedoresEndereco(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateFornecedoresEnderecoHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateFornecedoresEndereco, FornecedorEndereco>(configuration)
{
    private static readonly List<FornecedorEndereco> FornecedorEnderecos = [];

    public override async Task Handle(MigrateFornecedoresEndereco request, CancellationToken cancellationToken)
    {
        var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Docs", "SeedsCsv",
            "FornecedorEnderecos.csv");

        var lines = File.ReadAllLines(filePath, Encoding.GetEncoding("ISO-8859-1"));

        foreach (var t in lines)
        {
            var values = t.Split(',');

            var fornecedorEndereco = new FornecedorEndereco
            {
                FornecedorIdentificacao = values[0],
                PaisDescricao = values[1],
                EstadoDescricao = values[2],
                CidadeDescricao = values[3],
                Bairro = values[4],
                Cep = values[5],
                Logradouro = values[6],
                Numero = values[7],
                Complemento = values[8],
                Principal = bool.Parse(values[9]),
                Descricao = values[10]
            };

            FornecedorEnderecos.Add(fornecedorEndereco);
        }

        var dictFornecedor = await GetDictionaryFromDatabase(
            "SELECT cnpj, id FROM fornecedores WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictPais = await GetDictionaryFromDatabase(
            "SELECT descricao, id FROM paises",
            reader => reader.GetString(0).Trim(),
            reader => reader.GetGuid(1),
            null,
            cancellationToken
        );

        var dictEstado = await GetDictionaryFromDatabase(
            "SELECT abreviacao, id FROM estados",
            reader => reader.GetString(0).Trim(),
            reader => reader.GetGuid(1),
            null,
            cancellationToken
        );

        var dictCidade = await GetDictionaryFromDatabase(
            @"SELECT c.descricao, e.abreviacao, c.id
              FROM cidades c
              INNER JOIN estados e ON c.estado_id = e.id",
            reader => $"{reader.GetString(0).Trim()}-{reader.GetString(1).Trim()}",
            reader => reader.GetGuid(2),
            null,
            cancellationToken
        );

        var dataTable = GetData(request, cancellationToken, dictFornecedor, dictPais, dictEstado, dictCidade);

        await InsertOrUpdateData(dataTable, cancellationToken);
    }

    private IAsyncEnumerable<FornecedorEndereco> GetData(MigrateFornecedoresEndereco request,
        CancellationToken cancellationToken,
        Dictionary<string, Guid> dictFornecedor, Dictionary<string, Guid> dictPais, Dictionary<string, Guid> dictEstado,
        Dictionary<string, Guid> dictCidade)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request, dictFornecedor, dictPais, dictEstado, dictCidade)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM fornecedores_endereco",
                cancellationToken,
                fornecedorEndereco =>
                {
                    fornecedorEndereco.FornecedorId = dictFornecedor[fornecedorEndereco.FornecedorIdentificacao];
                    fornecedorEndereco.PaisId = dictPais[fornecedorEndereco.PaisDescricao];
                    fornecedorEndereco.EstadoId = dictEstado[fornecedorEndereco.EstadoDescricao];

                    if (!string.IsNullOrWhiteSpace(fornecedorEndereco.CidadeDescricao))
                    {
                        var cidadeKey =
                            $"{fornecedorEndereco.CidadeDescricao.Trim()}-{fornecedorEndereco.EstadoDescricao.Trim()}";

                        if (!dictCidade.TryGetValue(cidadeKey, out var cidadeId))
                            throw new Exception($"Cidade '{cidadeKey}' não encontrada na base de dados.");

                        fornecedorEndereco.CidadeId = cidadeId;

                        return Task.CompletedTask;
                    }

                    fornecedorEndereco.CidadeId = null;

                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<FornecedorEndereco> GetDataFromDefault(MigrateFornecedoresEndereco request,
        Dictionary<string, Guid> dictFornecedor, Dictionary<string, Guid> dictPais, Dictionary<string, Guid> dictEstado,
        Dictionary<string, Guid> dictCidade)
    {
        foreach (var fornecedorEndereco in FornecedorEnderecos)
        {
            Guid? cidadeId = null;

            if (!string.IsNullOrWhiteSpace(fornecedorEndereco.CidadeDescricao))
            {
                var cidadeKey =
                    $"{fornecedorEndereco.CidadeDescricao.Trim()}-{fornecedorEndereco.EstadoDescricao.Trim()}";

                if (!dictCidade.TryGetValue(cidadeKey, out var cidadeIdEncontrada))
                    throw new Exception($"Cidade '{cidadeKey}' não encontrada na base de dados.");

                cidadeId = cidadeIdEncontrada;
            }

            yield return fornecedorEndereco with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId,
                FornecedorId = dictFornecedor[fornecedorEndereco.FornecedorIdentificacao],
                PaisId = dictPais[fornecedorEndereco.PaisDescricao],
                EstadoId = dictEstado[fornecedorEndereco.EstadoDescricao],
                CidadeId = cidadeId
            };
        }

        await Task.CompletedTask;
    }
}