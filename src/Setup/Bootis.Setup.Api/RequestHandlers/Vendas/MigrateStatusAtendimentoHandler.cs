using Bootis.Setup.Api.Models.Vendas;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.Vendas;

public record MigrateStatusAtendimento(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateStatusAtendimentoHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateStatusAtendimento, StatusAtendimento>(configuration)
{
    private static readonly StatusAtendimento[] StatusAtendimento =
    [
        new()
        {
            Descricao = "Aguardando Atendimento", Ordem = 1
        },
        new()
        {
            Descricao = "Em Atendimento", Ordem = 2
        },
        new()
        {
            Descricao = "Orçamento Enviado", Ordem = 3
        },
        new()
        {
            Descricao = "Atendimento Concluído", Ordem = 4
        },
        new()
        {
            Descricao = "Aguardando Receita", Ordem = 5
        },
        new()
        {
            Descricao = "Finalizado", Ordem = 99
        }
    ];

    public override async Task Handle(MigrateStatusAtendimento request, CancellationToken cancellationToken)
    {
        var usuarioIdEnumerable = await GetEntityIdEnumerableFromDataBase(
            "SELECT id FROM usuarios WHERE tipo_id = 1 AND tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetGuid(0),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken);

        var dataTable = GetData(request, usuarioIdEnumerable, cancellationToken);

        await InsertOrUpdateData(dataTable, cancellationToken);
    }

    private IAsyncEnumerable<StatusAtendimento> GetData(MigrateStatusAtendimento request,
        IEnumerable<Guid> usuarioIdEnumerable,
        CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request, usuarioIdEnumerable)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM status_atendimento",
                cancellationToken,
                statusAtendimento =>
                {
                    statusAtendimento.UsuarioId = usuarioIdEnumerable.FirstOrDefault();
                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<StatusAtendimento> GetDataFromDefault(MigrateStatusAtendimento request,
        IEnumerable<Guid> usuarioIdEnumerable)
    {
        foreach (var statusAtendimento in StatusAtendimento)
            yield return statusAtendimento with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId,
                UsuarioId = usuarioIdEnumerable.FirstOrDefault()
            };

        await Task.CompletedTask;
    }
}