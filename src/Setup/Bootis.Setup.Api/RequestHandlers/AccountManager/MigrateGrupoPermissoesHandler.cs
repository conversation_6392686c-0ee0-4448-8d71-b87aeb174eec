using Bootis.Setup.Api.Models.AccountManager;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Extensions;

namespace Bootis.Setup.Api.RequestHandlers.AccountManager;

public record MigrateGrupoPermissoes(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateGrupoPermissoesHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateGrupoPermissoes, GrupoPermissao>(configuration)
{
    public override async Task Handle(MigrateGrupoPermissoes request, CancellationToken cancellationToken)
    {
        var grupoIdsEnumerable = await GetEntityIdEnumerableFromDataBase(
            "SELECT id FROM grupos WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetGuid(0),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken);

        var data = GetData(grupoIdsEnumerable);

        await InsertOrUpdateData(data, cancellationToken);
    }

    private static async IAsyncEnumerable<GrupoPermissao> GetData(IEnumerable<Guid> grupoIdsEnumerable)
    {
        foreach (var grupoId in grupoIdsEnumerable)
        foreach (var permissao in Enum.GetValues<Permissoes>())
            yield return new GrupoPermissao
            {
                GrupoId = grupoId,
                PermissaoId = permissao.ToInt()
            };
        await Task.CompletedTask;
    }
}