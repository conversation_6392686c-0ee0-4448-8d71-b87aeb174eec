using Bootis.Setup.Api.Models.AccountManager;
using UUIDNext;

namespace Bootis.Setup.Api.RequestHandlers.AccountManager;

public record MigrateGrupos(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateGruposHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateGrupos, Grupo>(configuration)
{
    private static readonly Grupo[] Grupos =
    [
        new()
        {
            Nome = "Administrador", Descricao = "Acesso a todas as aplicações e todas a permissões do sistema",
            SeAtivo = true
        },
        new()
        {
            Nome = "Vendas",
            Descricao = "Acesso as aplicações de vendas e cadastro de clientes, pacientes e prescritores",
            SeAtivo = true
        },
        new() { Nome = "Caixa", Descricao = "Processos do caixa: abertura, conferência e fechamento", SeAtivo = true },
        new() { Nome = "Compras", Descricao = "Responsável pelo processo cotação e compras", SeAtivo = true },
        new()
        {
            Nome = "Almoxarifado",
            Descricao = "Processo de entrada de NF´s, conferência de mercadorias, consultas de lotes e transferências",
            SeAtivo = true
        },
        new() { Nome = "Farmacêutico", Descricao = "Processo de controle de qualidade", SeAtivo = true },
        new()
        {
            Nome = "Controle de Qualidade", Descricao = "Processos do caixa: abertura, conferência e fechamento",
            SeAtivo = true
        },
        new()
        {
            Nome = "Financeiro", Descricao = "Atribuições de contas a pagar, receber entre outros do financeiro",
            SeAtivo = true
        }
    ];

    public override async Task Handle(MigrateGrupos request, CancellationToken cancellationToken)
    {
        var data = GetData(request, cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);
    }

    private IAsyncEnumerable<Grupo> GetData(MigrateGrupos request, CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM grupos", cancellationToken);
    }

    private static async IAsyncEnumerable<Grupo> GetDataFromDefault(MigrateGrupos request)
    {
        foreach (var grupo in Grupos)
            yield return grupo with
            {
                Id = Uuid.NewSequential(),
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId
            };

        await Task.CompletedTask;
    }
}