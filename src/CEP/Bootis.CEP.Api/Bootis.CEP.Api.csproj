<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <AssemblyName>Bootis.CEP.Api</AssemblyName>
        <RootNamespace>Bootis.CEP.Api</RootNamespace>
        <ProjectGuid>{9fe863f9-02fc-48db-9a87-7c22e02bcd75}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.9"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Aspire\Bootis.ServiceDefaults\Bootis.ServiceDefaults.csproj"/>
        <ProjectReference Include="..\..\Shared\Bootis.Shared.Api\Bootis.Shared.Api.csproj"/>
        <ProjectReference Include="..\Bootis.CEP.Contracts\Bootis.CEP.Contracts.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.9">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <None Update="Infrastructure\Seeds\Data\*">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>


</Project>

