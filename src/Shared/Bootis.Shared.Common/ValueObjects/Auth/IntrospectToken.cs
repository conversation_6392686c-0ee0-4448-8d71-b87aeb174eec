using Bootis.Shared.Common.Helpers;
using Bootis.Shared.Common.Security;

namespace Bootis.Shared.Common.ValueObjects.Auth;

public enum IntrospectType
{
    AccessToken,
    TemporaryToken
}

public record TokenIntrospectionRequest(string Token);

public class TokenValidationSuccess
{
    public bool IsActive { get; init; } = true;
    public long ExpirationTimestamp { get; set; }
    public required UserSession UserSession { get; init; }
}

public class TokenValidationFailure(AuthenticationError errorCode)
{
    public bool IsActive { get; set; }

    public AuthenticationError ErrorCode { get; set; } = errorCode;

    public string ErrorMessage { get; } = AuthHelper.GetDefaultErrorMessage(errorCode);

    public long Timestamp { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

    public Dictionary<string, object> AdditionalData { get; set; } = new();
}