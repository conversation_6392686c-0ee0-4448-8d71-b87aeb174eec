namespace Bootis.Shared.Common.Security;

public record UserSession
{
    public Guid SessionId { get; init; }
    public string Module { get; init; } = "Bootis";
    public required UserIdentity UserIdentity { get; init; }
    public required UserPreferences UserPreferences { get; init; }

    public Guid UserId => UserIdentity.UserId;
    public Guid TenantId => UserIdentity.TenantId;
    public Guid GroupTenantId => UserIdentity.GroupTenantId;
    public string UserName => UserIdentity.Name;
    public string UserEmail => UserIdentity.Email;
    public bool IsAuthenticated => UserId != Guid.Empty;
}