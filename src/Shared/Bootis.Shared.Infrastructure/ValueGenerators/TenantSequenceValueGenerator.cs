using Bootis.Shared.Domain.SeedWork;
using Bootis.Shared.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.ValueGeneration;

namespace Bootis.Shared.Infrastructure.ValueGenerators;

public class TenantSequenceValueGenerator : ValueGenerator<int>
{
    public override bool GeneratesTemporaryValues => false;

    public override int Next(EntityEntry entry)
    {
        var context = (BootisContext)entry.Context;

        if (!entry.CurrentValues.TryGetValue<Guid>(nameof(IGroupTenant.GroupTenantId), out var groupTenantId))
            return -1;

        if (!entry.CurrentValues.TryGetValue<Guid>(nameof(ITenant.TenantId), out var tenantId))
            return -1;
        
        if (groupTenantId == Guid.Empty)
            groupTenantId = context.GroupTenantId;
        
        if (tenantId == Guid.Empty)
            tenantId = context.TenantId;

        return context.GetNextSequence(entry.Entity.GetType().Name, groupTenantId, tenantId);
    }
}