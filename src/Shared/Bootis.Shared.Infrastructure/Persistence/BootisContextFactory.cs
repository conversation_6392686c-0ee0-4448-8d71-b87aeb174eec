using System.Diagnostics;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace Bootis.Shared.Infrastructure.Persistence;

public class BootisContextFactory : IDesignTimeDbContextFactory<BootisContext>
{
    public BootisContext CreateDbContext(string[] args)
    {
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development"}.json", optional: true)
            .AddUserSecrets(Assembly.GetExecutingAssembly(), optional: true)
            .AddEnvironmentVariables()
            .Build();

        var connectionString = configuration.GetConnectionString("PharmaDBConnection") ??
                              configuration.GetConnectionString("DefaultConnection");

        if (string.IsNullOrEmpty(connectionString))
        {
            var connectionStrings = configuration.GetSection("ConnectionStrings").GetChildren();
            var firstConnection = connectionStrings.FirstOrDefault();
            connectionString = firstConnection?.Value ??
                               "Host=localhost;Database=bootis;Username=********;Password=********";
        }

        var migrationsAssembly = GetMigrationsAssembly();

        var optionsBuilder = new DbContextOptionsBuilder<BootisContext>();
        optionsBuilder.UseNpgsql(connectionString, npgsql =>
        {
            npgsql.MigrationsAssembly(migrationsAssembly);
            npgsql.EnableRetryOnFailure();
        }).UseSnakeCaseNamingConvention();

        return new BootisContext(optionsBuilder.Options);
    }

    private static string GetMigrationsAssembly()
    {
        var envAssembly = Environment.GetEnvironmentVariable("EF_MIGRATIONS_ASSEMBLY");
        if (!string.IsNullOrEmpty(envAssembly)) return envAssembly;

        var stackTrace = new StackTrace();
        var frames = stackTrace.GetFrames();

        foreach (var frame in frames)
        {
            var assembly = frame.GetMethod()?.DeclaringType?.Assembly;
            if (assembly == null || assembly.FullName == null) continue;
            var assemblyName = assembly.GetName().Name;
            if (assemblyName != null && assemblyName.Contains("Api")) return assemblyName;
        }

        var currentDir = Directory.GetCurrentDirectory();
        var dirName = Path.GetFileName(currentDir);

        return dirName.Contains("Api") ? dirName : "";
    }
}