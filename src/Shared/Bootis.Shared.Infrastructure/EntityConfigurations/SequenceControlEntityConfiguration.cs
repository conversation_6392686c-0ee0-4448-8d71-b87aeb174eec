using Bootis.Shared.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Shared.Infrastructure.EntityConfigurations;

public class SequenceControlEntityConfiguration : IEntityTypeConfiguration<SequenceControl>
{
    public void Configure(EntityTypeBuilder<SequenceControl> builder)
    {
        builder.ToTable("sequence_control");

        builder.<PERSON><PERSON>ey(c => new { c.<PERSON><PERSON><PERSON>, c.<PERSON>ant<PERSON>d, c.GroupTenantId });
    }
}