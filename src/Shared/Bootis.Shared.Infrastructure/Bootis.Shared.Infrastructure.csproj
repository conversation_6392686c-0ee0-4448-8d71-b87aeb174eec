<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{527d6691-30d2-444f-b415-09dd8669ddb2}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Bootis.Shared.Application\Bootis.Shared.Application.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.4.2"/>
        <PackageReference Include="Dapper" Version="2.1.66"/>
        <PackageReference Include="EFCore.NamingConventions" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.9"/>
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.9"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.9"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.9"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.9"/>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    </ItemGroup>

</Project>

