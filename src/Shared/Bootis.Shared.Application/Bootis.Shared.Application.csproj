<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{ef41f84f-38ff-415e-abda-f69928527c0b}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Bootis.Shared.Common\Bootis.Shared.Common.csproj"/>
        <ProjectReference Include="..\Bootis.Shared.Domain\Bootis.Shared.Domain.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="MassTransit.RabbitMQ" Version="8.5.2" />
        <PackageReference Include="MediatR" Version="13.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.9"/>
        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.14.0" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.14.0" />
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>

