using Bootis.Shared.Common.Security;

namespace Bootis.Shared.Application.Interfaces;

public interface IUserContext
{
    UserSession UserSession { get; set; }

    Guid SessionId => UserSession?.SessionId ?? Guid.Empty;
    Guid UserId => UserSession?.UserIdentity.UserId ?? Guid.Empty;

    Guid TenantId => UserSession?.UserIdentity.TenantId ?? Guid.Empty;

    Guid GroupTenantId => UserSession?.UserIdentity.GroupTenantId ?? Guid.Empty;

    bool IsAuthenticated => UserSession != null;
}