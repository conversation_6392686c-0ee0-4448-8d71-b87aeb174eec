using Bootis.Shared.Common.ValueObjects.Auth;
using OneOf;

namespace Bootis.Shared.Application.Interfaces;

public interface IIdentityClient
{
    Task<OneOf<TokenValidationSuccess, TokenValidationFailure>> IntrospectAsync(string token,
        IntrospectType introspectType, CancellationToken cancellationToken = default);

    Task<OneOf<AuthorizeSuccess, AuthorizeFailure>> AuthorizeAsync(Guid sessionId, Guid userId, List<int> requiredRoles,
        CancellationToken cancellationToken = default);
}