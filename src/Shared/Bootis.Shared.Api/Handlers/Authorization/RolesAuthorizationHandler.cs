using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.ValueObjects.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Infrastructure;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Bootis.Shared.Api.Handlers.Authorization;

public class IdentityRolesAuthorizationHandler(
    IHttpContextAccessor httpContextAccessor,
    IIdentityClient identityClient,
    IUserContext userContext,
    ILogger<IdentityRolesAuthorizationHandler> logger)
    : AuthorizationHandler<RolesAuthorizationRequirement>
{
    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        RolesAuthorizationRequirement requirement)
    {
        try
        {
            if (context.User.Identity?.IsAuthenticated != true)
            {
                logger.LogDebug("User is not authenticated");
                context.Fail(new AuthorizationFailureReason(this, nameof(AuthenticationError.NotAuthenticated)));
                return;
            }

            var httpContext = httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                logger.LogWarning("HttpContext is null during authorization");
                context.Fail(new AuthorizationFailureReason(this, nameof(AuthenticationError.InternalError)));
                return;
            }

#if DEBUG
            if (IsDebugAuthentication(httpContext))
            {
                logger.LogDebug("Debug authentication detected, allowing access");
                context.Succeed(requirement);
                return;
            }
#endif
            var requiredRoleIds = ParseRequiredRoles(requirement.AllowedRoles);

            if (requiredRoleIds.Count == 0)
            {
                logger.LogDebug("No specific roles required, authentication sufficient");
                context.Succeed(requirement);
                return;
            }

            if (TryAuthorizeFromUserContext(context, requirement, requiredRoleIds)) return;

            await AuthorizeViaIdentityService(context, requirement, requiredRoleIds);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during authorization");
            context.Fail(new AuthorizationFailureReason(this, nameof(AuthenticationError.InternalError)));
        }
    }

    private static bool IsDebugAuthentication(HttpContext httpContext)
    {
#if DEBUG
        return httpContext.Request.Headers.ContainsKey("X-Debug-Auth-User");
#else
        return false;
#endif
    }

    private List<int> ParseRequiredRoles(IEnumerable<string> allowedRoles)
    {
        var roleIds = new List<int>();

        foreach (var role in allowedRoles)
            if (int.TryParse(role, out var roleId))
                roleIds.Add(roleId);
            else
                logger.LogWarning("Unable to parse role '{Role}' as integer", role);

        logger.LogDebug("Required role IDs: {RoleIds}", string.Join(", ", roleIds));
        return roleIds;
    }

    private bool TryAuthorizeFromUserContext(
        AuthorizationHandlerContext context,
        RolesAuthorizationRequirement requirement,
        List<int> requiredRoleIds)
    {
        try
        {
            if (userContext.UserSession == null)
                return false;

            var userSession = userContext.UserSession;

            var userRoles = userSession.UserIdentity.Roles?.ToList() ?? [];
            var hasAllRequiredRoles = requiredRoleIds.All(requiredRole => userRoles.Contains(requiredRole));

            if (hasAllRequiredRoles)
            {
                logger.LogDebug("User {UserId} authorized via local context with roles: {UserRoles}",
                    userContext.UserId, string.Join(", ", userRoles));

                context.Succeed(requirement);
                return true;
            }

            logger.LogDebug("User {UserId} missing required roles. Has: [{UserRoles}], Needs: [{RequiredRoles}]",
                userContext.UserId,
                string.Join(", ", userRoles),
                string.Join(", ", requiredRoleIds));

            return false;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error checking authorization from user context");
            return false;
        }
    }

    private async Task AuthorizeViaIdentityService(
        AuthorizationHandlerContext context,
        RolesAuthorizationRequirement requirement,
        List<int> requiredRoleIds)
    {
        try
        {
            logger.LogDebug("Authorizing via Identity Service for roles: {RequiredRoles}",
                string.Join(", ", requiredRoleIds));

            var result =
                await identityClient.AuthorizeAsync(userContext.SessionId, userContext.UserId, requiredRoleIds);

            if (result.IsT0 && result.AsT0.Authorized)
            {
                var success = result.AsT0;
                logger.LogDebug("Authorization successful via Identity Service for user. Granted roles: {GrantedRoles}",
                    string.Join(", ", success.GrantedRoles));

                context.Succeed(requirement);
            }
            else
            {
                var failure = result.IsT1 ? result.AsT1 : null;
                var errorCode = failure?.ErrorCode ?? AuthenticationError.InsufficientPermissions;

                logger.LogDebug("Authorization failed via Identity Service: {ErrorCode}", errorCode);

                if (failure != null)
                    logger.LogDebug("Missing roles: {MissingRoles}", string.Join(", ", failure.MissingRoles));

                context.Fail(new AuthorizationFailureReason(this, errorCode.ToString()));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during Identity Service authorization call");
            context.Fail(new AuthorizationFailureReason(this, nameof(AuthenticationError.ServiceUnavailable)));
        }
    }
}