using System.Reflection;
using System.Text;

namespace Bootis.Shared.Api.Components;

public static class BootisLogo
{
    private static string _cachedSvgTemplate;
    private static readonly Lock CacheLock = new();

    private static string GetSvgTemplate()
    {
        if (_cachedSvgTemplate != null)
            return _cachedSvgTemplate;

        lock (CacheLock)
        {
            if (_cachedSvgTemplate != null)
                return _cachedSvgTemplate;

            var assembly = Assembly.GetExecutingAssembly();
            var resourceName = "Bootis.Shared.Api.Resources.bootis-logo.svg";

            using var stream = assembly.GetManifestResourceStream(resourceName);
            if (stream == null) throw new InvalidOperationException($"Embedded resource '{resourceName}' not found.");

            using var reader = new StreamReader(stream);
            _cachedSvgTemplate = reader.ReadToEnd();
            return _cachedSvgTemplate;
        }
    }

    public static string GetSvg(int width = 48, int height = 48, string fillColor = "white")
    {
        var svgTemplate = GetSvgTemplate();

        var svg = svgTemplate
            .Replace("#FILLCOLOR#", fillColor)
            .Replace("width=\"220\"", $"width=\"{width}\"")
            .Replace("height=\"220\"", $"height=\"{height}\"");

        var xmlDeclarationEnd = svg.IndexOf(">");
        if (xmlDeclarationEnd > 0 && svg.StartsWith("<?xml"))
        {
            var svgStart = svg.IndexOf("<svg", xmlDeclarationEnd);
            if (svgStart > 0) svg = svg.Substring(svgStart);
        }

        if (!svg.Contains("<!DOCTYPE")) return svg.Trim();
        var docTypeStart = svg.IndexOf("<!DOCTYPE");
        var docTypeEnd = svg.IndexOf(">", docTypeStart) + 1;
        svg = svg.Remove(docTypeStart, docTypeEnd - docTypeStart);

        return svg.Trim();
    }

    public static string GetSmallSvg(string fillColor = "white")
    {
        return GetSvg(24, 24, fillColor);
    }

    public static string GetMediumSvg(string fillColor = "white")
    {
        return GetSvg(48, 48, fillColor);
    }

    public static string GetLargeSvg(string fillColor = "white")
    {
        return GetSvg(80, 80, fillColor);
    }

    public static string GetExtraLargeSvg(string fillColor = "white")
    {
        return GetSvg(128, 128, fillColor);
    }

    public static string GetBrandedSvg(int width = 80, int height = 80)
    {
        return GetSvg(width, height);
    }

    public static string GetDataUri(int width = 48, int height = 48, string fillColor = "white")
    {
        var svg = GetSvg(width, height, fillColor);
        var encoded = Convert.ToBase64String(Encoding.UTF8.GetBytes(svg));
        return $"data:image/svg+xml;base64,{encoded}";
    }

    public static string GetPath(string fillColor = "white")
    {
        var svgTemplate = GetSvgTemplate();

        var gStart = svgTemplate.IndexOf("<g transform=");
        var gEnd = svgTemplate.IndexOf("</g>", gStart) + 4;
        var gElement = svgTemplate.Substring(gStart, gEnd - gStart);

        var updatedGElement = gElement.Replace("#000000", fillColor);

        return updatedGElement;
    }

    public static string GetFaviconSvg(int size = 32)
    {
        return GetBrandedSvg(size, size);
    }

    public static string GetFaviconDataUri(int size = 32)
    {
        var svg = GetBrandedSvg(size, size);
        svg = svg.Replace("\"", "'")
            .Replace("\r\n", " ")
            .Replace("\n", " ")
            .Replace("  ", " ");
        return $"data:image/svg+xml,{Uri.EscapeDataString(svg)}";
    }
}