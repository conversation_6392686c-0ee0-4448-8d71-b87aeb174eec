using System.Reflection;

namespace Bootis.Shared.Api.Components;

public static class EmbeddedResourceReader
{
    private static readonly object _lock = new();
    private static readonly Dictionary<string, string> _cache = new();

    public static string GetResourceContent(string resourceName)
    {
        if (_cache.TryGetValue(resourceName, out var cached))
            return cached;

        lock (_lock)
        {
            if (_cache.TryGetValue(resourceName, out cached))
                return cached;

            var assembly = Assembly.GetExecutingAssembly();
            using var stream = assembly.GetManifestResourceStream(resourceName);

            if (stream == null)
                throw new InvalidOperationException($"Embedded resource '{resourceName}' not found.");

            using var reader = new StreamReader(stream);
            var content = reader.ReadToEnd();
            _cache[resourceName] = content;
            return content;
        }
    }

    public static string GetSwaggerDarkCss()
    {
        return GetResourceContent("Bootis.Shared.Api.Resources.swagger-dark.css");
    }
}