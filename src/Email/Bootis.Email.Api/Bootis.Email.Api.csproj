<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <AssemblyName>Bootis.Email.Api</AssemblyName>
        <RootNamespace>Bootis.Email.Api</RootNamespace>
        <UserSecretsId>5d659314-74bf-4d94-b852-548e1020fc53</UserSecretsId>
        <ProjectGuid>{d1096846-3d3b-4efc-a844-fc9fbed7943e}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AspNetCore.HealthChecks.SendGrid" Version="9.0.0"/>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.9"/>
        <PackageReference Include="SendGrid" Version="9.29.3"/>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Aspire\Bootis.ServiceDefaults\Bootis.ServiceDefaults.csproj"/>
        <ProjectReference Include="..\..\Identity\Bootis.Identity.Contracts\Bootis.Identity.Contracts.csproj"/>
        <ProjectReference Include="..\..\Shared\Bootis.Shared.Api\Bootis.Shared.Api.csproj"/>
    </ItemGroup>

</Project>

