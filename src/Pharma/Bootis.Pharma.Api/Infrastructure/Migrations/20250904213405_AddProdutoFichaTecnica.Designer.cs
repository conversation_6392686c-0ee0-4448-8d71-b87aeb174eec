// <auto-generated />
using System;
using Bootis.Shared.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Bootis.Pharma.Api.Infrastructure.Migrations
{
    [DbContext(typeof(BootisContext))]
    [Migration("20250904213405_AddProdutoFichaTecnica")]
    partial class AddProdutoFichaTecnica
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.BibliografiaAggregate.Bibliografia", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(110)
                        .HasColumnType("character varying(110)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_bibliografias");

                    b.ToTable("bibliografias", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate.CapsulaCor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CorCapsula")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("cor_capsula");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<bool>("Transparente")
                        .HasColumnType("boolean")
                        .HasColumnName("transparente");

                    b.HasKey("Id")
                        .HasName("pk_capsulas_cor");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_capsulas_cor_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_capsulas_cor_tenant_id");

                    b.ToTable("capsulas_cor", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate.CapsulaTamanho", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("NumeroCapsula")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("numero_capsula");

                    b.Property<decimal>("VolumeMl")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("volume_ml");

                    b.HasKey("Id")
                        .HasName("pk_capsulas_tamanho");

                    b.ToTable("capsulas_tamanho", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.CapsulaProntaAssociacao.CapsulaProntaMateriaPrimaAssociacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("ProdutoCapsulaProntaId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_capsula_pronta_id");

                    b.Property<Guid?>("ProdutoMateriaPrimaId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_materia_prima_id");

                    b.Property<decimal?>("Quantidade")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("quantidade");

                    b.Property<int?>("UnidadeId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_id");

                    b.HasKey("Id")
                        .HasName("pk_capsula_pronta_materia_prima_associacoes");

                    b.HasIndex("ProdutoCapsulaProntaId")
                        .HasDatabaseName("ix_capsula_pronta_materia_prima_associacoes_produto_capsula_pr");

                    b.HasIndex("ProdutoMateriaPrimaId")
                        .HasDatabaseName("ix_capsula_pronta_materia_prima_associacoes_produto_materia_pr");

                    b.ToTable("capsula_pronta_materia_prima_associacoes", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.CasAggregate.Cas", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("NomeProduto")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("character varying(120)")
                        .HasColumnName("nome_produto");

                    b.Property<string>("NumeroCas")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("numero_cas");

                    b.HasKey("Id")
                        .HasName("pk_cas");

                    b.ToTable("cas", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ClasseProdutoAggregate.ClasseProduto", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("id");

                    b.Property<bool>("ControlaEstoque")
                        .HasColumnType("boolean")
                        .HasColumnName("controla_estoque");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("abreviacao");

                    b.Property<int>("UnidadeCalculo")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_calculo");

                    b.Property<int>("UnidadePadraoVisualizacao")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_padrao_visualizacao");

                    b.HasKey("Id")
                        .HasName("pk_classes_produto");

                    b.ToTable("classes_produto", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ClasseProdutoAggregate.ClasseProdutoUnidadePrescricao", b =>
                {
                    b.Property<int>("ClasseProdutoId")
                        .HasColumnType("integer")
                        .HasColumnName("classe_produto_id");

                    b.Property<int>("UnidadeMedida")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida");

                    b.HasKey("ClasseProdutoId", "UnidadeMedida")
                        .HasName("pk_classes_produto_unidades_prescricao");

                    b.ToTable("classes_produto_unidades_prescricao", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ClasseProdutoAggregate.ClasseProdutoUnidadeVisualizacao", b =>
                {
                    b.Property<int>("ClasseProdutoId")
                        .HasColumnType("integer")
                        .HasColumnName("classe_produto_id");

                    b.Property<int>("UnidadeMedida")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida");

                    b.HasKey("ClasseProdutoId", "UnidadeMedida")
                        .HasName("pk_classes_produto_unidades_visualizacao");

                    b.ToTable("classes_produto_unidades_visualizacao", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.DcbAggregate.Dcb", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("NomeDcb")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("nome_dcb");

                    b.Property<int>("NumeroDcb")
                        .HasColumnType("integer")
                        .HasColumnName("numero_dcb");

                    b.HasKey("Id")
                        .HasName("pk_dcbs");

                    b.ToTable("dcbs", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemAssociacao.EmbalagemAssociacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid?>("ProdutoEmbalagemAssociadaId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_embalagem_associada_id");

                    b.Property<Guid>("ProdutoEmbalagemId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_embalagem_id");

                    b.Property<int?>("QuantidadeEmbalagem")
                        .HasColumnType("integer")
                        .HasColumnName("quantidade_embalagem");

                    b.HasKey("Id")
                        .HasName("pk_embalagem_associacoes");

                    b.HasIndex("ProdutoEmbalagemAssociadaId")
                        .HasDatabaseName("ix_embalagem_associacoes_produto_embalagem_associada_id");

                    b.HasIndex("ProdutoEmbalagemId")
                        .HasDatabaseName("ix_embalagem_associacoes_produto_embalagem_id");

                    b.ToTable("embalagem_associacoes", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemAssociacao.EmbalagemCapsulaTamanhoAssociacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("CapsulaTamanhoId")
                        .HasColumnType("uuid")
                        .HasColumnName("capsula_tamanho_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("ProdutoEmbalagemId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_embalagem_id");

                    b.Property<int?>("QuantidadeCapsula")
                        .HasColumnType("integer")
                        .HasColumnName("quantidade_capsula");

                    b.HasKey("Id")
                        .HasName("pk_embalagem_capsula_tamanho_associacoes");

                    b.HasIndex("CapsulaTamanhoId")
                        .HasDatabaseName("ix_embalagem_capsula_tamanho_associacoes_capsula_tamanho_id");

                    b.HasIndex("ProdutoEmbalagemId")
                        .HasDatabaseName("ix_embalagem_capsula_tamanho_associacoes_produto_embalagem_id");

                    b.ToTable("embalagem_capsula_tamanho_associacoes", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemAssociacao.ModeloRotuloEmbalagemAssociacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("FormaFarmaceuticaId")
                        .HasColumnType("uuid")
                        .HasColumnName("forma_farmaceutica_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("ModeloRotuloId")
                        .HasColumnType("uuid")
                        .HasColumnName("modelo_rotulo_id");

                    b.Property<Guid>("ProdutoEmbalagemId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_embalagem_id");

                    b.HasKey("Id")
                        .HasName("pk_modelo_rotulo_embalagem_associacoes");

                    b.HasIndex("FormaFarmaceuticaId")
                        .HasDatabaseName("ix_modelo_rotulo_embalagem_associacoes_forma_farmaceutica_id");

                    b.HasIndex("ModeloRotuloId")
                        .HasDatabaseName("ix_modelo_rotulo_embalagem_associacoes_modelo_rotulo_id");

                    b.HasIndex("ProdutoEmbalagemId")
                        .HasDatabaseName("ix_modelo_rotulo_embalagem_associacoes_produto_embalagem_id");

                    b.ToTable("modelo_rotulo_embalagem_associacoes", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate.EmbalagemClassificacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_embalagens_classificacao");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_embalagens_classificacao_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_embalagens_classificacao_tenant_id");

                    b.ToTable("embalagens_classificacao", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoFormaFarmaceuticaAggregate.EmbalagemClassificacaoFormaFarmaceutica", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("EmbalagemClassificacaoId")
                        .HasColumnType("uuid")
                        .HasColumnName("embalagem_classificacao_id");

                    b.Property<Guid>("FormaFarmaceuticaId")
                        .HasColumnType("uuid")
                        .HasColumnName("forma_farmaceutica_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_embalagens_classificacao_forma_farmaceutica");

                    b.HasIndex("EmbalagemClassificacaoId")
                        .HasDatabaseName("ix_embalagens_classificacao_forma_farmaceutica_embalagem_class");

                    b.HasIndex("FormaFarmaceuticaId")
                        .HasDatabaseName("ix_embalagens_classificacao_forma_farmaceutica_forma_farmaceut");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_embalagens_classificacao_forma_farmaceutica_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_embalagens_classificacao_forma_farmaceutica_tenant_id");

                    b.ToTable("embalagens_classificacao_forma_farmaceutica", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EnsaioControleQualidadeAggregate.EnsaioControleQualidade", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(110)
                        .HasColumnType("character varying(110)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_ensaios_controle_qualidade");

                    b.ToTable("ensaios_controle_qualidade", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Apresentacao")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)")
                        .HasColumnName("apresentacao");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<decimal>("CustoOperacional")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("custo_operacional");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_cadastro");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("LaboratorioId")
                        .HasColumnType("uuid")
                        .HasColumnName("laboratorio_id");

                    b.Property<Guid?>("ModeloOrdemManipulacaoId")
                        .HasColumnType("uuid")
                        .HasColumnName("modelo_ordem_manipulacao_id");

                    b.Property<int>("Ordem")
                        .HasColumnType("integer")
                        .HasColumnName("ordem");

                    b.Property<decimal>("PercentualMinimoExcipiente")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("percentual_minimo_excipiente");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoCalculo")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_calculo");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.Property<int>("UsoFormaFarmaceutica")
                        .HasColumnType("integer")
                        .HasColumnName("uso_forma_farmaceutica");

                    b.Property<int>("ValidadeDias")
                        .HasColumnType("integer")
                        .HasColumnName("validade_dias");

                    b.HasKey("Id")
                        .HasName("pk_formas_farmaceutica");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_formas_farmaceutica_group_tenant_id");

                    b.HasIndex("ModeloOrdemManipulacaoId")
                        .HasDatabaseName("ix_formas_farmaceutica_modelo_ordem_manipulacao_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_formas_farmaceutica_tenant_id");

                    b.ToTable("formas_farmaceutica", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.GrupoAggregate.Grupo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("DesativarProjecaoEstoque")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("desativar_projecao_estoque");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("descricao");

                    b.Property<int?>("DiasEstoqueMaximo")
                        .HasColumnType("integer")
                        .HasColumnName("dias_estoque_maximo");

                    b.Property<int?>("DiasEstoqueMinimo")
                        .HasColumnType("integer")
                        .HasColumnName("dias_estoque_minimo");

                    b.Property<int?>("DiasFimProjecao")
                        .HasColumnType("integer")
                        .HasColumnName("dias_fim_projecao");

                    b.Property<int?>("DiasInicioProjecao")
                        .HasColumnType("integer")
                        .HasColumnName("dias_inicio_projecao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_produto_grupos");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_produto_grupos_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_produto_grupos_tenant_id");

                    b.ToTable("produto_grupos", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.MensagemAggregate.Mensagem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)")
                        .HasColumnName("descricao");

                    b.Property<bool>("ExibeFichaPesagem")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("exibe_ficha_pesagem");

                    b.Property<bool>("ExibeImpressaoRotulo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("exibe_impressao_rotulo");

                    b.Property<bool>("ExibeRotulagem")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("exibe_rotulagem");

                    b.Property<bool>("ExibeVenda")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("exibe_venda");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_mensagens");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_mensagens_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_mensagens_tenant_id");

                    b.ToTable("mensagens", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ModeloOrdemManipulacaoAggregate.ModeloOrdemManipulacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoOrdemManipulacao")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_ordem_manipulacao");

                    b.HasKey("Id")
                        .HasName("pk_modelos_ordem_manipulacao");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_modelos_ordem_manipulacao_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_modelos_ordem_manipulacao_tenant_id");

                    b.ToTable("modelos_ordem_manipulacao", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoRotulo")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_rotulo");

                    b.HasKey("Id")
                        .HasName("pk_modelos_rotulo");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_modelos_rotulo_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_modelos_rotulo_tenant_id");

                    b.ToTable("modelos_rotulo", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.NcmAggregate.Ncm", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Categoria")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("categoria");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("codigo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_ncms");

                    b.ToTable("ncms", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<int>("ClasseProdutoId")
                        .HasColumnType("integer")
                        .HasColumnName("classe_produto_id");

                    b.Property<string>("CodigoBarras")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("codigo_barras");

                    b.Property<bool>("ControlaLote")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("controla_lote");

                    b.Property<bool>("ControlaQualidade")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("controla_qualidade");

                    b.Property<DateOnly?>("DataUltimaProjecaoAplicada")
                        .HasColumnType("date")
                        .HasColumnName("data_ultima_projecao_aplicada");

                    b.Property<bool>("DesativarProjecaoEstoque")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("desativar_projecao_estoque");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<string>("DescricaoRotulo")
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)")
                        .HasColumnName("descricao_rotulo");

                    b.Property<decimal?>("EstoqueMaximo")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("estoque_maximo");

                    b.Property<decimal?>("EstoqueMinimo")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("estoque_minimo");

                    b.Property<bool>("Etiqueta")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("etiqueta");

                    b.Property<Guid?>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("LinkImagem")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("link_imagem");

                    b.Property<decimal>("MargemLucro")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("margem_lucro");

                    b.Property<decimal?>("MediaConsumoDiaria")
                        .HasColumnType("numeric")
                        .HasColumnName("media_consumo_diaria");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.Property<Guid>("SubGrupoId")
                        .HasColumnType("uuid")
                        .HasColumnName("sub_grupo_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("TipoClassificacaoPsicotropicaMedicamentoId")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_classificacao_psicotropica_medicamento_id");

                    b.Property<int?>("TipoTarjaMedicamentoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("tipo_tarja_medicamento_id");

                    b.Property<int>("UnidadeEstoqueId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_estoque_id");

                    b.Property<bool>("UsoContinuo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("uso_continuo");

                    b.Property<decimal>("ValorCusto")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_custo");

                    b.Property<decimal>("ValorVenda")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_venda");

                    b.HasKey("Id")
                        .HasName("pk_produtos");

                    b.HasIndex("FornecedorId")
                        .HasDatabaseName("ix_produtos_fornecedor_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_produtos_group_tenant_id");

                    b.HasIndex("SubGrupoId")
                        .HasDatabaseName("ix_produtos_sub_grupo_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_produtos_tenant_id");

                    b.ToTable("produtos", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoCapsulaPronta", b =>
                {
                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<Guid?>("NumeroCapsulaId")
                        .HasColumnType("uuid")
                        .HasColumnName("numero_capsula_id");

                    b.HasKey("ProdutoId")
                        .HasName("pk_produtos_capsula_pronta");

                    b.HasIndex("NumeroCapsulaId")
                        .HasDatabaseName("ix_produtos_capsula_pronta_numero_capsula_id");

                    b.ToTable("produtos_capsula_pronta", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoEmbalagem", b =>
                {
                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<Guid?>("EmbalagemClassificacaoId")
                        .HasColumnType("uuid")
                        .HasColumnName("embalagem_classificacao_id");

                    b.Property<decimal?>("Volume")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("volume");

                    b.HasKey("ProdutoId")
                        .HasName("pk_produtos_embalagem");

                    b.HasIndex("EmbalagemClassificacaoId")
                        .HasDatabaseName("ix_produtos_embalagem_embalagem_classificacao_id");

                    b.ToTable("produtos_embalagem", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoMateriaPrima", b =>
                {
                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<int?>("BiofarmaceuticaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("biofarmaceutica_id")
                        .HasDefaultValueSql("1");

                    b.Property<Guid?>("CasId")
                        .HasColumnType("uuid")
                        .HasColumnName("cas_id");

                    b.Property<decimal?>("ConcentracaoAgua")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("concentracao_agua");

                    b.Property<Guid?>("DcbId")
                        .HasColumnType("uuid")
                        .HasColumnName("dcb_id");

                    b.Property<decimal?>("Densidade")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("densidade");

                    b.Property<int?>("DiasValidade")
                        .HasMaxLength(3)
                        .HasColumnType("integer")
                        .HasColumnName("dias_validade");

                    b.Property<decimal?>("DiluicaoFornecedor")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("diluicao_fornecedor");

                    b.Property<decimal?>("DiluicaoInterna")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("diluicao_interna");

                    b.Property<bool?>("ExigeCapsulaGastroresistente")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("exige_capsula_gastroresistente");

                    b.Property<decimal?>("FatorConcentracaoAgua")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("fator_concentracao_agua");

                    b.Property<decimal?>("FatorDiluicaoFornecedor")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("fator_diluicao_fornecedor");

                    b.Property<decimal?>("FatorDiluicaoInterna")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("fator_diluicao_interna");

                    b.Property<decimal>("FatorEquivalencia")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasDefaultValue(1m)
                        .HasColumnName("fator_equivalencia");

                    b.Property<bool?>("IsExcipiente")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_excipiente");

                    b.Property<bool?>("IsMonodroga")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_monodroga");

                    b.Property<bool?>("IsPellets")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_pellets");

                    b.Property<bool?>("IsQsp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_qsp");

                    b.Property<string>("ObservacaoRotuloArmazenagem")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("observacao_rotulo_armazenagem");

                    b.Property<decimal>("PesoMolecularBase")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasDefaultValue(1m)
                        .HasColumnName("peso_molecular_base");

                    b.Property<decimal>("PesoMolecularSal")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasDefaultValue(1m)
                        .HasColumnName("peso_molecular_sal");

                    b.Property<Guid?>("ProdutoExcipienteEspecificoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_excipiente_especifico_id");

                    b.Property<decimal?>("QuantidadeUnidadeAlternativa1")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("quantidade_unidade_alternativa1");

                    b.Property<decimal?>("QuantidadeUnidadeAlternativa2")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("quantidade_unidade_alternativa2");

                    b.Property<bool?>("SomenteDiluido")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("somente_diluido");

                    b.Property<bool?>("SomenteLaboratorio")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("somente_laboratorio");

                    b.Property<int?>("TipoComponenteId")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_componente_id");

                    b.Property<decimal?>("ToleranciaPesagemDown")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("tolerancia_pesagem_down");

                    b.Property<decimal?>("ToleranciaPesagemUp")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("tolerancia_pesagem_up");

                    b.Property<int?>("UnidadeAlternativaConversaoId1")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_alternativa_conversao_id1");

                    b.Property<int?>("UnidadeAlternativaConversaoId2")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_alternativa_conversao_id2");

                    b.Property<int?>("UnidadeAlternativaId1")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_alternativa_id1");

                    b.Property<int?>("UnidadeAlternativaId2")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_alternativa_id2");

                    b.Property<int?>("UnidadePrescricaoId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_prescricao_id");

                    b.Property<decimal?>("Valencia")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("valencia");

                    b.HasKey("ProdutoId")
                        .HasName("pk_produtos_materia_prima");

                    b.HasIndex("CasId")
                        .HasDatabaseName("ix_produtos_materia_prima_cas_id");

                    b.HasIndex("DcbId")
                        .HasDatabaseName("ix_produtos_materia_prima_dcb_id");

                    b.HasIndex("ProdutoExcipienteEspecificoId")
                        .HasDatabaseName("ix_produtos_materia_prima_produto_excipiente_especifico_id");

                    b.ToTable("produtos_materia_prima", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoTipoCapsula", b =>
                {
                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<Guid?>("CapsulaCorId")
                        .HasColumnType("uuid")
                        .HasColumnName("capsula_cor_id");

                    b.Property<Guid?>("NumeroCapsulaId")
                        .HasColumnType("uuid")
                        .HasColumnName("numero_capsula_id");

                    b.Property<Guid?>("TipoCapsulaId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_capsula_id");

                    b.HasKey("ProdutoId")
                        .HasName("pk_produtos_tipo_capsula");

                    b.HasIndex("CapsulaCorId")
                        .HasDatabaseName("ix_produtos_tipo_capsula_capsula_cor_id");

                    b.HasIndex("NumeroCapsulaId")
                        .HasDatabaseName("ix_produtos_tipo_capsula_numero_capsula_id");

                    b.HasIndex("TipoCapsulaId")
                        .HasDatabaseName("ix_produtos_tipo_capsula_tipo_capsula_id");

                    b.ToTable("produtos_tipo_capsula", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAssociadoAggregate.ProdutoAssociado", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Acumula")
                        .HasColumnType("boolean")
                        .HasColumnName("acumula");

                    b.Property<decimal?>("DosagemMaxima")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("dosagem_maxima");

                    b.Property<decimal?>("DosagemMinima")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("dosagem_minima");

                    b.Property<Guid>("FormaFarmaceuticaId")
                        .HasColumnType("uuid")
                        .HasColumnName("forma_farmaceutica_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("ProdutoAssociadoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_associado_id");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal>("QuantidadeAssociada")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("quantidade_associada");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoRelacaoQuantidade")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_relacao_quantidade");

                    b.Property<int>("UnidadeMedidaDosagem")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_dosagem");

                    b.Property<int>("UnidadeMedidaQuantidadeAssociada")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_quantidade_associada");

                    b.HasKey("Id")
                        .HasName("pk_produtos_associado");

                    b.HasIndex("FormaFarmaceuticaId")
                        .HasDatabaseName("ix_produtos_associado_forma_farmaceutica_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_produtos_associado_group_tenant_id");

                    b.HasIndex("ProdutoAssociadoId")
                        .HasDatabaseName("ix_produtos_associado_produto_associado_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_produtos_associado_produto_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_produtos_associado_tenant_id");

                    b.ToTable("produtos_associado", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoDiluidoAggregate.ProdutoDiluido", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("Diluicao")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("diluicao");

                    b.Property<decimal>("DosagemMaxima")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("dosagem_maxima");

                    b.Property<decimal>("DosagemMinima")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("dosagem_minima");

                    b.Property<Guid?>("FormaFarmaceuticaId")
                        .HasColumnType("uuid")
                        .HasColumnName("forma_farmaceutica_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<bool>("SeQualquerDosagem")
                        .HasColumnType("boolean")
                        .HasColumnName("se_qualquer_dosagem");

                    b.Property<bool>("SeTodasFormasFarmaceuticas")
                        .HasColumnType("boolean")
                        .HasColumnName("se_todas_formas_farmaceuticas");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_produtos_diluido");

                    b.HasIndex("FormaFarmaceuticaId")
                        .HasDatabaseName("ix_produtos_diluido_forma_farmaceutica_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_produtos_diluido_group_tenant_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_produtos_diluido_produto_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_produtos_diluido_tenant_id");

                    b.ToTable("produtos_diluido", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoExcipienteAggregate.ProdutoExcipiente", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Biofarmaceutica")
                        .HasColumnType("integer")
                        .HasColumnName("biofarmaceutica");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int>("Prioridade")
                        .HasColumnType("integer")
                        .HasColumnName("prioridade");

                    b.Property<Guid>("ProdutoMateriaPrimaId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_materia_prima_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_produtos_excipiente");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_produtos_excipiente_group_tenant_id");

                    b.HasIndex("ProdutoMateriaPrimaId")
                        .HasDatabaseName("ix_produtos_excipiente_produto_materia_prima_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_produtos_excipiente_tenant_id");

                    b.ToTable("produtos_excipiente", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate.ProdutoFichaTecnica", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateOnly>("DataCadastro")
                        .HasColumnType("date")
                        .HasColumnName("data_cadastro");

                    b.Property<string>("FormulaMolecular")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("formula_molecular");

                    b.Property<string>("InformacoesComplementares")
                        .HasColumnType("text")
                        .HasColumnName("informacoes_complementares");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("NomeCientifico")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome_cientifico");

                    b.Property<string>("NomePopular")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome_popular");

                    b.Property<int>("NumeroRevisao")
                        .HasColumnType("integer")
                        .HasColumnName("numero_revisao");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_produtos_ficha_tecnica");

                    b.HasIndex("ProdutoId")
                        .IsUnique()
                        .HasDatabaseName("ix_produtos_ficha_tecnica_produto_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_produtos_ficha_tecnica_usuario_id");

                    b.ToTable("produtos_ficha_tecnica", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate.ProdutoFichaTecnicaEspecificacao", b =>
                {
                    b.Property<Guid>("ProdutoFichaTecnicaId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_ficha_tecnica_id");

                    b.Property<Guid>("EnsaioControleQualidadeId")
                        .HasColumnType("uuid")
                        .HasColumnName("ensaio_controle_qualidade_id");

                    b.Property<Guid>("BibliografiaId")
                        .HasColumnType("uuid")
                        .HasColumnName("bibliografia_id");

                    b.Property<string>("Especificacao")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("especificacao");

                    b.Property<bool>("MostrarCertificadoAnalise")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("mostrar_certificado_analise");

                    b.HasKey("ProdutoFichaTecnicaId", "EnsaioControleQualidadeId", "BibliografiaId")
                        .HasName("pk_produtos_ficha_tecnica_especificacao");

                    b.HasIndex("BibliografiaId")
                        .HasDatabaseName("ix_produtos_ficha_tecnica_especificacao_bibliografia_id");

                    b.HasIndex("EnsaioControleQualidadeId")
                        .HasDatabaseName("ix_produtos_ficha_tecnica_especificacao_ensaio_controle_qualid");

                    b.ToTable("produtos_ficha_tecnica_especificacao", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate.ProdutoIncompativel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int>("NivelIncompatibilidade")
                        .HasColumnType("integer")
                        .HasColumnName("nivel_incompatibilidade");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<Guid>("ProdutoIncompativelId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_incompativel_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_produtos_incompativel");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_produtos_incompativel_group_tenant_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_produtos_incompativel_produto_id");

                    b.HasIndex("ProdutoIncompativelId")
                        .HasDatabaseName("ix_produtos_incompativel_produto_incompativel_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_produtos_incompativel_tenant_id");

                    b.ToTable("produtos_incompativel", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoMensagemAggregate.ProdutoMensagem", b =>
                {
                    b.Property<Guid>("MensagemId")
                        .HasColumnType("uuid")
                        .HasColumnName("mensagem_id");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("MensagemId", "ProdutoId")
                        .HasName("pk_mensagem_produtos");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_mensagem_produtos_produto_id");

                    b.ToTable("mensagem_produtos", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate.ProdutoSinonimo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("DescricaoRotulo")
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)")
                        .HasColumnName("descricao_rotulo");

                    b.Property<decimal?>("FatorCorrecao")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("fator_correcao");

                    b.Property<decimal?>("FatorEquivalencia")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("fator_equivalencia");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<decimal>("PercentualCorrecao")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("percentual_correcao");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<string>("Sinonimo")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("sinonimo");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_produtos_sinonimo");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_produtos_sinonimo_group_tenant_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_produtos_sinonimo_produto_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_produtos_sinonimo_tenant_id");

                    b.ToTable("produtos_sinonimo", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.SubGrupoAggregate.SubGrupo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<Guid>("GrupoId")
                        .HasColumnType("uuid")
                        .HasColumnName("grupo_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_sub_grupos");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_sub_grupos_group_tenant_id");

                    b.HasIndex("GrupoId")
                        .HasDatabaseName("ix_sub_grupos_grupo_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_sub_grupos_tenant_id");

                    b.ToTable("sub_grupos", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.TipoCapsulaAggregate.TipoCapsula", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("CapsulaGastroresistente")
                        .HasColumnType("boolean")
                        .HasColumnName("capsula_gastroresistente");

                    b.Property<bool>("CapsulaVegetal")
                        .HasColumnType("boolean")
                        .HasColumnName("capsula_vegetal");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_tipos_capsula");

                    b.ToTable("tipos_capsula", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.Enumerations.UnidadeMedida", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("descricao");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("abreviacao");

                    b.Property<int>("TipoUnidade")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_unidade");

                    b.Property<bool>("UnidadeAlternativa")
                        .HasColumnType("boolean")
                        .HasColumnName("unidade_alternativa");

                    b.Property<bool>("UnidadeEstoque")
                        .HasColumnType("boolean")
                        .HasColumnName("unidade_estoque");

                    b.Property<bool>("UnidadePosologia")
                        .HasColumnType("boolean")
                        .HasColumnName("unidade_posologia");

                    b.Property<bool>("UnidadePrescricao")
                        .HasColumnType("boolean")
                        .HasColumnName("unidade_prescricao");

                    b.HasKey("Id")
                        .HasName("pk_unidades_medida");

                    b.ToTable("unidades_medida", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.CfopAggregate.Cfop", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Codigo")
                        .HasColumnType("integer")
                        .HasColumnName("codigo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_cfops");

                    b.ToTable("cfops", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.CstCsosnAggregate.CstCsosn", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("codigo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_csts_csosn");

                    b.ToTable("csts_csosn", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NaturezaOperacaoAggregate.NaturezaOperacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_naturezas_operacao");

                    b.ToTable("naturezas_operacao", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateOnly>("DataEmissao")
                        .HasColumnType("date")
                        .HasColumnName("data_emissao");

                    b.Property<DateOnly?>("DataEntrega")
                        .HasColumnType("date")
                        .HasColumnName("data_entrega");

                    b.Property<DateTime?>("DataLancamento")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_lancamento");

                    b.Property<Guid?>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("InformacoesComplementares")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)")
                        .HasColumnName("informacoes_complementares");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid?>("NaturezaOperacaoId")
                        .HasColumnType("uuid")
                        .HasColumnName("natureza_operacao_id");

                    b.Property<int>("Numero")
                        .HasColumnType("integer")
                        .HasColumnName("numero");

                    b.Property<int>("Serie")
                        .HasColumnType("integer")
                        .HasColumnName("serie");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(2)
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid?>("TipoFreteId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_frete_id");

                    b.Property<Guid?>("TransportadoraId")
                        .HasColumnType("uuid")
                        .HasColumnName("transportadora_id");

                    b.Property<decimal?>("ValorBaseIcms")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_base_icms");

                    b.Property<decimal?>("ValorBaseIcmsSubstituicao")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_base_icms_substituicao");

                    b.Property<decimal?>("ValorDesconto")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_desconto");

                    b.Property<decimal?>("ValorFrete")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_frete");

                    b.Property<decimal?>("ValorIcms")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_icms");

                    b.Property<decimal?>("ValorIcmsSubstituicao")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_icms_substituicao");

                    b.Property<decimal?>("ValorIpi")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_ipi");

                    b.Property<decimal?>("ValorOutrasDespesas")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_outras_despesas");

                    b.Property<decimal?>("ValorProdutos")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_produtos");

                    b.Property<decimal?>("ValorSeguro")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_seguro");

                    b.Property<decimal?>("ValorTotalNotaFiscal")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_total_nota_fiscal");

                    b.HasKey("Id")
                        .HasName("pk_notas_fiscais_entrada");

                    b.HasIndex("FornecedorId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_fornecedor_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_group_tenant_id");

                    b.HasIndex("NaturezaOperacaoId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_natureza_operacao_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_tenant_id");

                    b.HasIndex("TipoFreteId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_tipo_frete_id");

                    b.HasIndex("TransportadoraId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_transportadora_id");

                    b.ToTable("notas_fiscais_entrada", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaHistorico", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("Data")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("NotaFiscalEntradaId")
                        .HasColumnType("uuid")
                        .HasColumnName("nota_fiscal_entrada_id");

                    b.Property<int>("StatusAlterado")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(2)
                        .HasColumnName("status_alterado");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_notas_fiscais_historico");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_notas_fiscais_historico_group_tenant_id");

                    b.HasIndex("NotaFiscalEntradaId")
                        .HasDatabaseName("ix_notas_fiscais_historico_nota_fiscal_entrada_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_notas_fiscais_historico_tenant_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_notas_fiscais_historico_usuario_id");

                    b.ToTable("notas_fiscais_historico", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal?>("AliquotaIpi")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("aliquota_ipi");

                    b.Property<decimal?>("BaseCalculoIcms")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("base_calculo_icms");

                    b.Property<Guid?>("CfopId")
                        .HasColumnType("uuid")
                        .HasColumnName("cfop_id");

                    b.Property<Guid?>("CstCsosnId")
                        .HasColumnType("uuid")
                        .HasColumnName("cst_csosn_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid?>("NcmId")
                        .HasColumnType("uuid")
                        .HasColumnName("ncm_id");

                    b.Property<Guid>("NotaFiscalEntradaId")
                        .HasColumnType("uuid")
                        .HasColumnName("nota_fiscal_entrada_id");

                    b.Property<Guid?>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal?>("QuantidadeComprada")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("quantidade_comprada");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("status");

                    b.Property<int?>("UnidadeId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_id");

                    b.Property<decimal?>("ValorIcms")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_icms");

                    b.Property<decimal?>("ValorIpi")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_ipi");

                    b.Property<decimal?>("ValorUnitario")
                        .HasPrecision(18, 7)
                        .HasColumnType("numeric(18,7)")
                        .HasColumnName("valor_unitario");

                    b.HasKey("Id")
                        .HasName("pk_notas_fiscais_entrada_itens");

                    b.HasIndex("CfopId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_itens_cfop_id");

                    b.HasIndex("CstCsosnId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_itens_cst_csosn_id");

                    b.HasIndex("NcmId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_itens_ncm_id");

                    b.HasIndex("NotaFiscalEntradaId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_itens_nota_fiscal_entrada_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_itens_produto_id");

                    b.ToTable("notas_fiscais_entrada_itens", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaLote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateOnly?>("DataFabricacao")
                        .HasColumnType("date")
                        .HasColumnName("data_fabricacao");

                    b.Property<DateTime?>("DataLancamento")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_lancamento");

                    b.Property<DateOnly?>("DataValidade")
                        .HasColumnType("date")
                        .HasColumnName("data_validade");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid?>("LocalEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_estoque_id");

                    b.Property<Guid>("NotaFiscalEntradaItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("nota_fiscal_entrada_item_id");

                    b.Property<string>("NumeroLote")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("numero_lote");

                    b.Property<decimal?>("Quantidade")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("quantidade");

                    b.Property<int?>("QuantidadeRotulo")
                        .HasPrecision(10, 2)
                        .HasColumnType("integer")
                        .HasColumnName("quantidade_rotulo");

                    b.Property<int?>("UnidadeId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_id");

                    b.HasKey("Id")
                        .HasName("pk_notas_fiscais_entrada_lotes");

                    b.HasIndex("LocalEstoqueId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_lotes_local_estoque_id");

                    b.HasIndex("NotaFiscalEntradaItemId")
                        .HasDatabaseName("ix_notas_fiscais_entrada_lotes_nota_fiscal_entrada_item_id");

                    b.ToTable("notas_fiscais_entrada_lotes", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaLoteInformacaoTecnica", b =>
                {
                    b.Property<Guid>("NotaFiscalEntradaLoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("nota_fiscal_entrada_lote_id");

                    b.Property<decimal?>("ConcentracaoAgua")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("concentracao_agua");

                    b.Property<decimal?>("Densidade")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("densidade");

                    b.Property<decimal?>("DiluicaoFornecedor")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("diluicao_fornecedor");

                    b.Property<decimal?>("FatorConcentracaoAgua")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("fator_concentracao_agua");

                    b.Property<decimal?>("FatorDiluicaoFornecedor")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("fator_diluicao_fornecedor");

                    b.Property<Guid?>("PaisOrigemId")
                        .HasColumnType("uuid")
                        .HasColumnName("pais_origem_id");

                    b.HasKey("NotaFiscalEntradaLoteId")
                        .HasName("pk_nota_fiscal_entrada_lotes_informacao_tecnica");

                    b.HasIndex("PaisOrigemId")
                        .HasDatabaseName("ix_nota_fiscal_entrada_lotes_informacao_tecnica_pais_origem_id");

                    b.ToTable("nota_fiscal_entrada_lotes_informacao_tecnica", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaPedido", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("NotaFiscalEntradaId")
                        .HasColumnType("uuid")
                        .HasColumnName("nota_fiscal_entrada_id");

                    b.Property<Guid>("PedidoCompraId")
                        .HasColumnType("uuid")
                        .HasColumnName("pedido_compra_id");

                    b.HasKey("Id")
                        .HasName("pk_nota_fiscal_entrada_pedidos");

                    b.HasIndex("NotaFiscalEntradaId")
                        .HasDatabaseName("ix_nota_fiscal_entrada_pedidos_nota_fiscal_entrada_id");

                    b.HasIndex("PedidoCompraId")
                        .HasDatabaseName("ix_nota_fiscal_entrada_pedidos_pedido_compra_id");

                    b.ToTable("nota_fiscal_entrada_pedidos", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.OrigemMercadoriaAggregate.OrigemMercadoria", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Codigo")
                        .HasColumnType("integer")
                        .HasColumnName("codigo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_origem_mercadorias");

                    b.ToTable("origem_mercadorias", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime?>("DataLancamento")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_lancamento");

                    b.Property<Guid?>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<decimal?>("Frete")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("frete");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Observacao")
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)")
                        .HasColumnName("observacao");

                    b.Property<DateOnly?>("PrevisaoEntrega")
                        .HasColumnType("date")
                        .HasColumnName("previsao_entrega");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("TipoDesconto")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_desconto");

                    b.Property<decimal?>("ValorAdicional")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("valor_adicional");

                    b.Property<decimal?>("ValorDesconto")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("valor_desconto");

                    b.HasKey("Id")
                        .HasName("pk_pedidos_compra");

                    b.HasIndex("FornecedorId")
                        .HasDatabaseName("ix_pedidos_compra_fornecedor_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_pedidos_compra_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_pedidos_compra_tenant_id");

                    b.ToTable("pedidos_compra", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraHistorico", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("Data")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data");

                    b.Property<bool>("Estorno")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("estorno");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("PedidoCompraId")
                        .HasColumnType("uuid")
                        .HasColumnName("pedido_compra_id");

                    b.Property<int>("StatusAlterado")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("status_alterado");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_pedidos_compra_historico");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_pedidos_compra_historico_group_tenant_id");

                    b.HasIndex("PedidoCompraId")
                        .HasDatabaseName("ix_pedidos_compra_historico_pedido_compra_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_pedidos_compra_historico_tenant_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_pedidos_compra_historico_usuario_id");

                    b.ToTable("pedidos_compra_historico", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraHistoricoAprovacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int>("ItensAprovados")
                        .HasColumnType("integer")
                        .HasColumnName("itens_aprovados");

                    b.Property<int>("ItensReprovados")
                        .HasColumnType("integer")
                        .HasColumnName("itens_reprovados");

                    b.HasKey("Id")
                        .HasName("pk_pedidos_compra_historico_aprovacao");

                    b.ToTable("pedidos_compra_historico_aprovacao", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal?>("DescontoTotal")
                        .HasColumnType("numeric")
                        .HasColumnName("desconto_total");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("PedidoCompraId")
                        .HasColumnType("uuid")
                        .HasColumnName("pedido_compra_id");

                    b.Property<decimal?>("PercentualDesconto")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("percentual_desconto");

                    b.Property<decimal?>("PrecoTotalBruto")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("preco_total_bruto");

                    b.Property<decimal?>("PrecoTotalLiquido")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("preco_total_liquido");

                    b.Property<decimal?>("PrecoUnitario")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("preco_unitario");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal?>("Quantidade")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("quantidade");

                    b.Property<int>("StatusCompra")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("status_compra");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("TipoDesconto")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_desconto");

                    b.Property<int?>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.Property<decimal?>("ValorDescontoUnitario")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("valor_desconto_unitario");

                    b.HasKey("Id")
                        .HasName("pk_pedidos_compra_itens");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_pedidos_compra_itens_group_tenant_id");

                    b.HasIndex("PedidoCompraId")
                        .HasDatabaseName("ix_pedidos_compra_itens_pedido_compra_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_pedidos_compra_itens_produto_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_pedidos_compra_itens_tenant_id");

                    b.ToTable("pedidos_compra_itens", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraItemNotaFiscal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("NotaFiscalEntradaItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("nota_fiscal_entrada_item_id");

                    b.Property<Guid>("PedidoCompraItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("pedido_compra_item_id");

                    b.Property<decimal>("Quantidade")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("quantidade");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_pedidos_compra_itens_notas_fiscais");

                    b.HasIndex("NotaFiscalEntradaItemId")
                        .HasDatabaseName("ix_pedidos_compra_itens_notas_fiscais_nota_fiscal_entrada_item");

                    b.HasIndex("PedidoCompraItemId")
                        .HasDatabaseName("ix_pedidos_compra_itens_notas_fiscais_pedido_compra_item_id");

                    b.ToTable("pedidos_compra_itens_notas_fiscais", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraTotalizador", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<decimal>("TotalPedido")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("total_pedido");

                    b.Property<decimal>("TotalProdutosBruto")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("total_produtos_bruto");

                    b.Property<decimal>("TotalProdutosDescontos")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("total_produtos_descontos");

                    b.Property<decimal>("TotalProdutosLiquido")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("total_produtos_liquido");

                    b.HasKey("Id")
                        .HasName("pk_pedidos_compra_totalizadores");

                    b.ToTable("pedidos_compra_totalizadores", (string)null);
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.TipoFreteAggregate.TipoFrete", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Codigo")
                        .HasColumnType("integer")
                        .HasColumnName("codigo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_tipos_frete");

                    b.ToTable("tipos_frete", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate.AjusteSaldoEstoque", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("DataLancamento")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_lancamento");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("LocalEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_estoque_id");

                    b.Property<Guid>("LoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_id");

                    b.Property<Guid>("MovimentoEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("movimento_estoque_id");

                    b.Property<Guid>("OperadorId")
                        .HasColumnType("uuid")
                        .HasColumnName("operador_id");

                    b.Property<decimal>("QuantidadeDoAjuste")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("quantidade_do_ajuste");

                    b.Property<decimal>("SaldoAnterior")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("saldo_anterior");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoOperacao")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_operacao");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_ajuste_saldo_estoque");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_ajuste_saldo_estoque_group_tenant_id");

                    b.HasIndex("LocalEstoqueId")
                        .HasDatabaseName("ix_ajuste_saldo_estoque_local_estoque_id");

                    b.HasIndex("LoteId")
                        .HasDatabaseName("ix_ajuste_saldo_estoque_lote_id");

                    b.HasIndex("MovimentoEstoqueId")
                        .HasDatabaseName("ix_ajuste_saldo_estoque_movimento_estoque_id");

                    b.HasIndex("OperadorId")
                        .HasDatabaseName("ix_ajuste_saldo_estoque_operador_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_ajuste_saldo_estoque_tenant_id");

                    b.ToTable("ajuste_saldo_estoque", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("CodigoUltimoLancamento")
                        .HasColumnType("integer")
                        .HasColumnName("codigo_ultimo_lancamento");

                    b.Property<DateTime>("DataCriacao")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_criacao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<bool>("OcultarQuantidadeImpressao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("ocultar_quantidade_impressao");

                    b.Property<bool>("OcultarQuantidadeLancamento")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("ocultar_quantidade_lancamento");

                    b.Property<Guid>("ResponsavelId")
                        .HasColumnType("uuid")
                        .HasColumnName("responsavel_id");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.Property<int>("StatusInventario")
                        .HasColumnType("integer")
                        .HasColumnName("status_inventario");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_inventarios");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_inventarios_group_tenant_id");

                    b.HasIndex("ResponsavelId")
                        .HasDatabaseName("ix_inventarios_responsavel_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_inventarios_tenant_id");

                    b.ToTable("inventarios", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioEspecificacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("GrupoId")
                        .HasColumnType("uuid")
                        .HasColumnName("grupo_id");

                    b.Property<Guid>("InventarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("inventario_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid?>("LocalEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_estoque_id");

                    b.Property<Guid?>("SubGrupoId")
                        .HasColumnType("uuid")
                        .HasColumnName("sub_grupo_id");

                    b.HasKey("Id")
                        .HasName("pk_inventario_especificacoes");

                    b.HasIndex("GrupoId")
                        .HasDatabaseName("ix_inventario_especificacoes_grupo_id");

                    b.HasIndex("InventarioId")
                        .HasDatabaseName("ix_inventario_especificacoes_inventario_id");

                    b.HasIndex("LocalEstoqueId")
                        .HasDatabaseName("ix_inventario_especificacoes_local_estoque_id");

                    b.HasIndex("SubGrupoId")
                        .HasDatabaseName("ix_inventario_especificacoes_sub_grupo_id");

                    b.ToTable("inventario_especificacoes", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioHistorico", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("Data")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data");

                    b.Property<Guid>("InventarioLancamentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("inventario_lancamento_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_inventario_historicos");

                    b.HasIndex("InventarioLancamentoId")
                        .HasDatabaseName("ix_inventario_historicos_inventario_lancamento_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_inventario_historicos_usuario_id");

                    b.ToTable("inventario_historicos", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool?>("Aprovado")
                        .HasColumnType("boolean")
                        .HasColumnName("aprovado");

                    b.Property<decimal?>("Diferenca")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("diferenca");

                    b.Property<Guid>("InventarioLancamentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("inventario_lancamento_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("LocalEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_estoque_id");

                    b.Property<Guid>("LoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_id");

                    b.Property<bool>("ProdutoAdicionado")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("produto_adicionado");

                    b.Property<decimal?>("QuantidadeInventariada")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("quantidade_inventariada");

                    b.Property<Guid?>("ResponsavelOperadorId")
                        .HasColumnType("uuid")
                        .HasColumnName("responsavel_operador_id");

                    b.Property<decimal?>("Saldo")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("saldo");

                    b.Property<Guid?>("SaldoEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("saldo_estoque_id");

                    b.Property<int?>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_inventario_itens");

                    b.HasIndex("InventarioLancamentoId")
                        .HasDatabaseName("ix_inventario_itens_inventario_lancamento_id");

                    b.HasIndex("LocalEstoqueId")
                        .HasDatabaseName("ix_inventario_itens_local_estoque_id");

                    b.HasIndex("LoteId")
                        .HasDatabaseName("ix_inventario_itens_lote_id");

                    b.HasIndex("ResponsavelOperadorId")
                        .HasDatabaseName("ix_inventario_itens_responsavel_operador_id");

                    b.HasIndex("SaldoEstoqueId")
                        .HasDatabaseName("ix_inventario_itens_saldo_estoque_id");

                    b.ToTable("inventario_itens", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioLancamento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("CodigoSequencia")
                        .HasColumnType("integer")
                        .HasColumnName("codigo_sequencia");

                    b.Property<Guid>("InventarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("inventario_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int?>("PercentualTolerancia")
                        .HasPrecision(10, 2)
                        .HasColumnType("integer")
                        .HasColumnName("percentual_tolerancia");

                    b.HasKey("Id")
                        .HasName("pk_inventario_lancamentos");

                    b.HasIndex("InventarioId")
                        .HasDatabaseName("ix_inventario_lancamentos_inventario_id");

                    b.ToTable("inventario_lancamentos", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid")
                        .HasColumnName("empresa_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoEstoque")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_estoque");

                    b.HasKey("Id")
                        .HasName("pk_locais_estoque");

                    b.HasIndex("EmpresaId")
                        .HasDatabaseName("ix_locais_estoque_empresa_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_locais_estoque_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_locais_estoque_tenant_id");

                    b.ToTable("locais_estoque", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateOnly>("DataFabricacao")
                        .HasColumnType("date")
                        .HasColumnName("data_fabricacao");

                    b.Property<DateTime>("DataLancamento")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_lancamento");

                    b.Property<DateOnly>("DataValidade")
                        .HasColumnType("date")
                        .HasColumnName("data_validade");

                    b.Property<Guid>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Numero")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("numero");

                    b.Property<int>("NumeroNf")
                        .HasColumnType("integer")
                        .HasColumnName("numero_nf");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<int>("SerieNf")
                        .HasColumnType("integer")
                        .HasColumnName("serie_nf");

                    b.Property<int>("Situacao")
                        .HasColumnType("integer")
                        .HasColumnName("situacao");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_lotes");

                    b.HasIndex("FornecedorId")
                        .HasDatabaseName("ix_lotes_fornecedor_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_lotes_group_tenant_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_lotes_produto_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_lotes_tenant_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_lotes_usuario_id");

                    b.ToTable("lotes", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.LoteInformacaoTecnica", b =>
                {
                    b.Property<Guid>("LoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_id");

                    b.Property<decimal?>("ConcentracaoAgua")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("concentracao_agua");

                    b.Property<decimal>("Densidade")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("densidade");

                    b.Property<decimal>("DiluicaoFornecedor")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("diluicao_fornecedor");

                    b.Property<decimal?>("DiluicaoInterna")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("diluicao_interna");

                    b.Property<decimal?>("FatorConcentracaoAgua")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("fator_concentracao_agua");

                    b.Property<decimal>("FatorDiluicaoFornecedor")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("fator_diluicao_fornecedor");

                    b.Property<decimal?>("FatorDiluicaoInterna")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("fator_diluicao_interna");

                    b.Property<Guid?>("LoteOrigemId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_origem_id");

                    b.Property<Guid?>("PaisOrigemId")
                        .HasColumnType("uuid")
                        .HasColumnName("pais_origem_id");

                    b.HasKey("LoteId")
                        .HasName("pk_lotes_informacao_tecnica");

                    b.HasIndex("LoteOrigemId")
                        .HasDatabaseName("ix_lotes_informacao_tecnica_lote_origem_id");

                    b.HasIndex("PaisOrigemId")
                        .HasDatabaseName("ix_lotes_informacao_tecnica_pais_origem_id");

                    b.ToTable("lotes_informacao_tecnica", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.MotivoPerdaAggregate.MotivoPerda", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("CodigoSngpc")
                        .HasColumnType("integer")
                        .HasColumnName("codigo_sngpc");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_motivos_perda");

                    b.ToTable("motivos_perda", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate.MovimentoEstoque", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("DataLancamento")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_lancamento");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid")
                        .HasColumnName("empresa_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("LocalEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_estoque_id");

                    b.Property<Guid>("LoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_id");

                    b.Property<Guid>("OperacaoEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("operacao_estoque_id");

                    b.Property<decimal>("Quantidade")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("quantidade");

                    b.Property<decimal>("SaldoFinal")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("saldo_final");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoOperacao")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_operacao");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_movimentos_estoque");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_movimentos_estoque_group_tenant_id");

                    b.HasIndex("LocalEstoqueId")
                        .HasDatabaseName("ix_movimentos_estoque_local_estoque_id");

                    b.HasIndex("LoteId")
                        .HasDatabaseName("ix_movimentos_estoque_lote_id");

                    b.HasIndex("OperacaoEstoqueId")
                        .HasDatabaseName("ix_movimentos_estoque_operacao_estoque_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_movimentos_estoque_tenant_id");

                    b.ToTable("movimentos_estoque", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate.OperacaoEstoque", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<bool>("SeAquisicao")
                        .HasColumnType("boolean")
                        .HasColumnName("se_aquisicao");

                    b.Property<bool>("SeInventario")
                        .HasColumnType("boolean")
                        .HasColumnName("se_inventario");

                    b.Property<bool>("SePerda")
                        .HasColumnType("boolean")
                        .HasColumnName("se_perda");

                    b.Property<bool>("SeProjecao")
                        .HasColumnType("boolean")
                        .HasColumnName("se_projecao");

                    b.Property<int>("TipoOperacao")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_operacao");

                    b.HasKey("Id")
                        .HasName("pk_operacoes_estoque");

                    b.ToTable("operacoes_estoque", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate.Perda", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("DataLancamento")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_lancamento");

                    b.Property<DateOnly>("DataPerda")
                        .HasColumnType("date")
                        .HasColumnName("data_perda");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("LocalEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_estoque_id");

                    b.Property<Guid>("LoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_id");

                    b.Property<Guid>("MotivoPerdaId")
                        .HasColumnType("uuid")
                        .HasColumnName("motivo_perda_id");

                    b.Property<Guid>("MovimentoEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("movimento_estoque_id");

                    b.Property<string>("Observacao")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("observacao");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal>("Quantidade")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("quantidade");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_perdas");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_perdas_group_tenant_id");

                    b.HasIndex("LocalEstoqueId")
                        .HasDatabaseName("ix_perdas_local_estoque_id");

                    b.HasIndex("LoteId")
                        .HasDatabaseName("ix_perdas_lote_id");

                    b.HasIndex("MotivoPerdaId")
                        .HasDatabaseName("ix_perdas_motivo_perda_id");

                    b.HasIndex("MovimentoEstoqueId")
                        .HasDatabaseName("ix_perdas_movimento_estoque_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_perdas_produto_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_perdas_tenant_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_perdas_usuario_id");

                    b.ToTable("perdas", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.ProdutoLoteEmUsoAggregate.ProdutoLoteEmUso", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("LocalEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_estoque_id");

                    b.Property<Guid>("LoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_id");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.HasKey("Id")
                        .HasName("pk_produto_lotes_em_uso");

                    b.HasIndex("LocalEstoqueId")
                        .HasDatabaseName("ix_produto_lotes_em_uso_local_estoque_id");

                    b.HasIndex("LoteId")
                        .HasDatabaseName("ix_produto_lotes_em_uso_lote_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_produto_lotes_em_uso_produto_id");

                    b.ToTable("produto_lotes_em_uso", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate.SaldoEstoque", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Bloqueado")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("bloqueado");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid")
                        .HasColumnName("empresa_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("LocalEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_estoque_id");

                    b.Property<Guid>("LoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_id");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal>("Saldo")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("saldo");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_saldos_estoque");

                    b.HasIndex("EmpresaId")
                        .HasDatabaseName("ix_saldos_estoque_empresa_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_saldos_estoque_group_tenant_id");

                    b.HasIndex("LocalEstoqueId")
                        .HasDatabaseName("ix_saldos_estoque_local_estoque_id");

                    b.HasIndex("LoteId")
                        .HasDatabaseName("ix_saldos_estoque_lote_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_saldos_estoque_produto_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_saldos_estoque_tenant_id");

                    b.ToTable("saldos_estoque", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("DataTransferencia")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_transferencia");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("LocalDeEstoqueDestinoId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_de_estoque_destino_id");

                    b.Property<Guid>("LocalDeEstoqueOrigemId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_de_estoque_origem_id");

                    b.Property<string>("Observacao")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("observacao");

                    b.Property<int>("SequenciaNumeroTransferencia")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_numero_transferencia");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_transferencias_lote");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_transferencias_lote_group_tenant_id");

                    b.HasIndex("LocalDeEstoqueDestinoId")
                        .HasDatabaseName("ix_transferencias_lote_local_de_estoque_destino_id");

                    b.HasIndex("LocalDeEstoqueOrigemId")
                        .HasDatabaseName("ix_transferencias_lote_local_de_estoque_origem_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_transferencias_lote_tenant_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_transferencias_lote_usuario_id");

                    b.ToTable("transferencias_lote", (string)null);
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLoteItens", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("LoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_id");

                    b.Property<Guid>("MovimentoEstoqueDestinoId")
                        .HasColumnType("uuid")
                        .HasColumnName("movimento_estoque_destino_id");

                    b.Property<Guid>("MovimentoEstoqueOrigemId")
                        .HasColumnType("uuid")
                        .HasColumnName("movimento_estoque_origem_id");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal>("QuantidadeTransferida")
                        .HasPrecision(18, 9)
                        .HasColumnType("numeric(18,9)")
                        .HasColumnName("quantidade_transferida");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("TransferenciaLoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("transferencia_lote_id");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_transferencias_lote_itens");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_transferencias_lote_itens_group_tenant_id");

                    b.HasIndex("LoteId")
                        .HasDatabaseName("ix_transferencias_lote_itens_lote_id");

                    b.HasIndex("MovimentoEstoqueDestinoId")
                        .HasDatabaseName("ix_transferencias_lote_itens_movimento_estoque_destino_id");

                    b.HasIndex("MovimentoEstoqueOrigemId")
                        .HasDatabaseName("ix_transferencias_lote_itens_movimento_estoque_origem_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_transferencias_lote_itens_produto_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_transferencias_lote_itens_tenant_id");

                    b.HasIndex("TransferenciaLoteId")
                        .HasDatabaseName("ix_transferencias_lote_itens_transferencia_lote_id");

                    b.ToTable("transferencias_lote_itens", (string)null);
                });

            modelBuilder.Entity("Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate.Cidade", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("EstadoId")
                        .HasColumnType("uuid")
                        .HasColumnName("estado_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_cidades");

                    b.HasIndex("EstadoId")
                        .HasDatabaseName("ix_cidades_estado_id");

                    b.ToTable("cidades", (string)null);
                });

            modelBuilder.Entity("Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate.Estado", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Abreviacao")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("abreviacao");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("PaisId")
                        .HasColumnType("uuid")
                        .HasColumnName("pais_id");

                    b.HasKey("Id")
                        .HasName("pk_estados");

                    b.HasIndex("PaisId")
                        .HasDatabaseName("ix_estados_pais_id");

                    b.ToTable("estados", (string)null);
                });

            modelBuilder.Entity("Bootis.Localidade.Domain.AggregatesModel.PaisAggregate.Pais", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Abreviacao")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("abreviacao");

                    b.Property<string>("AbreviacaoIso2")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("abreviacao_iso2");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int>("IsoId")
                        .HasColumnType("integer")
                        .HasColumnName("iso_id");

                    b.Property<string>("en_US")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("en_us");

                    b.Property<string>("pt_BR")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("pt_br");

                    b.HasKey("Id")
                        .HasName("pk_paises");

                    b.ToTable("paises", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate.Conglomerado", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_inclusao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("nome");

                    b.HasKey("Id")
                        .HasName("pk_conglomerados");

                    b.ToTable("conglomerados", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate.ConglomeradoMatriz", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid")
                        .HasColumnName("empresa_id");

                    b.HasKey("Id")
                        .HasName("pk_conglomerado_matriz");

                    b.HasIndex("EmpresaId")
                        .IsUnique()
                        .HasDatabaseName("ix_conglomerado_matriz_empresa_id");

                    b.ToTable("conglomerado_matriz", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.Empresa", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativa")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativa");

                    b.Property<string>("Cnae")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("cnae");

                    b.Property<string>("Cnpj")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("cnpj");

                    b.Property<Guid>("ConglomeradoId")
                        .HasColumnType("uuid")
                        .HasColumnName("conglomerado_id");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("varchar")
                        .HasColumnName("email");

                    b.Property<Guid?>("EmpresaPagadoraId")
                        .HasColumnType("uuid")
                        .HasColumnName("empresa_pagadora_id");

                    b.Property<string>("InscricaoEstadual")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("inscricao_estadual");

                    b.Property<string>("InscricaoMunicipal")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("inscricao_municipal");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("NomeFantasia")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("nome_fantasia");

                    b.Property<string>("RazaoSocial")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("razao_social");

                    b.Property<string>("Site")
                        .HasMaxLength(100)
                        .HasColumnType("varchar")
                        .HasColumnName("site");

                    b.Property<string>("Telefone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("telefone");

                    b.Property<int>("TipoId")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_id");

                    b.HasKey("Id")
                        .HasName("pk_empresas");

                    b.HasIndex("ConglomeradoId")
                        .HasDatabaseName("ix_empresas_conglomerado_id");

                    b.HasIndex("EmpresaPagadoraId")
                        .HasDatabaseName("ix_empresas_empresa_pagadora_id");

                    b.HasIndex("TipoId")
                        .HasDatabaseName("ix_empresas_tipo_id");

                    b.ToTable("empresas", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.EmpresaPagadoraAggregate.EmpresaPagadora", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid")
                        .HasColumnName("empresa_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_empresas_pagadora");

                    b.HasIndex("EmpresaId")
                        .HasDatabaseName("ix_empresas_pagadora_empresa_id");

                    b.ToTable("empresas_pagadora", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate.Grupo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.Property<bool>("SeAtivo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("se_ativo");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_grupos");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_grupos_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_grupos_tenant_id");

                    b.ToTable("grupos", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate.GrupoPermissao", b =>
                {
                    b.Property<Guid>("GrupoId")
                        .HasColumnType("uuid")
                        .HasColumnName("grupo_id");

                    b.Property<int>("PermissaoId")
                        .HasColumnType("integer")
                        .HasColumnName("permissao_id");

                    b.HasKey("GrupoId", "PermissaoId")
                        .HasName("pk_grupos_permissoes");

                    b.ToTable("grupos_permissoes", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.PermissaoUsuario", b =>
                {
                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.Property<int>("PermissaoId")
                        .HasColumnType("integer")
                        .HasColumnName("permissao_id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.HasKey("UsuarioId", "PermissaoId")
                        .HasName("pk_permissoes_usuarios");

                    b.ToTable("permissoes_usuarios", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<string>("Cpf")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("cpf");

                    b.Property<DateOnly?>("DataNascimento")
                        .HasColumnType("date")
                        .HasColumnName("data_nascimento");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("email");

                    b.Property<string>("EmailAlternativo")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("email_alternativo");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid")
                        .HasColumnName("empresa_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.Property<string>("Sobrenome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("sobrenome");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoId")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_id");

                    b.HasKey("Id")
                        .HasName("pk_usuarios");

                    b.HasIndex("EmpresaId")
                        .HasDatabaseName("ix_usuarios_empresa_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_usuarios_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_usuarios_tenant_id");

                    b.HasIndex("TipoId")
                        .HasDatabaseName("ix_usuarios_tipo_id");

                    b.ToTable("usuarios", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.Enumerations.TipoEmpresa", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_tipos_empresa");

                    b.ToTable("tipos_empresa", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.Enumerations.TipoMoeda", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_tipos_moeda");

                    b.ToTable("tipos_moeda", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.Enumerations.TipoTelefone", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_tipos_telefone");

                    b.ToTable("tipos_telefone", (string)null);
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.Enumerations.TipoUsuario", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_tipos_usuario");

                    b.ToTable("tipos_usuario", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<string>("Cnpj")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("cnpj");

                    b.Property<string>("Cpf")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("cpf");

                    b.Property<DateOnly?>("DataNascimento")
                        .HasColumnType("date")
                        .HasColumnName("data_nascimento");

                    b.Property<decimal?>("DescontoFormulas")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("desconto_formulas");

                    b.Property<decimal?>("DescontoProdutosAcabados")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("desconto_produtos_acabados");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("LinkImagem")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("link_imagem");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.Property<string>("Observacao")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("observacao");

                    b.Property<int>("Pessoa")
                        .HasColumnType("integer")
                        .HasColumnName("pessoa");

                    b.Property<string>("RazaoSocial")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("razao_social");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_clientes");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_clientes_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_clientes_tenant_id");

                    b.ToTable("clientes", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteContato", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ClienteId")
                        .HasColumnType("uuid")
                        .HasColumnName("cliente_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Identificacao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("identificacao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Observacao")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("observacao");

                    b.Property<bool>("Principal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("principal");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("TipoContatoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_contato_id");

                    b.HasKey("Id")
                        .HasName("pk_cliente_contatos");

                    b.HasIndex("ClienteId")
                        .HasDatabaseName("ix_cliente_contatos_cliente_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_cliente_contatos_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_cliente_contatos_tenant_id");

                    b.HasIndex("TipoContatoId")
                        .HasDatabaseName("ix_cliente_contatos_tipo_contato_id");

                    b.ToTable("cliente_contatos", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteDocumento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ClienteId")
                        .HasColumnType("uuid")
                        .HasColumnName("cliente_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Identificacao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("identificacao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Observacao")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("observacao");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("TipoDocumentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_documento_id");

                    b.HasKey("Id")
                        .HasName("pk_cliente_documentos");

                    b.HasIndex("ClienteId")
                        .HasDatabaseName("ix_cliente_documentos_cliente_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_cliente_documentos_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_cliente_documentos_tenant_id");

                    b.HasIndex("TipoDocumentoId")
                        .HasDatabaseName("ix_cliente_documentos_tipo_documento_id");

                    b.ToTable("cliente_documentos", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Bairro")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("bairro");

                    b.Property<string>("Cep")
                        .HasMaxLength(9)
                        .HasColumnType("character varying(9)")
                        .HasColumnName("cep");

                    b.Property<Guid?>("CidadeId")
                        .HasColumnType("uuid")
                        .HasColumnName("cidade_id");

                    b.Property<Guid>("ClienteId")
                        .HasColumnType("uuid")
                        .HasColumnName("cliente_id");

                    b.Property<string>("Complemento")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("complemento");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<Guid?>("EstadoId")
                        .HasColumnType("uuid")
                        .HasColumnName("estado_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Logradouro")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("logradouro");

                    b.Property<string>("Numero")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("numero");

                    b.Property<Guid?>("PaisId")
                        .HasColumnType("uuid")
                        .HasColumnName("pais_id");

                    b.Property<bool>("Principal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("principal");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_cliente_enderecos");

                    b.HasIndex("CidadeId")
                        .HasDatabaseName("ix_cliente_enderecos_cidade_id");

                    b.HasIndex("ClienteId")
                        .HasDatabaseName("ix_cliente_enderecos_cliente_id");

                    b.HasIndex("EstadoId")
                        .HasDatabaseName("ix_cliente_enderecos_estado_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_cliente_enderecos_group_tenant_id");

                    b.HasIndex("PaisId")
                        .HasDatabaseName("ix_cliente_enderecos_pais_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_cliente_enderecos_tenant_id");

                    b.ToTable("cliente_enderecos", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.EspecialidadePrescritorAggregate.EspecialidadePrescritor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_especialidades_prescritor");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_especialidades_prescritor_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_especialidades_prescritor_tenant_id");

                    b.ToTable("especialidades_prescritor", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<string>("Cnpj")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("cnpj");

                    b.Property<string>("Cpf")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("cpf");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("LinkImagem")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("link_imagem");

                    b.Property<string>("Nome")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.Property<string>("Observacao")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("observacao");

                    b.Property<string>("RazaoSocial")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("razao_social");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoFornecedorId")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_fornecedor_id");

                    b.Property<int>("TipoPessoa")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_pessoa");

                    b.HasKey("Id")
                        .HasName("pk_fornecedores");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_fornecedores_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_fornecedores_tenant_id");

                    b.HasIndex("TipoFornecedorId")
                        .HasDatabaseName("ix_fornecedores_tipo_fornecedor_id");

                    b.ToTable("fornecedores", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.FornecedorContato", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Identificacao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("identificacao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Observacao")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("observacao");

                    b.Property<bool>("Principal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("principal");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("TipoContatoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_contato_id");

                    b.HasKey("Id")
                        .HasName("pk_fornecedor_contatos");

                    b.HasIndex("FornecedorId")
                        .HasDatabaseName("ix_fornecedor_contatos_fornecedor_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_fornecedor_contatos_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_fornecedor_contatos_tenant_id");

                    b.HasIndex("TipoContatoId")
                        .HasDatabaseName("ix_fornecedor_contatos_tipo_contato_id");

                    b.ToTable("fornecedor_contatos", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.FornecedorDocumento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Identificacao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("identificacao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Observacao")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("observacao");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("TipoDocumentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_documento_id");

                    b.HasKey("Id")
                        .HasName("pk_fornecedor_documentos");

                    b.HasIndex("FornecedorId")
                        .HasDatabaseName("ix_fornecedor_documentos_fornecedor_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_fornecedor_documentos_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_fornecedor_documentos_tenant_id");

                    b.HasIndex("TipoDocumentoId")
                        .HasDatabaseName("ix_fornecedor_documentos_tipo_documento_id");

                    b.ToTable("fornecedor_documentos", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.FornecedorEndereco", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Bairro")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("bairro");

                    b.Property<string>("Cep")
                        .HasMaxLength(9)
                        .HasColumnType("character varying(9)")
                        .HasColumnName("cep");

                    b.Property<Guid?>("CidadeId")
                        .HasColumnType("uuid")
                        .HasColumnName("cidade_id");

                    b.Property<string>("Complemento")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("complemento");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<Guid?>("EstadoId")
                        .HasColumnType("uuid")
                        .HasColumnName("estado_id");

                    b.Property<Guid>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Logradouro")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("logradouro");

                    b.Property<string>("Numero")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("numero");

                    b.Property<Guid?>("PaisId")
                        .HasColumnType("uuid")
                        .HasColumnName("pais_id");

                    b.Property<bool>("Principal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("principal");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_fornecedor_enderecos");

                    b.HasIndex("CidadeId")
                        .HasDatabaseName("ix_fornecedor_enderecos_cidade_id");

                    b.HasIndex("EstadoId")
                        .HasDatabaseName("ix_fornecedor_enderecos_estado_id");

                    b.HasIndex("FornecedorId")
                        .HasDatabaseName("ix_fornecedor_enderecos_fornecedor_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_fornecedor_enderecos_group_tenant_id");

                    b.HasIndex("PaisId")
                        .HasDatabaseName("ix_fornecedor_enderecos_pais_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_fornecedor_enderecos_tenant_id");

                    b.ToTable("fornecedor_enderecos", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.OrgaoExpedidorAggregate.OrgaoExpedidor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("codigo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_orgaos_expedidor");

                    b.ToTable("orgaos_expedidor", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.Prescritor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<string>("CodigoRegistro")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("codigo_registro");

                    b.Property<DateOnly?>("DataNascimento")
                        .HasColumnType("date")
                        .HasColumnName("data_nascimento");

                    b.Property<decimal>("DescontoFormulas")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("desconto_formulas");

                    b.Property<decimal>("DescontoProdutosAcabados")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("desconto_produtos_acabados");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("LinkImagem")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("link_imagem");

                    b.Property<string>("NomeCompleto")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome_completo");

                    b.Property<string>("Observacao")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("observacao");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("TipoRegistroId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_registro_id");

                    b.Property<Guid>("UfRegistroId")
                        .HasColumnType("uuid")
                        .HasColumnName("uf_registro_id");

                    b.HasKey("Id")
                        .HasName("pk_prescritores");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_prescritores_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_prescritores_tenant_id");

                    b.HasIndex("TipoRegistroId")
                        .HasDatabaseName("ix_prescritores_tipo_registro_id");

                    b.HasIndex("UfRegistroId")
                        .HasDatabaseName("ix_prescritores_uf_registro_id");

                    b.ToTable("prescritores", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.PrescritorContato", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Identificacao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("identificacao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Observacao")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("observacao");

                    b.Property<Guid>("PrescritorId")
                        .HasColumnType("uuid")
                        .HasColumnName("prescritor_id");

                    b.Property<bool>("Principal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("principal");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("TipoContatoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_contato_id");

                    b.HasKey("Id")
                        .HasName("pk_prescritor_contatos");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_prescritor_contatos_group_tenant_id");

                    b.HasIndex("PrescritorId")
                        .HasDatabaseName("ix_prescritor_contatos_prescritor_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_prescritor_contatos_tenant_id");

                    b.HasIndex("TipoContatoId")
                        .HasDatabaseName("ix_prescritor_contatos_tipo_contato_id");

                    b.ToTable("prescritor_contatos", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.PrescritorDocumento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Identificacao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("identificacao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Observacao")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("observacao");

                    b.Property<Guid>("PrescritorId")
                        .HasColumnType("uuid")
                        .HasColumnName("prescritor_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("TipoDocumentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_documento_id");

                    b.HasKey("Id")
                        .HasName("pk_prescritor_documentos");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_prescritor_documentos_group_tenant_id");

                    b.HasIndex("PrescritorId")
                        .HasDatabaseName("ix_prescritor_documentos_prescritor_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_prescritor_documentos_tenant_id");

                    b.HasIndex("TipoDocumentoId")
                        .HasDatabaseName("ix_prescritor_documentos_tipo_documento_id");

                    b.ToTable("prescritor_documentos", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.PrescritorEndereco", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Bairro")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("bairro");

                    b.Property<string>("Cep")
                        .HasMaxLength(9)
                        .HasColumnType("character varying(9)")
                        .HasColumnName("cep");

                    b.Property<Guid?>("CidadeId")
                        .HasColumnType("uuid")
                        .HasColumnName("cidade_id");

                    b.Property<string>("Complemento")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("complemento");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<Guid?>("EstadoId")
                        .HasColumnType("uuid")
                        .HasColumnName("estado_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Logradouro")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("logradouro");

                    b.Property<string>("Numero")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("numero");

                    b.Property<Guid?>("PaisId")
                        .HasColumnType("uuid")
                        .HasColumnName("pais_id");

                    b.Property<Guid>("PrescritorId")
                        .HasColumnType("uuid")
                        .HasColumnName("prescritor_id");

                    b.Property<bool>("Principal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("principal");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_prescritor_enderecos");

                    b.HasIndex("CidadeId")
                        .HasDatabaseName("ix_prescritor_enderecos_cidade_id");

                    b.HasIndex("EstadoId")
                        .HasDatabaseName("ix_prescritor_enderecos_estado_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_prescritor_enderecos_group_tenant_id");

                    b.HasIndex("PaisId")
                        .HasDatabaseName("ix_prescritor_enderecos_pais_id");

                    b.HasIndex("PrescritorId")
                        .HasDatabaseName("ix_prescritor_enderecos_prescritor_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_prescritor_enderecos_tenant_id");

                    b.ToTable("prescritor_enderecos", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.TipoRegistro", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Sigla")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("sigla");

                    b.HasKey("Id")
                        .HasName("pk_tipos_registro");

                    b.ToTable("tipos_registro", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.TipoContatoAggregate.TipoContato", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("icon");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.HasKey("Id")
                        .HasName("pk_tipos_contato");

                    b.ToTable("tipos_contato", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.TipoDocumentoAggregate.TipoDocumento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.Property<int?>("Ordem")
                        .HasColumnType("integer")
                        .HasColumnName("ordem");

                    b.Property<int>("Pessoa")
                        .HasColumnType("integer")
                        .HasColumnName("pessoa");

                    b.HasKey("Id")
                        .HasName("pk_tipos_documento");

                    b.ToTable("tipos_documento", (string)null);
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.Enumerations.TipoFornecedor", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_tipos_fornecedor");

                    b.ToTable("tipos_fornecedor", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("Densidade")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("densidade");

                    b.Property<int>("DiasValidade")
                        .HasPrecision(3)
                        .HasColumnType("integer")
                        .HasColumnName("dias_validade");

                    b.Property<decimal>("Diluicao")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("diluicao");

                    b.Property<Guid>("FormaFarmaceuticaId")
                        .HasColumnType("uuid")
                        .HasColumnName("forma_farmaceutica_id");

                    b.Property<int>("FormulaPadraoDesmembramento")
                        .HasColumnType("integer")
                        .HasColumnName("formula_padrao_desmembramento");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Procedimento")
                        .HasMaxLength(40000)
                        .HasColumnType("character varying(40000)")
                        .HasColumnName("procedimento");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal>("QuantidadePadrao")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("quantidade_padrao");

                    b.Property<string>("Rodape")
                        .HasMaxLength(40000)
                        .HasColumnType("character varying(40000)")
                        .HasColumnName("rodape");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_formulas_padrao");

                    b.HasIndex("FormaFarmaceuticaId")
                        .HasDatabaseName("ix_formulas_padrao_forma_farmaceutica_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_formulas_padrao_group_tenant_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_formulas_padrao_produto_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_formulas_padrao_tenant_id");

                    b.ToTable("formulas_padrao", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadraoItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Fase")
                        .HasColumnType("integer")
                        .HasColumnName("fase");

                    b.Property<Guid>("FormulaPadraoId")
                        .HasColumnType("uuid")
                        .HasColumnName("formula_padrao_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal>("Quantidade")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("quantidade");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoItem")
                        .HasPrecision(3)
                        .HasColumnType("integer")
                        .HasColumnName("tipo_item");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_formulas_padrao_item");

                    b.HasIndex("FormulaPadraoId")
                        .HasDatabaseName("ix_formulas_padrao_item_formula_padrao_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_formulas_padrao_item_group_tenant_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_formulas_padrao_item_produto_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_formulas_padrao_item_tenant_id");

                    b.ToTable("formulas_padrao_item", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid")
                        .HasColumnName("empresa_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("LocalEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_estoque_id");

                    b.Property<Guid>("ModeloOrdemManipulacaoId")
                        .HasColumnType("uuid")
                        .HasColumnName("modelo_ordem_manipulacao_id");

                    b.Property<string>("NomeLaboratorio")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nome_laboratorio");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_laboratorios");

                    b.HasIndex("EmpresaId")
                        .HasDatabaseName("ix_laboratorios_empresa_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_laboratorios_group_tenant_id");

                    b.HasIndex("LocalEstoqueId")
                        .HasDatabaseName("ix_laboratorios_local_estoque_id");

                    b.HasIndex("ModeloOrdemManipulacaoId")
                        .HasDatabaseName("ix_laboratorios_modelo_ordem_manipulacao_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_laboratorios_tenant_id");

                    b.ToTable("laboratorios", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.PosologiaAggregate.Posologia", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("FormaFarmaceuticaId")
                        .HasColumnType("uuid")
                        .HasColumnName("forma_farmaceutica_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int>("Periodo")
                        .HasColumnType("integer")
                        .HasColumnName("periodo");

                    b.Property<decimal>("QuantidadeDosePorPeriodo")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("quantidade_dose_por_periodo");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_posologias");

                    b.HasIndex("FormaFarmaceuticaId")
                        .HasDatabaseName("ix_posologias_forma_farmaceutica_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_posologias_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_posologias_tenant_id");

                    b.ToTable("posologias", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CodigoBarras")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("codigo_barras");

                    b.Property<DateTime>("DataEmissao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_emissao");

                    b.Property<DateOnly>("DataPrescricao")
                        .HasColumnType("date")
                        .HasColumnName("data_prescricao");

                    b.Property<DateTime>("DataValidadePrescricao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_validade_prescricao");

                    b.Property<decimal?>("DescontoManual")
                        .HasColumnType("numeric")
                        .HasColumnName("desconto_manual");

                    b.Property<int?>("DuracaoPrevistaReSellDias")
                        .HasColumnType("integer")
                        .HasColumnName("duracao_prevista_re_sell_dias");

                    b.Property<Guid>("FormaFarmaceuticaId")
                        .HasColumnType("uuid")
                        .HasColumnName("forma_farmaceutica_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid?>("ModeloOrdemManipulacaoId")
                        .HasColumnType("uuid")
                        .HasColumnName("modelo_ordem_manipulacao_id");

                    b.Property<Guid?>("ModeloRotuloId")
                        .HasColumnType("uuid")
                        .HasColumnName("modelo_rotulo_id");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<Guid>("PacienteId")
                        .HasColumnType("uuid")
                        .HasColumnName("paciente_id");

                    b.Property<decimal?>("PercentualDescontoManual")
                        .HasColumnType("numeric")
                        .HasColumnName("percentual_desconto_manual");

                    b.Property<int?>("PeriodicidadeReSell")
                        .HasMaxLength(20)
                        .HasColumnType("integer")
                        .HasColumnName("periodicidade_re_sell");

                    b.Property<Guid?>("PosologiaId")
                        .HasColumnType("uuid")
                        .HasColumnName("posologia_id");

                    b.Property<Guid?>("PrescritorId")
                        .HasColumnType("uuid")
                        .HasColumnName("prescritor_id");

                    b.Property<DateTime>("PrevisaoEntrega")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("previsao_entrega");

                    b.Property<DateTime?>("PrevisaoTerminoReSell")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("previsao_termino_re_sell");

                    b.Property<decimal?>("QuantidadeDose")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)")
                        .HasColumnName("quantidade_dose");

                    b.Property<decimal?>("QuantidadeReSell")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)")
                        .HasColumnName("quantidade_re_sell");

                    b.Property<decimal>("QuantidadeReceita")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade_receita");

                    b.Property<int>("QuantidadeRepetir")
                        .HasColumnType("integer")
                        .HasColumnName("quantidade_repetir");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<int>("StatusImpressaoOrdemManipulacao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("status_impressao_ordem_manipulacao");

                    b.Property<int>("StatusImpressaoRotulo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("status_impressao_rotulo");

                    b.Property<int?>("TarjaReceitaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("tarja_receita_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("TipoDescontoManual")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_desconto_manual");

                    b.Property<int?>("TipoUsoContinuo")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_uso_continuo");

                    b.Property<bool>("UsoContinuo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("uso_continuo");

                    b.HasKey("Id")
                        .HasName("pk_receitas_manipuladas");

                    b.HasIndex("FormaFarmaceuticaId")
                        .HasDatabaseName("ix_receitas_manipuladas_forma_farmaceutica_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_receitas_manipuladas_group_tenant_id");

                    b.HasIndex("ModeloOrdemManipulacaoId")
                        .HasDatabaseName("ix_receitas_manipuladas_modelo_ordem_manipulacao_id");

                    b.HasIndex("ModeloRotuloId")
                        .HasDatabaseName("ix_receitas_manipuladas_modelo_rotulo_id");

                    b.HasIndex("PacienteId")
                        .HasDatabaseName("ix_receitas_manipuladas_paciente_id");

                    b.HasIndex("PosologiaId")
                        .HasDatabaseName("ix_receitas_manipuladas_posologia_id");

                    b.HasIndex("PrescritorId")
                        .HasDatabaseName("ix_receitas_manipuladas_prescritor_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_receitas_manipuladas_tenant_id");

                    b.ToTable("receitas_manipuladas", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaBaseCalculo", b =>
                {
                    b.Property<Guid>("ReceitaManipuladaId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_id");

                    b.Property<DateOnly>("DataValidade")
                        .HasColumnType("date")
                        .HasColumnName("data_validade");

                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<decimal>("QuantidadeDivisaoDose")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade_divisao_dose");

                    b.Property<int>("RegraDataValidade")
                        .HasColumnType("integer")
                        .HasColumnName("regra_data_validade");

                    b.Property<int>("RegraEscolhaExcipiente")
                        .HasColumnType("integer")
                        .HasColumnName("regra_escolha_excipiente");

                    b.Property<decimal>("VolumeExcipienteMinimo")
                        .HasColumnType("numeric")
                        .HasColumnName("volume_excipiente_minimo");

                    b.Property<decimal>("VolumeMateriaPrima")
                        .HasColumnType("numeric")
                        .HasColumnName("volume_materia_prima");

                    b.Property<decimal>("VolumeTotal")
                        .HasColumnType("numeric")
                        .HasColumnName("volume_total");

                    b.HasKey("ReceitaManipuladaId")
                        .HasName("pk_receitas_manipuladas_base_calculos");

                    b.ToTable("receitas_manipuladas_base_calculos", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaCalculo", b =>
                {
                    b.Property<Guid>("ReceitaManipuladaRastreioCalculoId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_rastreio_calculo_id");

                    b.Property<decimal?>("Densidade")
                        .HasColumnType("numeric")
                        .HasColumnName("densidade");

                    b.Property<decimal?>("FatorConcentracaoAgua")
                        .HasColumnType("numeric")
                        .HasColumnName("fator_concentracao_agua");

                    b.Property<decimal?>("FatorDiluicaoFornecedor")
                        .HasColumnType("numeric")
                        .HasColumnName("fator_diluicao_fornecedor");

                    b.Property<decimal?>("FatorDiluicaoInterna")
                        .HasColumnType("numeric")
                        .HasColumnName("fator_diluicao_interna");

                    b.Property<decimal?>("FatorEquivalencia")
                        .HasColumnType("numeric")
                        .HasColumnName("fator_equivalencia");

                    b.Property<decimal?>("FatorTotal")
                        .HasColumnType("numeric")
                        .HasColumnName("fator_total");

                    b.Property<Guid?>("LoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_id");

                    b.Property<decimal>("MargemLucro")
                        .HasColumnType("numeric")
                        .HasColumnName("margem_lucro");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal>("QuantidadeCalculada")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade_calculada");

                    b.Property<decimal?>("QuantidadeVolume")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade_volume");

                    b.Property<Guid?>("ReceitaManipuladaId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_id");

                    b.Property<int>("UnidadeMedidaCalculadaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_calculada_id");

                    b.Property<int>("UnidadeMedidaVolumeId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_volume_id");

                    b.Property<decimal>("ValorCusto")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_custo");

                    b.Property<decimal>("ValorVenda")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_venda");

                    b.HasKey("ReceitaManipuladaRastreioCalculoId")
                        .HasName("pk_receitas_manipuladas_calculos");

                    b.HasIndex("LoteId")
                        .HasDatabaseName("ix_receitas_manipuladas_calculos_lote_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_receitas_manipuladas_calculos_produto_id");

                    b.HasIndex("ReceitaManipuladaId")
                        .HasDatabaseName("ix_receitas_manipuladas_calculos_receita_manipulada_id");

                    b.ToTable("receitas_manipuladas_calculos", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaHistorico", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("DataHora")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_hora");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("MotivoCancelamento")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("motivo_cancelamento");

                    b.Property<Guid>("ReceitaManipuladaId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_receitas_manipuladas_historico");

                    b.HasIndex("ReceitaManipuladaId")
                        .HasDatabaseName("ix_receitas_manipuladas_historico_receita_manipulada_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_receitas_manipuladas_historico_usuario_id");

                    b.ToTable("receitas_manipuladas_historico", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("DescricaoProduto")
                        .HasColumnType("text")
                        .HasColumnName("descricao_produto");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<bool>("OcultaRotulo")
                        .HasColumnType("boolean")
                        .HasColumnName("oculta_rotulo");

                    b.Property<int>("Ordem")
                        .HasColumnType("integer")
                        .HasColumnName("ordem");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal>("Quantidade")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade");

                    b.Property<Guid>("ReceitaManipuladaId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_id");

                    b.Property<int>("Tipo")
                        .HasColumnType("integer")
                        .HasColumnName("tipo");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_receitas_manipuladas_item_produto_id");

                    b.HasIndex("ReceitaManipuladaId")
                        .HasDatabaseName("ix_receitas_manipuladas_item_receita_manipulada_id");

                    b.ToTable("receitas_manipuladas_item", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaRastreioCalculo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool?>("FormulaDesmembra")
                        .HasColumnType("boolean")
                        .HasColumnName("formula_desmembra");

                    b.Property<bool>("GeraCalculo")
                        .HasColumnType("boolean")
                        .HasColumnName("gera_calculo");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid?>("LoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("lote_id");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<decimal>("Quantidade")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("quantidade");

                    b.Property<decimal?>("QuantidadeVolume")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade_volume");

                    b.Property<Guid>("ReceitaManipuladaId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_id");

                    b.Property<Guid?>("ReceitaManipuladaItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_item_id");

                    b.Property<Guid?>("ReceitaManipuladaRastreioCalculoId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_rastreio_calculo_id");

                    b.Property<int?>("TipoDesmembramento")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_desmembramento");

                    b.Property<int>("TipoOrigem")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("tipo_origem");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.Property<int?>("UnidadeMedidaVolumeId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_volume_id");

                    b.HasKey("Id")
                        .HasName("pk_receita_manipulada_rastreio_calculos");

                    b.HasIndex("LoteId")
                        .HasDatabaseName("ix_receita_manipulada_rastreio_calculos_lote_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_receita_manipulada_rastreio_calculos_produto_id");

                    b.HasIndex("ReceitaManipuladaId")
                        .HasDatabaseName("ix_receita_manipulada_rastreio_calculos_receita_manipulada_id");

                    b.HasIndex("ReceitaManipuladaItemId")
                        .HasDatabaseName("ix_receita_manipulada_rastreio_calculos_receita_manipulada_ite");

                    b.HasIndex("ReceitaManipuladaRastreioCalculoId")
                        .HasDatabaseName("ix_receita_manipulada_rastreio_calculos_receita_manipulada_ras");

                    b.ToTable("receita_manipulada_rastreio_calculos", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaRotulo", b =>
                {
                    b.Property<Guid>("ReceitaManipuladaId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_id");

                    b.Property<DateTime>("Data")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data");

                    b.Property<DateTime?>("DataEmissao")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_emissao");

                    b.Property<DateOnly?>("DataPrescricao")
                        .HasColumnType("date")
                        .HasColumnName("data_prescricao");

                    b.Property<DateOnly?>("DataValidade")
                        .HasColumnType("date")
                        .HasColumnName("data_validade");

                    b.Property<DateTime?>("DataValidadePrescricao")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_validade_prescricao");

                    b.Property<string>("EnderecoCliente")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("endereco_cliente");

                    b.Property<string>("FormaFarmaceuticaApresentacao")
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)")
                        .HasColumnName("forma_farmaceutica_apresentacao");

                    b.Property<int?>("FormaFarmaceuticaUso")
                        .HasColumnType("integer")
                        .HasColumnName("forma_farmaceutica_uso");

                    b.Property<string>("ItensReceitaDescricao")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("itens_receita_descricao");

                    b.Property<string>("NomeCliente")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome_cliente");

                    b.Property<string>("NomePaciente")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome_paciente");

                    b.Property<string>("ObservacaoReceita")
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)")
                        .HasColumnName("observacao_receita");

                    b.Property<string>("ObservacoesProdutosMateriaPrima")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("observacoes_produtos_materia_prima");

                    b.Property<string>("Posologia")
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)")
                        .HasColumnName("posologia");

                    b.Property<string>("PrescritorCodigoRegistro")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("prescritor_codigo_registro");

                    b.Property<string>("PrescritorNomeCompleto")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("prescritor_nome_completo");

                    b.Property<Guid?>("PrescritorUfRegistroId")
                        .HasColumnType("uuid")
                        .HasColumnName("prescritor_uf_registro_id");

                    b.Property<DateTime?>("PrevisaoEntrega")
                        .HasColumnType("timestamptz")
                        .HasColumnName("previsao_entrega");

                    b.Property<decimal?>("QuantidadeDivisaoDose")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade_divisao_dose");

                    b.Property<decimal?>("QuantidadeDose")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)")
                        .HasColumnName("quantidade_dose");

                    b.Property<decimal?>("QuantidadeReceita")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("quantidade_receita");

                    b.Property<int?>("QuantidadeRepetir")
                        .HasColumnType("integer")
                        .HasColumnName("quantidade_repetir");

                    b.Property<bool>("Repeticao")
                        .HasColumnType("boolean")
                        .HasColumnName("repeticao");

                    b.Property<int?>("SequenciaGroupTenantCliente")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant_cliente");

                    b.Property<int?>("SequenciaGroupTenantPaciente")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant_paciente");

                    b.Property<string>("TelefoneCliente")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("telefone_cliente");

                    b.Property<Guid?>("TipoRegistroId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_registro_id");

                    b.Property<int?>("TipoTarjaMedicamento")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_tarja_medicamento");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("ReceitaManipuladaId")
                        .HasName("pk_receita_manipulada_rotulos");

                    b.HasIndex("PrescritorUfRegistroId")
                        .HasDatabaseName("ix_receita_manipulada_rotulos_prescritor_uf_registro_id");

                    b.HasIndex("TipoRegistroId")
                        .HasDatabaseName("ix_receita_manipulada_rotulos_tipo_registro_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_receita_manipulada_rotulos_usuario_id");

                    b.ToTable("receita_manipulada_rotulos", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaValores", b =>
                {
                    b.Property<Guid>("ReceitaManipuladaId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_id");

                    b.Property<decimal>("CustoOperacional")
                        .HasColumnType("numeric")
                        .HasColumnName("custo_operacional");

                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<decimal>("ValorBruto")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_bruto");

                    b.Property<decimal>("ValorCusto")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_custo");

                    b.Property<decimal>("ValorDesconto")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_desconto");

                    b.Property<decimal>("ValorLucro")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_lucro");

                    b.Property<decimal>("ValorTotal")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_total");

                    b.HasKey("ReceitaManipuladaId")
                        .HasName("pk_receitas_manipuladas_valores");

                    b.ToTable("receitas_manipuladas_valores", (string)null);
                });

            modelBuilder.Entity("Bootis.Shared.Infrastructure.Entities.SequenceControl", b =>
                {
                    b.Property<string>("EntityName")
                        .HasColumnType("text")
                        .HasColumnName("entity_name");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<int>("CurrentSequence")
                        .HasColumnType("integer")
                        .HasColumnName("current_sequence");

                    b.HasKey("EntityName", "TenantId", "GroupTenantId")
                        .HasName("pk_sequence_control");

                    b.ToTable("sequence_control", (string)null);
                });

            modelBuilder.Entity("Bootis.Shared.Infrastructure.Persistence.OutboxMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("OccurredOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("occurred_on");

                    b.Property<string>("Payload")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("payload");

                    b.Property<bool>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<DateTime?>("ProcessedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("processed_on");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("type");

                    b.Property<string>("UserSession")
                        .HasColumnType("jsonb")
                        .HasColumnName("user_data");

                    b.HasKey("Id")
                        .HasName("pk_outbox_messages");

                    b.HasIndex("Processed")
                        .HasDatabaseName("ix_outbox_messages_processed");

                    b.ToTable("outbox_messages", (string)null);
                });

            modelBuilder.Entity("Bootis.Shared.Infrastructure.SeedHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("ExecutedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("executed_at");

                    b.Property<string>("SeedName")
                        .IsRequired()
                        .HasMaxLength(-1)
                        .HasColumnType("text")
                        .HasColumnName("seed_name");

                    b.HasKey("Id")
                        .HasName("pk_seed_history");

                    b.ToTable("seed_history", (string)null);
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.AtendimentoAggregate.Atendimento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AtendenteId")
                        .HasColumnType("uuid")
                        .HasColumnName("atendente_id");

                    b.Property<Guid>("CanalAtendimentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("canal_atendimento_id");

                    b.Property<Guid>("ClienteId")
                        .HasColumnType("uuid")
                        .HasColumnName("cliente_id");

                    b.Property<DateTime>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_atualizacao");

                    b.Property<DateTime>("DataCriacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_criacao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("IdentificadorCanal")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("identificador_canal");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.Property<Guid>("StatusAtendimentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("status_atendimento_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_atendimentos");

                    b.HasIndex("AtendenteId")
                        .HasDatabaseName("ix_atendimentos_atendente_id");

                    b.HasIndex("CanalAtendimentoId")
                        .HasDatabaseName("ix_atendimentos_canal_atendimento_id");

                    b.HasIndex("ClienteId")
                        .HasDatabaseName("ix_atendimentos_cliente_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_atendimentos_group_tenant_id");

                    b.HasIndex("StatusAtendimentoId")
                        .HasDatabaseName("ix_atendimentos_status_atendimento_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_atendimentos_tenant_id");

                    b.ToTable("atendimentos", (string)null);
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.AtendimentoAggregate.CanalAtendimento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("descricao");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("icon");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.HasKey("Id")
                        .HasName("pk_canais_atendimento");

                    b.ToTable("canais_atendimento", (string)null);
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.Entrega", b =>
                {
                    b.Property<Guid>("id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Observacao")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("observacao");

                    b.Property<int>("TipoEntrega")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_entrega");

                    b.HasKey("id");

                    b.ToTable("entregas", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AtendimentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("atendimento_id");

                    b.Property<Guid>("ClienteId")
                        .HasColumnType("uuid")
                        .HasColumnName("cliente_id");

                    b.Property<DateTime>("DataLancamento")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data_lancamento");

                    b.Property<decimal>("Desconto")
                        .HasColumnType("numeric")
                        .HasColumnName("desconto");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<string>("Observacao")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("observacao");

                    b.Property<decimal>("PercentualDesconto")
                        .HasColumnType("numeric")
                        .HasColumnName("percentual_desconto");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoDesconto")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_desconto");

                    b.Property<decimal>("ValorDescontoRateio")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_desconto_rateio");

                    b.Property<decimal>("ValorDescontoTotal")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_desconto_total");

                    b.Property<Guid>("VendedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendedor_id");

                    b.HasKey("Id")
                        .HasName("pk_pedidos_venda");

                    b.HasIndex("AtendimentoId")
                        .IsUnique()
                        .HasDatabaseName("ix_pedidos_venda_atendimento_id");

                    b.HasIndex("ClienteId")
                        .HasDatabaseName("ix_pedidos_venda_cliente_id");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_pedidos_venda_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_pedidos_venda_tenant_id");

                    b.HasIndex("VendedorId")
                        .HasDatabaseName("ix_pedidos_venda_vendedor_id");

                    b.ToTable("pedidos_venda", (string)null);
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaHistorico", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("Data")
                        .HasColumnType("timestamptz")
                        .HasColumnName("data");

                    b.Property<bool>("Estorno")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("estorno");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<Guid>("PedidoVendaId")
                        .HasColumnType("uuid")
                        .HasColumnName("pedido_venda_id");

                    b.Property<int>("StatusAlterado")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("status_alterado");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_pedidos_venda_historico");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_pedidos_venda_historico_group_tenant_id");

                    b.HasIndex("PedidoVendaId")
                        .HasDatabaseName("ix_pedidos_venda_historico_pedido_venda_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_pedidos_venda_historico_tenant_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_pedidos_venda_historico_usuario_id");

                    b.ToTable("pedidos_venda_historico", (string)null);
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("Desconto")
                        .HasColumnType("numeric")
                        .HasColumnName("desconto");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(40000)
                        .HasColumnType("character varying(40000)")
                        .HasColumnName("descricao");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int>("Ordem")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("ordem");

                    b.Property<Guid>("PedidoVendaId")
                        .HasColumnType("uuid")
                        .HasColumnName("pedido_venda_id");

                    b.Property<decimal>("PercentualDesconto")
                        .HasColumnType("numeric")
                        .HasColumnName("percentual_desconto");

                    b.Property<decimal>("Quantidade")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("quantidade");

                    b.Property<bool>("Selecionado")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("selecionado");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<int>("TipoDesconto")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_desconto");

                    b.Property<int>("TipoItem")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_item");

                    b.Property<decimal>("ValorDescontoRateio")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_desconto_rateio");

                    b.Property<decimal>("ValorDescontoUnitario")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("valor_desconto_unitario");

                    b.Property<decimal>("ValorTotalItem")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("valor_total_item");

                    b.Property<decimal>("ValorUnitario")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("valor_unitario");

                    b.HasKey("Id");

                    b.HasIndex("PedidoVendaId")
                        .HasDatabaseName("ix_pedidos_venda_itens_pedido_venda_id");

                    b.ToTable("pedidos_venda_itens", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaTotalizador", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<decimal>("TotalPedido")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("total_pedido");

                    b.Property<decimal>("TotalProdutosBruto")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("total_produtos_bruto");

                    b.Property<decimal>("TotalProdutosDescontosRateio")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("total_produtos_descontos_rateio");

                    b.Property<decimal>("TotalProdutosDescontosUnitarios")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("total_produtos_descontos_unitarios");

                    b.Property<decimal>("TotalProdutosLiquidoComRateio")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("total_produtos_liquido_com_rateio");

                    b.Property<decimal>("TotalProdutosLiquidoSemRateio")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("total_produtos_liquido_sem_rateio");

                    b.HasKey("Id")
                        .HasName("pk_pedidos_venda_totalizadores");

                    b.ToTable("pedidos_venda_totalizadores", (string)null);
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.StatusAtendimentoAggregate.StatusAtendimento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("ativo");

                    b.Property<string>("CorFonte")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("cor_fonte");

                    b.Property<string>("CorFundo")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("cor_fundo");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<bool>("IsRemoved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_removed");

                    b.Property<int>("Ordem")
                        .HasColumnType("integer")
                        .HasColumnName("ordem");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_status_atendimento");

                    b.HasIndex("GroupTenantId")
                        .HasDatabaseName("ix_status_atendimento_group_tenant_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_status_atendimento_tenant_id");

                    b.HasIndex("UsuarioId")
                        .HasDatabaseName("ix_status_atendimento_usuario_id");

                    b.ToTable("status_atendimento", (string)null);
                });

            modelBuilder.Entity("EspecialidadePrescritorPrescritor", b =>
                {
                    b.Property<Guid>("EspecialidadesId")
                        .HasColumnType("uuid")
                        .HasColumnName("especialidade_id");

                    b.Property<Guid>("PrescritoresId")
                        .HasColumnType("uuid")
                        .HasColumnName("prescritor_id");

                    b.HasKey("EspecialidadesId", "PrescritoresId")
                        .HasName("pk_prescritores_especialidades");

                    b.HasIndex("PrescritoresId")
                        .HasDatabaseName("ix_prescritores_especialidades_prescritores_id");

                    b.ToTable("prescritores_especialidades", (string)null);
                });

            modelBuilder.Entity("GrupoUsuario", b =>
                {
                    b.Property<Guid>("GruposId")
                        .HasColumnType("uuid")
                        .HasColumnName("grupos_id");

                    b.Property<Guid>("UsuariosId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuarios_id");

                    b.HasKey("GruposId", "UsuariosId")
                        .HasName("pk_usuarios_grupos");

                    b.HasIndex("UsuariosId")
                        .HasDatabaseName("ix_usuarios_grupos_usuarios_id");

                    b.ToTable("usuarios_grupos", (string)null);
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaItemSinonimo", b =>
                {
                    b.HasBaseType("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaItem");

                    b.Property<decimal?>("FatorCorrecao")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("fator_correcao");

                    b.Property<decimal?>("FatorEquivalencia")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("fator_equivalencia");

                    b.Property<decimal?>("PercentualCorrecao")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("percentual_correcao");

                    b.Property<Guid>("ProdutoSinonimoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_sinonimo_id");

                    b.HasIndex("ProdutoSinonimoId")
                        .HasDatabaseName("ix_receitas_manipuladas_item_sinonimo_produto_sinonimo_id");

                    b.ToTable("receitas_manipuladas_item_sinonimo", (string)null);
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaEntrega", b =>
                {
                    b.HasBaseType("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.Entrega");

                    b.Property<string>("Bairro")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("bairro");

                    b.Property<string>("Cep")
                        .HasMaxLength(9)
                        .HasColumnType("character varying(9)")
                        .HasColumnName("cep");

                    b.Property<string>("CidadeDescricao")
                        .HasColumnType("text")
                        .HasColumnName("cidade_descricao");

                    b.Property<Guid?>("CidadeId")
                        .HasColumnType("uuid")
                        .HasColumnName("cidade_id");

                    b.Property<Guid>("ClienteEnderecoId")
                        .HasColumnType("uuid")
                        .HasColumnName("cliente_endereco_id");

                    b.Property<string>("Complemento")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("complemento");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("descricao");

                    b.Property<string>("EstadoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("estado_descricao");

                    b.Property<Guid?>("EstadoId")
                        .HasColumnType("uuid")
                        .HasColumnName("estado_id");

                    b.Property<string>("Logradouro")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("logradouro");

                    b.Property<string>("Numero")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("numero");

                    b.Property<string>("PaisDescricao")
                        .HasColumnType("text")
                        .HasColumnName("pais_descricao");

                    b.Property<Guid?>("PaisId")
                        .HasColumnType("uuid")
                        .HasColumnName("pais_id");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.Property<decimal>("Taxa")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("taxa");

                    b.Property<decimal>("Troco")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("troco");

                    b.HasIndex("CidadeId")
                        .HasDatabaseName("ix_entregas_pedido_venda_cidade_id");

                    b.HasIndex("ClienteEnderecoId")
                        .HasDatabaseName("ix_entregas_pedido_venda_cliente_endereco_id");

                    b.HasIndex("EstadoId")
                        .HasDatabaseName("ix_entregas_pedido_venda_estado_id");

                    b.HasIndex("PaisId")
                        .HasDatabaseName("ix_entregas_pedido_venda_pais_id");

                    b.ToTable("entregas_pedido_venda", (string)null);
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItemProdutoAcabado", b =>
                {
                    b.HasBaseType("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItem");

                    b.Property<DateTime?>("DataPrescricao")
                        .HasColumnType("date")
                        .HasColumnName("data_prescricao");

                    b.Property<Guid?>("PrescritorId")
                        .HasColumnType("uuid")
                        .HasColumnName("prescritor_id");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.HasIndex("PrescritorId")
                        .HasDatabaseName("ix_pedidos_venda_itens_produto_acabado_prescritor_id");

                    b.HasIndex("ProdutoId")
                        .HasDatabaseName("ix_pedidos_venda_itens_produto_acabado_produto_id");

                    b.ToTable("pedidos_venda_itens_produto_acabado", (string)null);
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItemReceitaManipulada", b =>
                {
                    b.HasBaseType("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItem");

                    b.Property<Guid>("ReceitaManipuladaId")
                        .HasColumnType("uuid")
                        .HasColumnName("receita_manipulada_id");

                    b.HasIndex("ReceitaManipuladaId")
                        .HasDatabaseName("ix_pedidos_venda_itens_receita_manipulada_receita_manipulada_id");

                    b.ToTable("pedidos_venda_itens_receita_manipulada", (string)null);
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.CapsulaProntaAssociacao.CapsulaProntaMateriaPrimaAssociacao", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoCapsulaPronta", "ProdutoCapsulaPronta")
                        .WithMany("CapsulaProntaMateriaPrimaAssociacao")
                        .HasForeignKey("ProdutoCapsulaProntaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_capsula_pronta_materia_prima_associacoes_produtos_capsula_p");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoMateriaPrima", "ProdutoMateriaPrima")
                        .WithMany()
                        .HasForeignKey("ProdutoMateriaPrimaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_capsula_pronta_materia_prima_associacoes_produtos_materia_p");

                    b.Navigation("ProdutoCapsulaPronta");

                    b.Navigation("ProdutoMateriaPrima");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ClasseProdutoAggregate.ClasseProdutoUnidadePrescricao", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ClasseProdutoAggregate.ClasseProduto", "ClasseProduto")
                        .WithMany("UnidadesPrescricao")
                        .HasForeignKey("ClasseProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_classes_produto_unidades_prescricao_classes_produto_classe_");

                    b.Navigation("ClasseProduto");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ClasseProdutoAggregate.ClasseProdutoUnidadeVisualizacao", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ClasseProdutoAggregate.ClasseProduto", "ClasseProduto")
                        .WithMany("UnidadesVisualizacao")
                        .HasForeignKey("ClasseProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_classes_produto_unidades_visualizacao_classes_produto_class");

                    b.Navigation("ClasseProduto");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemAssociacao.EmbalagemAssociacao", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoEmbalagem", "ProdutoEmbalagemAssociada")
                        .WithMany()
                        .HasForeignKey("ProdutoEmbalagemAssociadaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_embalagem_associacoes_produtos_embalagem_produto_embalagem_");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoEmbalagem", "ProdutoEmbalagem")
                        .WithMany("EmbalagemAssociacao")
                        .HasForeignKey("ProdutoEmbalagemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_embalagem_associacoes_produtos_embalagem_produto_embalagem_1");

                    b.Navigation("ProdutoEmbalagem");

                    b.Navigation("ProdutoEmbalagemAssociada");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemAssociacao.EmbalagemCapsulaTamanhoAssociacao", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate.CapsulaTamanho", "CapsulaTamanho")
                        .WithMany()
                        .HasForeignKey("CapsulaTamanhoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_embalagem_capsula_tamanho_associacoes_capsulas_tamanho_caps");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoEmbalagem", "ProdutoEmbalagem")
                        .WithMany("EmbalagemCapsulaTamanhoAssociacao")
                        .HasForeignKey("ProdutoEmbalagemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_embalagem_capsula_tamanho_associacoes_produtos_embalagem_pr");

                    b.Navigation("CapsulaTamanho");

                    b.Navigation("ProdutoEmbalagem");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemAssociacao.ModeloRotuloEmbalagemAssociacao", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica", "FormaFarmaceutica")
                        .WithMany()
                        .HasForeignKey("FormaFarmaceuticaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_modelo_rotulo_embalagem_associacoes_formas_farmaceutica_for");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo", "ModeloRotulo")
                        .WithMany()
                        .HasForeignKey("ModeloRotuloId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_modelo_rotulo_embalagem_associacoes_modelo_rotulo_modelo_ro");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoEmbalagem", "ProdutoEmbalagem")
                        .WithMany("ModeloRotuloEmbalagemAssociacao")
                        .HasForeignKey("ProdutoEmbalagemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_modelo_rotulo_embalagem_associacoes_produtos_embalagem_prod");

                    b.Navigation("FormaFarmaceutica");

                    b.Navigation("ModeloRotulo");

                    b.Navigation("ProdutoEmbalagem");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoFormaFarmaceuticaAggregate.EmbalagemClassificacaoFormaFarmaceutica", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate.EmbalagemClassificacao", "EmbalagemClassificacao")
                        .WithMany("EmbalagemClassificacaoFormaFarmaceutica")
                        .HasForeignKey("EmbalagemClassificacaoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_embalagens_classificacao_forma_farmaceutica_embalagens_clas");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica", "FormaFarmaceutica")
                        .WithMany()
                        .HasForeignKey("FormaFarmaceuticaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_embalagens_classificacao_forma_farmaceutica_forma_farmaceut");

                    b.Navigation("EmbalagemClassificacao");

                    b.Navigation("FormaFarmaceutica");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ModeloOrdemManipulacaoAggregate.ModeloOrdemManipulacao", "ModeloOrdemManipulacao")
                        .WithMany()
                        .HasForeignKey("ModeloOrdemManipulacaoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_formas_farmaceutica_modelo_ordem_manipulacao_modelo_ordem_m");

                    b.Navigation("ModeloOrdemManipulacao");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", "Fornecedor")
                        .WithMany()
                        .HasForeignKey("FornecedorId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("fk_produtos_fornecedor_fornecedor_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.SubGrupoAggregate.SubGrupo", "SubGrupo")
                        .WithMany("Produtos")
                        .HasForeignKey("SubGrupoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_sub_grupo_sub_grupo_id");

                    b.Navigation("Fornecedor");

                    b.Navigation("SubGrupo");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoCapsulaPronta", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate.CapsulaTamanho", "CapsulaTamanho")
                        .WithMany()
                        .HasForeignKey("NumeroCapsulaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_produtos_capsula_pronta_capsulas_tamanho_numero_capsula_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithOne("ProdutoCapsulaPronta")
                        .HasForeignKey("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoCapsulaPronta", "ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_capsula_pronta_produto_produto_id");

                    b.Navigation("CapsulaTamanho");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoEmbalagem", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate.EmbalagemClassificacao", "EmbalagemClassificacao")
                        .WithMany("ProdutoEmbalagem")
                        .HasForeignKey("EmbalagemClassificacaoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_produtos_embalagem_embalagens_classificacao_embalagem_class");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithOne("ProdutoEmbalagem")
                        .HasForeignKey("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoEmbalagem", "ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_embalagem_produto_produto_id");

                    b.Navigation("EmbalagemClassificacao");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoMateriaPrima", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.CasAggregate.Cas", "Cas")
                        .WithMany()
                        .HasForeignKey("CasId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_produtos_materia_prima_cas_cas_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.DcbAggregate.Dcb", "Dcb")
                        .WithMany()
                        .HasForeignKey("DcbId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_produtos_materia_prima_dcbs_dcb_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoMateriaPrima", "ProdutoExcipienteEspecifico")
                        .WithMany()
                        .HasForeignKey("ProdutoExcipienteEspecificoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_produtos_materia_prima_produtos_materia_prima_produto_excip");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithOne("ProdutoMateriaPrima")
                        .HasForeignKey("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoMateriaPrima", "ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_materia_prima_produtos_produto_id");

                    b.Navigation("Cas");

                    b.Navigation("Dcb");

                    b.Navigation("Produto");

                    b.Navigation("ProdutoExcipienteEspecifico");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoTipoCapsula", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate.CapsulaCor", "CapsulaCor")
                        .WithMany()
                        .HasForeignKey("CapsulaCorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_produtos_tipo_capsula_capsulas_cor_capsula_cor_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate.CapsulaTamanho", "CapsulaTamanho")
                        .WithMany()
                        .HasForeignKey("NumeroCapsulaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_produtos_tipo_capsula_capsulas_tamanho_numero_capsula_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithOne("ProdutoTipoCapsula")
                        .HasForeignKey("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoTipoCapsula", "ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_tipo_capsula_produtos_produto_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.TipoCapsulaAggregate.TipoCapsula", "TipoCapsula")
                        .WithMany()
                        .HasForeignKey("TipoCapsulaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_produtos_tipo_capsula_tipo_capsula_tipo_capsula_id");

                    b.Navigation("CapsulaCor");

                    b.Navigation("CapsulaTamanho");

                    b.Navigation("Produto");

                    b.Navigation("TipoCapsula");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAssociadoAggregate.ProdutoAssociado", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica", "FormaFarmaceutica")
                        .WithMany()
                        .HasForeignKey("FormaFarmaceuticaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_associado_formas_farmaceutica_forma_farmaceutica_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "ProdutoAssociacao")
                        .WithMany()
                        .HasForeignKey("ProdutoAssociadoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_associado_produto_produto_associado_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany("ProdutoAssociado")
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_associado_produto_produto_id");

                    b.Navigation("FormaFarmaceutica");

                    b.Navigation("Produto");

                    b.Navigation("ProdutoAssociacao");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoDiluidoAggregate.ProdutoDiluido", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica", "FormaFarmaceutica")
                        .WithMany()
                        .HasForeignKey("FormaFarmaceuticaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_produtos_diluido_formas_farmaceutica_forma_farmaceutica_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany("ProdutoDiluido")
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_diluido_produto_produto_id");

                    b.Navigation("FormaFarmaceutica");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoExcipienteAggregate.ProdutoExcipiente", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoMateriaPrima", "ProdutoMateriaPrima")
                        .WithMany()
                        .HasForeignKey("ProdutoMateriaPrimaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_excipiente_produtos_materia_prima_produto_materia_");

                    b.Navigation("ProdutoMateriaPrima");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate.ProdutoFichaTecnica", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithOne()
                        .HasForeignKey("Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate.ProdutoFichaTecnica", "ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_ficha_tecnica_produtos_produto_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_ficha_tecnica_usuario_usuario_id");

                    b.Navigation("Produto");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate.ProdutoFichaTecnicaEspecificacao", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.BibliografiaAggregate.Bibliografia", "Bibliografia")
                        .WithMany()
                        .HasForeignKey("BibliografiaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_ficha_tecnica_especificacao_bibliografias_bibliogr");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.EnsaioControleQualidadeAggregate.EnsaioControleQualidade", "EnsaioControleQualidade")
                        .WithMany()
                        .HasForeignKey("EnsaioControleQualidadeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_ficha_tecnica_especificacao_ensaios_controle_quali");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate.ProdutoFichaTecnica", "ProdutoFichaTecnica")
                        .WithMany("Especificacoes")
                        .HasForeignKey("ProdutoFichaTecnicaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_ficha_tecnica_especificacao_produtos_ficha_tecnica");

                    b.Navigation("Bibliografia");

                    b.Navigation("EnsaioControleQualidade");

                    b.Navigation("ProdutoFichaTecnica");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate.ProdutoIncompativel", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany("ProdutoIncompativel")
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_incompativel_produtos_produto_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "ProdutoIncompatibilidade")
                        .WithMany()
                        .HasForeignKey("ProdutoIncompativelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_incompativel_produtos_produto_incompativel_id");

                    b.Navigation("Produto");

                    b.Navigation("ProdutoIncompatibilidade");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoMensagemAggregate.ProdutoMensagem", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.MensagemAggregate.Mensagem", "Mensagem")
                        .WithMany()
                        .HasForeignKey("MensagemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_mensagem_produtos_mensagens_mensagem_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany("ProdutoMensagem")
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_mensagem_produtos_produtos_produto_id");

                    b.Navigation("Mensagem");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate.ProdutoSinonimo", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany("ProdutoSinonimo")
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_produtos_sinonimo_produtos_produto_id");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.SubGrupoAggregate.SubGrupo", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.GrupoAggregate.Grupo", "Grupo")
                        .WithMany("SubGrupos")
                        .HasForeignKey("GrupoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_sub_grupos_produto_grupos_grupo_id");

                    b.Navigation("Grupo");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", "Fornecedor")
                        .WithMany()
                        .HasForeignKey("FornecedorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_notas_fiscais_entrada_fornecedor_fornecedor_id");

                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.NaturezaOperacaoAggregate.NaturezaOperacao", "NaturezaOperacao")
                        .WithMany()
                        .HasForeignKey("NaturezaOperacaoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_notas_fiscais_entrada_naturezas_operacao_natureza_operacao_");

                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.TipoFreteAggregate.TipoFrete", "TipoFrete")
                        .WithMany()
                        .HasForeignKey("TipoFreteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_notas_fiscais_entrada_tipo_frete_tipo_frete_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", "Transportadora")
                        .WithMany()
                        .HasForeignKey("TransportadoraId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_notas_fiscais_entrada_fornecedor_transportadora_id");

                    b.Navigation("Fornecedor");

                    b.Navigation("NaturezaOperacao");

                    b.Navigation("TipoFrete");

                    b.Navigation("Transportadora");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaHistorico", b =>
                {
                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada", "NotaFiscalEntrada")
                        .WithMany("NotaFiscalEntradaHistorico")
                        .HasForeignKey("NotaFiscalEntradaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_notas_fiscais_historico_notas_fiscais_entrada_nota_fiscal_e");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_notas_fiscais_historico_usuario_usuario_id");

                    b.Navigation("NotaFiscalEntrada");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaItem", b =>
                {
                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.CfopAggregate.Cfop", "Cfop")
                        .WithMany()
                        .HasForeignKey("CfopId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_notas_fiscais_entrada_itens_cfops_cfop_id");

                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.CstCsosnAggregate.CstCsosn", "Cst")
                        .WithMany()
                        .HasForeignKey("CstCsosnId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_notas_fiscais_entrada_itens_csts_csosn_cst_csosn_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.NcmAggregate.Ncm", "Ncm")
                        .WithMany()
                        .HasForeignKey("NcmId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_notas_fiscais_entrada_itens_ncms_ncm_id");

                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada", "NotaFiscalEntrada")
                        .WithMany("NotaFiscalEntradaItem")
                        .HasForeignKey("NotaFiscalEntradaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_notas_fiscais_entrada_itens_notas_fiscais_entrada_nota_fisc");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_notas_fiscais_entrada_itens_produtos_produto_id");

                    b.Navigation("Cfop");

                    b.Navigation("Cst");

                    b.Navigation("Ncm");

                    b.Navigation("NotaFiscalEntrada");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaLote", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoque")
                        .WithMany()
                        .HasForeignKey("LocalEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_notas_fiscais_entrada_lotes_local_estoque_local_estoque_id");

                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaItem", "NotaFiscalEntradaItem")
                        .WithMany("NotaFiscalEntradaLote")
                        .HasForeignKey("NotaFiscalEntradaItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_notas_fiscais_entrada_lotes_notas_fiscais_entrada_itens_not");

                    b.Navigation("LocalEstoque");

                    b.Navigation("NotaFiscalEntradaItem");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaLoteInformacaoTecnica", b =>
                {
                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaLote", "NotaFiscalEntradaLote")
                        .WithOne("NotaFiscalEntradaLoteInformacaoTecnica")
                        .HasForeignKey("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaLoteInformacaoTecnica", "NotaFiscalEntradaLoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_nota_fiscal_entrada_lotes_informacao_tecnica_notas_fiscais_");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.PaisAggregate.Pais", "PaisOrigem")
                        .WithMany()
                        .HasForeignKey("PaisOrigemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_nota_fiscal_entrada_lotes_informacao_tecnica_pais_pais_orig");

                    b.OwnsMany("Bootis.Compra.Domain.ValuesObject.NotaFiscalEntradaLoteUnidadeAlternativa", "NotaFiscalEntradaLoteUnidadeAlternativa", b1 =>
                        {
                            b1.Property<Guid>("NotaFiscalEntradaLoteInformacaoTecnicaNotaFiscalEntradaLoteId")
                                .HasColumnType("uuid")
                                .HasColumnName("nota_fiscal_entrada_lote_informacao_tecnica_nota_fiscal_entrada_lote_id");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<decimal?>("QuantidadeUnidadeAlternativa")
                                .HasPrecision(18, 9)
                                .HasColumnType("numeric(18,9)")
                                .HasColumnName("quantidade_unidade_alternativa");

                            b1.Property<int?>("UnidadeAlternativaConversaoId")
                                .HasColumnType("integer")
                                .HasColumnName("unidade_alternativa_conversao_id");

                            b1.Property<int?>("UnidadeAlternativaId")
                                .HasColumnType("integer")
                                .HasColumnName("unidade_alternativa_id");

                            b1.HasKey("NotaFiscalEntradaLoteInformacaoTecnicaNotaFiscalEntradaLoteId", "Id")
                                .HasName("pk_nota_fiscal_entrada_lotes_unidade_alternativa");

                            b1.ToTable("nota_fiscal_entrada_lotes_unidade_alternativa", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("NotaFiscalEntradaLoteInformacaoTecnicaNotaFiscalEntradaLoteId")
                                .HasConstraintName("fk_nota_fiscal_entrada_lotes_unidade_alternativa_nota_fiscal_e");
                        });

                    b.Navigation("NotaFiscalEntradaLote");

                    b.Navigation("NotaFiscalEntradaLoteUnidadeAlternativa");

                    b.Navigation("PaisOrigem");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaPedido", b =>
                {
                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada", "NotaFiscalEntrada")
                        .WithMany()
                        .HasForeignKey("NotaFiscalEntradaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_nota_fiscal_entrada_pedidos_notas_fiscais_entrada_nota_fisc");

                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra", "PedidoCompra")
                        .WithMany()
                        .HasForeignKey("PedidoCompraId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_nota_fiscal_entrada_pedidos_pedido_compra_pedido_compra_id");

                    b.Navigation("NotaFiscalEntrada");

                    b.Navigation("PedidoCompra");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", "Fornecedor")
                        .WithMany()
                        .HasForeignKey("FornecedorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_pedidos_compra_fornecedor_fornecedor_id");

                    b.Navigation("Fornecedor");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraHistorico", b =>
                {
                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra", "PedidoCompra")
                        .WithMany("Historico")
                        .HasForeignKey("PedidoCompraId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_compra_historico_pedidos_compra_pedido_compra_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_compra_historico_usuario_usuario_id");

                    b.Navigation("PedidoCompra");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraHistoricoAprovacao", b =>
                {
                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraHistorico", "PedidoCompraHistorico")
                        .WithOne("PedidoCompraHistoricoAprovacao")
                        .HasForeignKey("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraHistoricoAprovacao", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_compra_historico_aprovacao_pedido_compra_historico_");

                    b.Navigation("PedidoCompraHistorico");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraItem", b =>
                {
                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra", "PedidoCompra")
                        .WithMany("Itens")
                        .HasForeignKey("PedidoCompraId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_compra_itens_pedidos_compra_pedido_compra_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_compra_itens_produtos_produto_id");

                    b.Navigation("PedidoCompra");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraItemNotaFiscal", b =>
                {
                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaItem", "NotaFiscalEntradaItem")
                        .WithMany()
                        .HasForeignKey("NotaFiscalEntradaItemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_compra_itens_notas_fiscais_notas_fiscais_entrada_it");

                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraItem", "PedidoCompraItem")
                        .WithMany()
                        .HasForeignKey("PedidoCompraItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_compra_itens_notas_fiscais_pedidos_compra_itens_ped");

                    b.Navigation("NotaFiscalEntradaItem");

                    b.Navigation("PedidoCompraItem");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraTotalizador", b =>
                {
                    b.HasOne("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra", "PedidoCompra")
                        .WithOne("Totalizador")
                        .HasForeignKey("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraTotalizador", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_compra_totalizadores_pedidos_compra_id");

                    b.Navigation("PedidoCompra");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate.AjusteSaldoEstoque", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoque")
                        .WithMany()
                        .HasForeignKey("LocalEstoqueId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_ajuste_saldo_estoque_local_estoque_local_estoque_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "Lote")
                        .WithMany()
                        .HasForeignKey("LoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_ajuste_saldo_estoque_lote_lote_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate.MovimentoEstoque", "MovimentoEstoque")
                        .WithMany("AjustesSaldoEstoque")
                        .HasForeignKey("MovimentoEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_ajuste_saldo_estoque_movimento_estoque_movimento_estoque_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Operador")
                        .WithMany()
                        .HasForeignKey("OperadorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_ajuste_saldo_estoque_usuario_operador_id");

                    b.Navigation("LocalEstoque");

                    b.Navigation("Lote");

                    b.Navigation("MovimentoEstoque");

                    b.Navigation("Operador");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Responsavel")
                        .WithMany()
                        .HasForeignKey("ResponsavelId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_inventarios_usuario_responsavel_id");

                    b.Navigation("Responsavel");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioEspecificacao", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.GrupoAggregate.Grupo", "Grupo")
                        .WithMany()
                        .HasForeignKey("GrupoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_inventario_especificacoes_produto_grupos_grupo_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario", "Inventario")
                        .WithMany("InventarioEspecificacao")
                        .HasForeignKey("InventarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_inventario_especificacoes_inventarios_inventario_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoque")
                        .WithMany()
                        .HasForeignKey("LocalEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_inventario_especificacoes_local_estoque_local_estoque_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.SubGrupoAggregate.SubGrupo", "SubGrupo")
                        .WithMany()
                        .HasForeignKey("SubGrupoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_inventario_especificacoes_sub_grupos_sub_grupo_id");

                    b.Navigation("Grupo");

                    b.Navigation("Inventario");

                    b.Navigation("LocalEstoque");

                    b.Navigation("SubGrupo");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioHistorico", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioLancamento", "InventarioLancamento")
                        .WithMany("InventarioHistorico")
                        .HasForeignKey("InventarioLancamentoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_inventario_historicos_inventario_lancamento_inventario_lanc");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_inventario_historicos_usuario_usuario_id");

                    b.Navigation("InventarioLancamento");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioItem", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioLancamento", "InventarioLancamento")
                        .WithMany("InventarioItem")
                        .HasForeignKey("InventarioLancamentoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_inventario_itens_inventario_lancamento_inventario_lancament");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoque")
                        .WithMany()
                        .HasForeignKey("LocalEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_inventario_itens_local_estoque_local_estoque_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "Lote")
                        .WithMany()
                        .HasForeignKey("LoteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_inventario_itens_lote_lote_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "ResponsavelOperador")
                        .WithMany()
                        .HasForeignKey("ResponsavelOperadorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_inventario_itens_usuario_responsavel_operador_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate.SaldoEstoque", "SaldoEstoque")
                        .WithMany()
                        .HasForeignKey("SaldoEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_inventario_itens_saldo_estoque_saldo_estoque_id");

                    b.Navigation("InventarioLancamento");

                    b.Navigation("LocalEstoque");

                    b.Navigation("Lote");

                    b.Navigation("ResponsavelOperador");

                    b.Navigation("SaldoEstoque");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioLancamento", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario", "Inventario")
                        .WithMany("InventarioLancamento")
                        .HasForeignKey("InventarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_inventario_lancamentos_inventarios_inventario_id");

                    b.Navigation("Inventario");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_locais_estoque_empresa_empresa_id");

                    b.Navigation("Empresa");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", "Fornecedor")
                        .WithMany()
                        .HasForeignKey("FornecedorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_lotes_fornecedor_fornecedor_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_lotes_produtos_produto_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_lotes_usuario_usuario_id");

                    b.Navigation("Fornecedor");

                    b.Navigation("Produto");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.LoteInformacaoTecnica", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "Lote")
                        .WithOne("LoteInformacaoTecnica")
                        .HasForeignKey("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.LoteInformacaoTecnica", "LoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_lotes_informacao_tecnica_lotes_lote_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "LoteOrigem")
                        .WithMany()
                        .HasForeignKey("LoteOrigemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_lotes_informacao_tecnica_lotes_lote_origem_id");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.PaisAggregate.Pais", "Pais")
                        .WithMany()
                        .HasForeignKey("PaisOrigemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_lotes_informacao_tecnica_pais_pais_origem_id");

                    b.OwnsMany("Bootis.Estoque.Domain.ValuesObject.LoteUnidadeAlternativa", "LotesUnidadeAlternativa", b1 =>
                        {
                            b1.Property<Guid>("LoteInformacaoTecnicaId")
                                .HasColumnType("uuid")
                                .HasColumnName("lote_informacao_tecnica_id");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<decimal>("QuantidadeUnidadeAlternativa")
                                .HasPrecision(18, 9)
                                .HasColumnType("numeric(18,9)")
                                .HasColumnName("quantidade_unidade_alternativa");

                            b1.Property<int>("UnidadeAlternativaConversaoId")
                                .HasColumnType("integer")
                                .HasColumnName("unidade_alternativa_conversao_id");

                            b1.Property<int>("UnidadeAlternativaId")
                                .HasColumnType("integer")
                                .HasColumnName("unidade_alternativa_id");

                            b1.HasKey("LoteInformacaoTecnicaId", "Id")
                                .HasName("pk_lotes_unidade_alternativa");

                            b1.ToTable("lotes_unidade_alternativa", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("LoteInformacaoTecnicaId")
                                .HasConstraintName("fk_lotes_unidade_alternativa_lotes_informacao_tecnica_lote_inf");
                        });

                    b.Navigation("Lote");

                    b.Navigation("LoteOrigem");

                    b.Navigation("LotesUnidadeAlternativa");

                    b.Navigation("Pais");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate.MovimentoEstoque", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoque")
                        .WithMany()
                        .HasForeignKey("LocalEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_movimentos_estoque_locais_estoque_local_estoque_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "Lote")
                        .WithMany()
                        .HasForeignKey("LoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_movimentos_estoque_lotes_lote_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate.OperacaoEstoque", "OperacaoEstoque")
                        .WithMany("MovimentosEstoque")
                        .HasForeignKey("OperacaoEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_movimentos_estoque_operacao_estoque_operacao_estoque_id");

                    b.Navigation("LocalEstoque");

                    b.Navigation("Lote");

                    b.Navigation("OperacaoEstoque");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate.Perda", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoque")
                        .WithMany()
                        .HasForeignKey("LocalEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_perdas_locais_estoque_local_estoque_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "Lote")
                        .WithMany()
                        .HasForeignKey("LoteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_perdas_lotes_lote_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.MotivoPerdaAggregate.MotivoPerda", "MotivoPerda")
                        .WithMany("LancamentosPerda")
                        .HasForeignKey("MotivoPerdaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_perdas_motivos_perda_motivo_perda_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate.MovimentoEstoque", "MovimentoEstoque")
                        .WithMany("LancamentosPerda")
                        .HasForeignKey("MovimentoEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_perdas_movimentos_estoque_movimento_estoque_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_perdas_produtos_produto_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_perdas_usuario_usuario_id");

                    b.Navigation("LocalEstoque");

                    b.Navigation("Lote");

                    b.Navigation("MotivoPerda");

                    b.Navigation("MovimentoEstoque");

                    b.Navigation("Produto");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.ProdutoLoteEmUsoAggregate.ProdutoLoteEmUso", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoque")
                        .WithMany()
                        .HasForeignKey("LocalEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_produto_lotes_em_uso_locais_estoque_local_estoque_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "Lote")
                        .WithMany()
                        .HasForeignKey("LoteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_produto_lotes_em_uso_lotes_lote_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_produto_lotes_em_uso_produtos_produto_id");

                    b.Navigation("LocalEstoque");

                    b.Navigation("Lote");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate.SaldoEstoque", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_saldos_estoque_empresa_empresa_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoque")
                        .WithMany("SaldosEstoque")
                        .HasForeignKey("LocalEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_saldos_estoque_locais_estoque_local_estoque_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "Lote")
                        .WithMany()
                        .HasForeignKey("LoteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_saldos_estoque_lotes_lote_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_saldos_estoque_produtos_produto_id");

                    b.Navigation("Empresa");

                    b.Navigation("LocalEstoque");

                    b.Navigation("Lote");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoqueDestino")
                        .WithMany()
                        .HasForeignKey("LocalDeEstoqueDestinoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_transferencias_lote_locais_estoque_local_de_estoque_destino");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoqueOrigem")
                        .WithMany()
                        .HasForeignKey("LocalDeEstoqueOrigemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_transferencias_lote_locais_estoque_local_de_estoque_origem_");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_transferencias_lote_usuario_usuario_id");

                    b.Navigation("LocalEstoqueDestino");

                    b.Navigation("LocalEstoqueOrigem");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLoteItens", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "Lote")
                        .WithMany()
                        .HasForeignKey("LoteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_transferencias_lote_itens_lotes_lote_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate.MovimentoEstoque", "MovimentoEstoqueDestino")
                        .WithMany("TransferenciaLoteItensDestino")
                        .HasForeignKey("MovimentoEstoqueDestinoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_transferencias_lote_itens_movimentos_estoque_movimento_esto");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate.MovimentoEstoque", "MovimentoEstoqueOrigem")
                        .WithMany("TransferenciaLoteItensOrigem")
                        .HasForeignKey("MovimentoEstoqueOrigemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_transferencias_lote_itens_movimentos_estoque_movimento_esto1");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_transferencias_lote_itens_produtos_produto_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote", "TransferenciaLote")
                        .WithMany("TransferenciaLoteItens")
                        .HasForeignKey("TransferenciaLoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_transferencias_lote_itens_transferencias_lote_transferencia");

                    b.Navigation("Lote");

                    b.Navigation("MovimentoEstoqueDestino");

                    b.Navigation("MovimentoEstoqueOrigem");

                    b.Navigation("Produto");

                    b.Navigation("TransferenciaLote");
                });

            modelBuilder.Entity("Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate.Cidade", b =>
                {
                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate.Estado", "Estado")
                        .WithMany("Cidades")
                        .HasForeignKey("EstadoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_cidades_estado_estado_id");

                    b.Navigation("Estado");
                });

            modelBuilder.Entity("Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate.Estado", b =>
                {
                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.PaisAggregate.Pais", "Pais")
                        .WithMany("Estados")
                        .HasForeignKey("PaisId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_estados_pais_pais_id");

                    b.Navigation("Pais");
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate.ConglomeradoMatriz", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.Empresa", "Empresa")
                        .WithOne()
                        .HasForeignKey("Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate.ConglomeradoMatriz", "EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_conglomerado_matriz_empresa_empresa_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate.Conglomerado", "Conglomerado")
                        .WithOne("Matriz")
                        .HasForeignKey("Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate.ConglomeradoMatriz", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_conglomerado_matriz_conglomerados_id");

                    b.Navigation("Conglomerado");

                    b.Navigation("Empresa");
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.Empresa", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate.Conglomerado", "Conglomerado")
                        .WithMany("Empresas")
                        .HasForeignKey("ConglomeradoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_empresas_conglomerados_conglomerado_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.EmpresaPagadoraAggregate.EmpresaPagadora", "EmpresaPagadora")
                        .WithMany()
                        .HasForeignKey("EmpresaPagadoraId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_empresas_empresa_pagadora_empresa_pagadora_id");

                    b.HasOne("Bootis.Organizacional.Domain.Enumerations.TipoEmpresa", null)
                        .WithMany()
                        .HasForeignKey("TipoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_empresas_tipos_empresa_tipo_id");

                    b.OwnsMany("Bootis.Organizacional.Domain.ValuesObject.Telefone", "Telefones", b1 =>
                        {
                            b1.Property<Guid>("EmpresaId")
                                .HasColumnType("uuid")
                                .HasColumnName("empresa_id");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("Numero")
                                .HasMaxLength(14)
                                .HasColumnType("character varying(14)")
                                .HasColumnName("numero");

                            b1.Property<int>("TipoId")
                                .HasColumnType("integer")
                                .HasColumnName("tipo_id");

                            b1.HasKey("EmpresaId", "Id")
                                .HasName("pk_telefones_empresa");

                            b1.HasIndex("TipoId")
                                .HasDatabaseName("ix_telefones_empresa_tipo_id");

                            b1.ToTable("telefones_empresa", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("EmpresaId")
                                .HasConstraintName("fk_telefones_empresa_empresas_empresa_id");

                            b1.HasOne("Bootis.Organizacional.Domain.Enumerations.TipoTelefone", null)
                                .WithMany()
                                .HasForeignKey("TipoId")
                                .OnDelete(DeleteBehavior.Cascade)
                                .HasConstraintName("fk_telefones_empresa_tipos_telefone_tipo_id");
                        });

                    b.OwnsOne("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.EmpresaResponsavel", "Responsavel", b1 =>
                        {
                            b1.Property<Guid>("EmpresaId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<Guid?>("UsuarioId")
                                .HasColumnType("uuid")
                                .HasColumnName("usuario_id");

                            b1.HasKey("EmpresaId")
                                .HasName("pk_empresa_responsavel");

                            b1.HasIndex("UsuarioId")
                                .HasDatabaseName("ix_empresa_responsavel_responsavel_usuario_id");

                            b1.ToTable("empresa_responsavel", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("EmpresaId")
                                .HasConstraintName("fk_empresa_responsavel_empresas_id");

                            b1.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                                .WithMany()
                                .HasForeignKey("UsuarioId")
                                .OnDelete(DeleteBehavior.Restrict)
                                .HasConstraintName("fk_empresa_responsavel_usuario_responsavel_usuario_id");

                            b1.Navigation("Usuario");
                        });

                    b.OwnsOne("Bootis.Organizacional.Domain.ValuesObject.Endereco", "Endereco", b1 =>
                        {
                            b1.Property<Guid>("EmpresaId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Bairro")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("bairro");

                            b1.Property<string>("Cep")
                                .IsRequired()
                                .HasMaxLength(8)
                                .HasColumnType("character varying(8)")
                                .HasColumnName("cep");

                            b1.Property<string>("Cidade")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("cidade");

                            b1.Property<string>("Complemento")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("complemento");

                            b1.Property<string>("Estado")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("estado");

                            b1.Property<string>("Logradouro")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("logradouro");

                            b1.Property<int?>("Numero")
                                .HasColumnType("integer")
                                .HasColumnName("numero");

                            b1.HasKey("EmpresaId")
                                .HasName("pk_endereco_empresa");

                            b1.ToTable("endereco_empresa", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("EmpresaId")
                                .HasConstraintName("fk_endereco_empresa_empresas_id");
                        });

                    b.OwnsOne("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.ConfiguracaoEmpresa", "Configuracao", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .HasColumnType("uuid")
                                .HasColumnName("configuracao_id");

                            b1.Property<bool>("IsRemoved")
                                .HasColumnType("boolean")
                                .HasColumnName("is_removed");

                            b1.Property<int>("TipoMoedaId")
                                .HasColumnType("integer")
                                .HasColumnName("tipo_moeda_id");

                            b1.HasKey("Id")
                                .HasName("pk_configuracoes_empresa");

                            b1.HasIndex("TipoMoedaId")
                                .HasDatabaseName("ix_configuracoes_empresa_configuracao_tipo_moeda_id");

                            b1.ToTable("configuracoes_empresa", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("Id")
                                .HasConstraintName("fk_configuracoes_empresa_empresas_configuracao_id");

                            b1.HasOne("Bootis.Organizacional.Domain.Enumerations.TipoMoeda", null)
                                .WithMany()
                                .HasForeignKey("TipoMoedaId")
                                .OnDelete(DeleteBehavior.Cascade)
                                .IsRequired()
                                .HasConstraintName("fk_configuracoes_empresa_tipos_moeda_tipo_moeda_id");
                        });

                    b.Navigation("Configuracao");

                    b.Navigation("Conglomerado");

                    b.Navigation("EmpresaPagadora");

                    b.Navigation("Endereco");

                    b.Navigation("Responsavel");

                    b.Navigation("Telefones");
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.EmpresaPagadoraAggregate.EmpresaPagadora", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.Empresa", null)
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_empresas_pagadora_empresas_empresa_id");
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate.GrupoPermissao", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate.Grupo", "Grupo")
                        .WithMany("Permissoes")
                        .HasForeignKey("GrupoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_grupos_permissoes_grupos_grupo_id");

                    b.Navigation("Grupo");
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.PermissaoUsuario", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany("Permissoes")
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_permissoes_usuarios_usuario_usuario_id");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.Empresa", "Empresa")
                        .WithMany("Usuarios")
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_usuarios_empresas_empresa_id");

                    b.HasOne("Bootis.Organizacional.Domain.Enumerations.TipoUsuario", null)
                        .WithMany()
                        .HasForeignKey("TipoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_usuarios_tipos_usuario_tipo_id");

                    b.OwnsOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.PreferenciasUsuario", "Preferencias", b1 =>
                        {
                            b1.Property<Guid>("UsuarioId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<bool>("ContrasteAumentado")
                                .HasColumnType("boolean")
                                .HasColumnName("contraste_aumentado");

                            b1.Property<string>("Idioma")
                                .ValueGeneratedOnAdd()
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasDefaultValue("pt-br")
                                .HasColumnName("idioma");

                            b1.Property<string>("PadraoData")
                                .ValueGeneratedOnAdd()
                                .HasMaxLength(15)
                                .HasColumnType("character varying(15)")
                                .HasDefaultValue("dd/MM/yyyy")
                                .HasColumnName("padrao_data");

                            b1.Property<string>("PadraoHora")
                                .ValueGeneratedOnAdd()
                                .HasMaxLength(15)
                                .HasColumnType("character varying(15)")
                                .HasDefaultValue("hh:mm")
                                .HasColumnName("padrao_hora");

                            b1.Property<int>("TemaUsuario")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(1)
                                .HasColumnName("tema_usuario");

                            b1.Property<bool>("TextoAmpliado")
                                .HasColumnType("boolean")
                                .HasColumnName("texto_ampliado");

                            b1.Property<bool>("TextoNegrito")
                                .HasColumnType("boolean")
                                .HasColumnName("texto_negrito");

                            b1.Property<string>("TimeZone")
                                .ValueGeneratedOnAdd()
                                .HasMaxLength(30)
                                .HasColumnType("character varying(30)")
                                .HasDefaultValue("America/Sao_Paulo")
                                .HasColumnName("time_zone");

                            b1.HasKey("UsuarioId")
                                .HasName("pk_preferencias_usuario");

                            b1.ToTable("preferencias_usuario", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("UsuarioId")
                                .HasConstraintName("fk_preferencias_usuario_usuarios_id");
                        });

                    b.OwnsOne("Bootis.Organizacional.Domain.ValuesObject.Telefone", "Celular", b1 =>
                        {
                            b1.Property<Guid>("UsuarioId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Numero")
                                .HasMaxLength(14)
                                .HasColumnType("character varying(14)")
                                .HasColumnName("numero");

                            b1.Property<int>("TipoId")
                                .HasColumnType("integer")
                                .HasColumnName("tipo_id");

                            b1.HasKey("UsuarioId")
                                .HasName("pk_telefone_usuario");

                            b1.HasIndex("TipoId")
                                .HasDatabaseName("ix_telefone_usuario_celular_tipo_id");

                            b1.ToTable("telefone_usuario", (string)null);

                            b1.HasOne("Bootis.Organizacional.Domain.Enumerations.TipoTelefone", null)
                                .WithMany()
                                .HasForeignKey("TipoId")
                                .OnDelete(DeleteBehavior.Cascade)
                                .HasConstraintName("fk_telefone_usuario_tipos_telefone_celular_tipo_id");

                            b1.WithOwner()
                                .HasForeignKey("UsuarioId")
                                .HasConstraintName("fk_telefone_usuario_usuarios_id");
                        });

                    b.Navigation("Celular");

                    b.Navigation("Empresa");

                    b.Navigation("Preferencias");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteContato", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente", "Cliente")
                        .WithMany("Contatos")
                        .HasForeignKey("ClienteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_cliente_contatos_cliente_cliente_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.TipoContatoAggregate.TipoContato", "TipoContato")
                        .WithMany()
                        .HasForeignKey("TipoContatoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_cliente_contatos_tipo_contato_tipo_contato_id");

                    b.Navigation("Cliente");

                    b.Navigation("TipoContato");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteDocumento", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente", "Cliente")
                        .WithMany("Documentos")
                        .HasForeignKey("ClienteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_cliente_documentos_cliente_cliente_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.TipoDocumentoAggregate.TipoDocumento", "TipoDocumento")
                        .WithMany()
                        .HasForeignKey("TipoDocumentoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_cliente_documentos_tipo_documento_tipo_documento_id");

                    b.Navigation("Cliente");

                    b.Navigation("TipoDocumento");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco", b =>
                {
                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate.Cidade", "Cidade")
                        .WithMany()
                        .HasForeignKey("CidadeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_cliente_enderecos_cidades_cidade_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente", "Cliente")
                        .WithMany("Enderecos")
                        .HasForeignKey("ClienteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_cliente_enderecos_cliente_cliente_id");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate.Estado", "Estado")
                        .WithMany()
                        .HasForeignKey("EstadoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_cliente_enderecos_estados_estado_id");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.PaisAggregate.Pais", "Pais")
                        .WithMany()
                        .HasForeignKey("PaisId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_cliente_enderecos_paises_pais_id");

                    b.Navigation("Cidade");

                    b.Navigation("Cliente");

                    b.Navigation("Estado");

                    b.Navigation("Pais");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.Enumerations.TipoFornecedor", "TipoFornecedor")
                        .WithMany()
                        .HasForeignKey("TipoFornecedorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fornecedores_tipos_fornecedor_tipo_fornecedor_id");

                    b.Navigation("TipoFornecedor");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.FornecedorContato", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", "Fornecedor")
                        .WithMany("Contatos")
                        .HasForeignKey("FornecedorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_fornecedor_contatos_fornecedor_fornecedor_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.TipoContatoAggregate.TipoContato", "TipoContato")
                        .WithMany()
                        .HasForeignKey("TipoContatoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fornecedor_contatos_tipo_contato_tipo_contato_id");

                    b.Navigation("Fornecedor");

                    b.Navigation("TipoContato");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.FornecedorDocumento", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", "Fornecedor")
                        .WithMany("Documentos")
                        .HasForeignKey("FornecedorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_fornecedor_documentos_fornecedor_fornecedor_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.TipoDocumentoAggregate.TipoDocumento", "TipoDocumento")
                        .WithMany()
                        .HasForeignKey("TipoDocumentoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fornecedor_documentos_tipo_documento_tipo_documento_id");

                    b.Navigation("Fornecedor");

                    b.Navigation("TipoDocumento");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.FornecedorEndereco", b =>
                {
                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate.Cidade", "Cidade")
                        .WithMany()
                        .HasForeignKey("CidadeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_fornecedor_enderecos_cidades_cidade_id");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate.Estado", "Estado")
                        .WithMany()
                        .HasForeignKey("EstadoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_fornecedor_enderecos_estados_estado_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", "Fornecedor")
                        .WithMany("Enderecos")
                        .HasForeignKey("FornecedorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_fornecedor_enderecos_fornecedor_fornecedor_id");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.PaisAggregate.Pais", "Pais")
                        .WithMany()
                        .HasForeignKey("PaisId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_fornecedor_enderecos_paises_pais_id");

                    b.Navigation("Cidade");

                    b.Navigation("Estado");

                    b.Navigation("Fornecedor");

                    b.Navigation("Pais");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.Prescritor", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.TipoRegistro", "TipoRegistro")
                        .WithMany()
                        .HasForeignKey("TipoRegistroId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_prescritores_tipo_registro_tipo_registro_id");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate.Estado", "UfRegistro")
                        .WithMany()
                        .HasForeignKey("UfRegistroId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_prescritores_estados_uf_registro_id");

                    b.Navigation("TipoRegistro");

                    b.Navigation("UfRegistro");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.PrescritorContato", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.Prescritor", "Prescritor")
                        .WithMany("Contatos")
                        .HasForeignKey("PrescritorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_prescritor_contatos_prescritor_prescritor_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.TipoContatoAggregate.TipoContato", "TipoContato")
                        .WithMany()
                        .HasForeignKey("TipoContatoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_prescritor_contatos_tipo_contato_tipo_contato_id");

                    b.Navigation("Prescritor");

                    b.Navigation("TipoContato");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.PrescritorDocumento", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.Prescritor", "Prescritor")
                        .WithMany("Documentos")
                        .HasForeignKey("PrescritorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_prescritor_documentos_prescritor_prescritor_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.TipoDocumentoAggregate.TipoDocumento", "TipoDocumento")
                        .WithMany()
                        .HasForeignKey("TipoDocumentoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_prescritor_documentos_tipo_documento_tipo_documento_id");

                    b.Navigation("Prescritor");

                    b.Navigation("TipoDocumento");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.PrescritorEndereco", b =>
                {
                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate.Cidade", "Cidade")
                        .WithMany()
                        .HasForeignKey("CidadeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_prescritor_enderecos_cidades_cidade_id");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate.Estado", "Estado")
                        .WithMany()
                        .HasForeignKey("EstadoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_prescritor_enderecos_estados_estado_id");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.PaisAggregate.Pais", "Pais")
                        .WithMany()
                        .HasForeignKey("PaisId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_prescritor_enderecos_paises_pais_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.Prescritor", "Prescritor")
                        .WithMany("Enderecos")
                        .HasForeignKey("PrescritorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_prescritor_enderecos_prescritor_prescritor_id");

                    b.Navigation("Cidade");

                    b.Navigation("Estado");

                    b.Navigation("Pais");

                    b.Navigation("Prescritor");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica", "FormaFarmaceutica")
                        .WithMany()
                        .HasForeignKey("FormaFarmaceuticaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_formulas_padrao_formas_farmaceutica_forma_farmaceutica_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_formulas_padrao_produtos_produto_id");

                    b.Navigation("FormaFarmaceutica");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadraoItem", b =>
                {
                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao", "FormulaPadrao")
                        .WithMany("FormulaPadraoItem")
                        .HasForeignKey("FormulaPadraoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_formulas_padrao_item_formulas_padrao_formula_padrao_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_formulas_padrao_item_produtos_produto_id");

                    b.Navigation("FormulaPadrao");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_laboratorios_empresas_empresa_id");

                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", "LocalEstoque")
                        .WithMany()
                        .HasForeignKey("LocalEstoqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_laboratorios_locais_estoque_local_estoque_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ModeloOrdemManipulacaoAggregate.ModeloOrdemManipulacao", "ModeloOrdemManipulacao")
                        .WithMany()
                        .HasForeignKey("ModeloOrdemManipulacaoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_laboratorios_modelos_ordem_manipulacao_modelo_ordem_manipul");

                    b.Navigation("Empresa");

                    b.Navigation("LocalEstoque");

                    b.Navigation("ModeloOrdemManipulacao");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.PosologiaAggregate.Posologia", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica", "FormaFarmaceutica")
                        .WithMany()
                        .HasForeignKey("FormaFarmaceuticaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_posologias_formas_farmaceutica_forma_farmaceutica_id");

                    b.Navigation("FormaFarmaceutica");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica", "FormaFarmaceutica")
                        .WithMany()
                        .HasForeignKey("FormaFarmaceuticaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_formas_farmaceutica_forma_farmaceutica");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ModeloOrdemManipulacaoAggregate.ModeloOrdemManipulacao", "ModeloOrdemManipulacao")
                        .WithMany()
                        .HasForeignKey("ModeloOrdemManipulacaoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_receitas_manipuladas_modelos_ordem_manipulacao_modelo_ordem");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo", "ModeloRotulo")
                        .WithMany()
                        .HasForeignKey("ModeloRotuloId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_receitas_manipuladas_modelos_rotulo_modelo_rotulo_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente", "Paciente")
                        .WithMany()
                        .HasForeignKey("PacienteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_clientes_paciente_id");

                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.PosologiaAggregate.Posologia", "Posologia")
                        .WithMany()
                        .HasForeignKey("PosologiaId")
                        .HasConstraintName("fk_receitas_manipuladas_posologias_posologia_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.Prescritor", "Prescritor")
                        .WithMany()
                        .HasForeignKey("PrescritorId")
                        .HasConstraintName("fk_receitas_manipuladas_prescritores_prescritor_id");

                    b.Navigation("FormaFarmaceutica");

                    b.Navigation("ModeloOrdemManipulacao");

                    b.Navigation("ModeloRotulo");

                    b.Navigation("Paciente");

                    b.Navigation("Posologia");

                    b.Navigation("Prescritor");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaBaseCalculo", b =>
                {
                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", "ReceitaManipulada")
                        .WithOne("BaseCalculo")
                        .HasForeignKey("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaBaseCalculo", "ReceitaManipuladaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_base_calculos_receita_manipulada_recei");

                    b.Navigation("ReceitaManipulada");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaCalculo", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "Lote")
                        .WithMany()
                        .HasForeignKey("LoteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_receitas_manipuladas_calculos_lotes_lote_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_calculos_produtos_produto_id");

                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", null)
                        .WithMany("CalculosItens")
                        .HasForeignKey("ReceitaManipuladaId")
                        .HasConstraintName("fk_receitas_manipuladas_calculos_receita_manipulada_receita_ma");

                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaRastreioCalculo", "ReceitaManipuladaRastreioCalculo")
                        .WithMany()
                        .HasForeignKey("ReceitaManipuladaRastreioCalculoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_calculos_receita_manipulada_rastreio_c");

                    b.Navigation("Lote");

                    b.Navigation("Produto");

                    b.Navigation("ReceitaManipuladaRastreioCalculo");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaHistorico", b =>
                {
                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", "ReceitaManipulada")
                        .WithMany("Historico")
                        .HasForeignKey("ReceitaManipuladaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_historico_receita_manipulada_receita_m");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_historico_usuarios_usuario_id");

                    b.Navigation("ReceitaManipulada");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaItem", b =>
                {
                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_item_produtos_produto_id");

                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", "ReceitaManipulada")
                        .WithMany("Itens")
                        .HasForeignKey("ReceitaManipuladaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_item_receitas_manipuladas_receita_mani");

                    b.Navigation("Produto");

                    b.Navigation("ReceitaManipulada");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaRastreioCalculo", b =>
                {
                    b.HasOne("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", "Lote")
                        .WithMany()
                        .HasForeignKey("LoteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_receita_manipulada_rastreio_calculos_lotes_lote_id");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_receita_manipulada_rastreio_calculos_produtos_produto_id");

                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", "ReceitaManipulada")
                        .WithMany("RastreioCalculos")
                        .HasForeignKey("ReceitaManipuladaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receita_manipulada_rastreio_calculos_receitas_manipuladas_r");

                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaItem", "ReceitaManipuladaItem")
                        .WithMany()
                        .HasForeignKey("ReceitaManipuladaItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_receita_manipulada_rastreio_calculos_receitas_manipuladas_i");

                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaRastreioCalculo", "ReceitaManipuladaRastreioCalculoOrigem")
                        .WithMany()
                        .HasForeignKey("ReceitaManipuladaRastreioCalculoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_receita_manipulada_rastreio_calculos_receita_manipulada_ras");

                    b.Navigation("Lote");

                    b.Navigation("Produto");

                    b.Navigation("ReceitaManipulada");

                    b.Navigation("ReceitaManipuladaItem");

                    b.Navigation("ReceitaManipuladaRastreioCalculoOrigem");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaRotulo", b =>
                {
                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate.Estado", "PrescritorUfRegistro")
                        .WithMany()
                        .HasForeignKey("PrescritorUfRegistroId")
                        .HasConstraintName("fk_receita_manipulada_rotulos_estados_prescritor_uf_registro_id");

                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", "ReceitaManipulada")
                        .WithOne("EdicaoEtiqueta")
                        .HasForeignKey("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaRotulo", "ReceitaManipuladaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receita_manipulada_rotulos_receitas_manipuladas_receita_man");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.TipoRegistro", "TipoRegistro")
                        .WithMany()
                        .HasForeignKey("TipoRegistroId")
                        .HasConstraintName("fk_receita_manipulada_rotulos_tipos_registro_tipo_registro_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receita_manipulada_rotulos_usuarios_usuario_id");

                    b.Navigation("PrescritorUfRegistro");

                    b.Navigation("ReceitaManipulada");

                    b.Navigation("TipoRegistro");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaValores", b =>
                {
                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", "ReceitaManipulada")
                        .WithOne("Valores")
                        .HasForeignKey("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaValores", "ReceitaManipuladaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_valores_receitas_manipuladas_receita_m");

                    b.Navigation("ReceitaManipulada");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.AtendimentoAggregate.Atendimento", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Atendente")
                        .WithMany()
                        .HasForeignKey("AtendenteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_atendimentos_usuarios_atendente_id");

                    b.HasOne("Bootis.Venda.Domain.AggregatesModel.AtendimentoAggregate.CanalAtendimento", "CanalAtendimento")
                        .WithMany("Atendimentos")
                        .HasForeignKey("CanalAtendimentoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_atendimentos_canal_atendimento_canal_atendimento_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente", "Cliente")
                        .WithMany()
                        .HasForeignKey("ClienteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_atendimentos_clientes_cliente_id");

                    b.HasOne("Bootis.Venda.Domain.AggregatesModel.StatusAtendimentoAggregate.StatusAtendimento", "StatusAtendimento")
                        .WithMany("Atendimentos")
                        .HasForeignKey("StatusAtendimentoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_atendimentos_status_atendimento_status_atendimento_id");

                    b.Navigation("Atendente");

                    b.Navigation("CanalAtendimento");

                    b.Navigation("Cliente");

                    b.Navigation("StatusAtendimento");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.Entrega", b =>
                {
                    b.HasOne("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda", null)
                        .WithOne("Entrega")
                        .HasForeignKey("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.Entrega", "id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_entregas_pedidos_venda_id");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda", b =>
                {
                    b.HasOne("Bootis.Venda.Domain.AggregatesModel.AtendimentoAggregate.Atendimento", "Atendimento")
                        .WithOne()
                        .HasForeignKey("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda", "AtendimentoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_atendimentos_atendimento_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente", "Cliente")
                        .WithMany()
                        .HasForeignKey("ClienteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_clientes_cliente_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", null)
                        .WithMany()
                        .HasForeignKey("VendedorId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_usuarios_vendedor_id");

                    b.Navigation("Atendimento");

                    b.Navigation("Cliente");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaHistorico", b =>
                {
                    b.HasOne("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda", "PedidoVenda")
                        .WithMany("Historico")
                        .HasForeignKey("PedidoVendaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_historico_pedidos_venda_pedido_venda_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", null)
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_historico_usuarios_usuario_id");

                    b.Navigation("PedidoVenda");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItem", b =>
                {
                    b.HasOne("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda", null)
                        .WithMany("Itens")
                        .HasForeignKey("PedidoVendaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_itens_pedidos_venda_pedido_venda_id");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaTotalizador", b =>
                {
                    b.HasOne("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda", "PedidoVenda")
                        .WithOne("Totalizador")
                        .HasForeignKey("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaTotalizador", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_totalizadores_pedidos_venda_id");

                    b.Navigation("PedidoVenda");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.StatusAtendimentoAggregate.StatusAtendimento", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_status_atendimento_usuarios_usuario_id");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("EspecialidadePrescritorPrescritor", b =>
                {
                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.EspecialidadePrescritorAggregate.EspecialidadePrescritor", null)
                        .WithMany()
                        .HasForeignKey("EspecialidadesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_prescritores_especialidades_especialidades_prescritor_espec");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.Prescritor", null)
                        .WithMany()
                        .HasForeignKey("PrescritoresId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_prescritores_especialidades_prescritores_prescritores_id");
                });

            modelBuilder.Entity("GrupoUsuario", b =>
                {
                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate.Grupo", null)
                        .WithMany()
                        .HasForeignKey("GruposId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_usuarios_grupos_grupos_grupos_id");

                    b.HasOne("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", null)
                        .WithMany()
                        .HasForeignKey("UsuariosId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_usuarios_grupos_usuario_usuarios_id");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaItemSinonimo", b =>
                {
                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaItem", null)
                        .WithOne()
                        .HasForeignKey("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipuladaItemSinonimo", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_receitas_manipuladas_item_sinonimo_receitas_manipuladas_ite");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate.ProdutoSinonimo", "ProdutoSinonimo")
                        .WithMany()
                        .HasForeignKey("ProdutoSinonimoId")
                        .HasConstraintName("fk_receitas_manipuladas_item_sinonimo_produtos_sinonimo_produt");

                    b.Navigation("ProdutoSinonimo");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaEntrega", b =>
                {
                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate.Cidade", null)
                        .WithMany()
                        .HasForeignKey("CidadeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_entregas_pedido_venda_cidades_cidade_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco", null)
                        .WithMany()
                        .HasForeignKey("ClienteEnderecoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_entregas_pedido_venda_cliente_enderecos_cliente_endereco_id");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate.Estado", null)
                        .WithMany()
                        .HasForeignKey("EstadoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_entregas_pedido_venda_estados_estado_id");

                    b.HasOne("Bootis.Localidade.Domain.AggregatesModel.PaisAggregate.Pais", null)
                        .WithMany()
                        .HasForeignKey("PaisId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_entregas_pedido_venda_paises_pais_id");

                    b.HasOne("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.Entrega", null)
                        .WithOne()
                        .HasForeignKey("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaEntrega", "id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_entregas_pedido_venda_entregas_id");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItemProdutoAcabado", b =>
                {
                    b.HasOne("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItem", "PedidoVendaItem")
                        .WithOne()
                        .HasForeignKey("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItemProdutoAcabado", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_itens_produto_acabado_pedidos_venda_itens_id");

                    b.HasOne("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.Prescritor", null)
                        .WithMany()
                        .HasForeignKey("PrescritorId")
                        .HasConstraintName("fk_pedidos_venda_itens_produto_acabado_prescritores_prescritor");

                    b.HasOne("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_itens_produto_acabado_produtos_produto_id");

                    b.Navigation("PedidoVendaItem");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItemReceitaManipulada", b =>
                {
                    b.HasOne("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItem", "PedidoVendaItem")
                        .WithOne()
                        .HasForeignKey("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVendaItemReceitaManipulada", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_itens_receita_manipulada_pedidos_venda_itens_");

                    b.HasOne("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", "ReceitaManipulada")
                        .WithMany()
                        .HasForeignKey("ReceitaManipuladaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pedidos_venda_itens_receita_manipulada_receitas_manipuladas");

                    b.Navigation("PedidoVendaItem");

                    b.Navigation("ReceitaManipulada");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ClasseProdutoAggregate.ClasseProduto", b =>
                {
                    b.Navigation("UnidadesPrescricao");

                    b.Navigation("UnidadesVisualizacao");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate.EmbalagemClassificacao", b =>
                {
                    b.Navigation("EmbalagemClassificacaoFormaFarmaceutica");

                    b.Navigation("ProdutoEmbalagem");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.GrupoAggregate.Grupo", b =>
                {
                    b.Navigation("SubGrupos");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto", b =>
                {
                    b.Navigation("ProdutoAssociado");

                    b.Navigation("ProdutoCapsulaPronta");

                    b.Navigation("ProdutoDiluido");

                    b.Navigation("ProdutoEmbalagem");

                    b.Navigation("ProdutoIncompativel");

                    b.Navigation("ProdutoMateriaPrima");

                    b.Navigation("ProdutoMensagem");

                    b.Navigation("ProdutoSinonimo");

                    b.Navigation("ProdutoTipoCapsula");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoCapsulaPronta", b =>
                {
                    b.Navigation("CapsulaProntaMateriaPrimaAssociacao");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.ProdutoEmbalagem", b =>
                {
                    b.Navigation("EmbalagemAssociacao");

                    b.Navigation("EmbalagemCapsulaTamanhoAssociacao");

                    b.Navigation("ModeloRotuloEmbalagemAssociacao");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate.ProdutoFichaTecnica", b =>
                {
                    b.Navigation("Especificacoes");
                });

            modelBuilder.Entity("Bootis.Catalogo.Domain.AggregatesModel.SubGrupoAggregate.SubGrupo", b =>
                {
                    b.Navigation("Produtos");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada", b =>
                {
                    b.Navigation("NotaFiscalEntradaHistorico");

                    b.Navigation("NotaFiscalEntradaItem");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaItem", b =>
                {
                    b.Navigation("NotaFiscalEntradaLote");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntradaLote", b =>
                {
                    b.Navigation("NotaFiscalEntradaLoteInformacaoTecnica");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra", b =>
                {
                    b.Navigation("Historico");

                    b.Navigation("Itens");

                    b.Navigation("Totalizador");
                });

            modelBuilder.Entity("Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompraHistorico", b =>
                {
                    b.Navigation("PedidoCompraHistoricoAprovacao");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario", b =>
                {
                    b.Navigation("InventarioEspecificacao");

                    b.Navigation("InventarioLancamento");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.InventarioLancamento", b =>
                {
                    b.Navigation("InventarioHistorico");

                    b.Navigation("InventarioItem");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque", b =>
                {
                    b.Navigation("SaldosEstoque");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Lote", b =>
                {
                    b.Navigation("LoteInformacaoTecnica");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.MotivoPerdaAggregate.MotivoPerda", b =>
                {
                    b.Navigation("LancamentosPerda");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate.MovimentoEstoque", b =>
                {
                    b.Navigation("AjustesSaldoEstoque");

                    b.Navigation("LancamentosPerda");

                    b.Navigation("TransferenciaLoteItensDestino");

                    b.Navigation("TransferenciaLoteItensOrigem");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate.OperacaoEstoque", b =>
                {
                    b.Navigation("MovimentosEstoque");
                });

            modelBuilder.Entity("Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote", b =>
                {
                    b.Navigation("TransferenciaLoteItens");
                });

            modelBuilder.Entity("Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate.Estado", b =>
                {
                    b.Navigation("Cidades");
                });

            modelBuilder.Entity("Bootis.Localidade.Domain.AggregatesModel.PaisAggregate.Pais", b =>
                {
                    b.Navigation("Estados");
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate.Conglomerado", b =>
                {
                    b.Navigation("Empresas");

                    b.Navigation("Matriz");
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate.Empresa", b =>
                {
                    b.Navigation("Usuarios");
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate.Grupo", b =>
                {
                    b.Navigation("Permissoes");
                });

            modelBuilder.Entity("Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate.Usuario", b =>
                {
                    b.Navigation("Permissoes");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente", b =>
                {
                    b.Navigation("Contatos");

                    b.Navigation("Documentos");

                    b.Navigation("Enderecos");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate.Fornecedor", b =>
                {
                    b.Navigation("Contatos");

                    b.Navigation("Documentos");

                    b.Navigation("Enderecos");
                });

            modelBuilder.Entity("Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate.Prescritor", b =>
                {
                    b.Navigation("Contatos");

                    b.Navigation("Documentos");

                    b.Navigation("Enderecos");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao", b =>
                {
                    b.Navigation("FormulaPadraoItem");
                });

            modelBuilder.Entity("Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada", b =>
                {
                    b.Navigation("BaseCalculo");

                    b.Navigation("CalculosItens");

                    b.Navigation("EdicaoEtiqueta");

                    b.Navigation("Historico");

                    b.Navigation("Itens");

                    b.Navigation("RastreioCalculos");

                    b.Navigation("Valores");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.AtendimentoAggregate.CanalAtendimento", b =>
                {
                    b.Navigation("Atendimentos");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda", b =>
                {
                    b.Navigation("Entrega");

                    b.Navigation("Historico");

                    b.Navigation("Itens");

                    b.Navigation("Totalizador");
                });

            modelBuilder.Entity("Bootis.Venda.Domain.AggregatesModel.StatusAtendimentoAggregate.StatusAtendimento", b =>
                {
                    b.Navigation("Atendimentos");
                });
#pragma warning restore 612, 618
        }
    }
}
