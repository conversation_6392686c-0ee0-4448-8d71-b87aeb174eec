{
  "App": {
    "Name": "<PERSON>arma"
  },
  "ConnectionStrings": {
    "PharmaDBConnection": "Host=bootis-dev-pg.postgres.database.azure.com;Port=5432;Database=pharma;Username=adminbootis;Password=StrongP@ssw0rd",
    //"PharmaDBConnection": "Host=bootis-hml-pg.postgres.database.azure.com;Port=5432;Database=pharma;Username=adminbootis;Password=StrongP@ssw0rd",
    //"PharmaDBConnection": "Host=host.docker.internal;Port=5430;Database=pharma;Username=postgres;Password=********",
    "RabbitMQConnection": ""
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Information",
        "System": "Warning"
      }
    },
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] ({SourceContext}) {Message:lj}{NewLine}{Exception}"
        }
      }
    ]
  },
  "Clients": {
    "Cep": {
      "BaseAddress": "https://tst.bootis.com.br/cep"
    }
  },
  "Security": {
    "ApiKey": "",
    "IdentityServer": "https://tst.bootis.com.br/identity",
    "JwtKey": "4a+Tf9Mw+XnplQ7sNkXtEt+H4rJ1ltbppozxzb7WgI8QB6TxhasmoKC18U42WfSJYSa9orGRtiDx+iE+XKSgAA=="
  },
  "AutoMigrateDatabase": true,
  "AllowedHosts": "*"
}
