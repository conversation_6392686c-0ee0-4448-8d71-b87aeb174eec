using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.Inventario.Atualizar;
using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Estoque.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.Inventario;

public class AdicionarProdutoRequestHandler(
    IUnitOfWork unitOfWork,
    IInventarioRepository inventarioRepository,
    ISaldoEstoqueRepository saldoEstoqueRepository,
    ILocalEstoqueRepository localEstoqueRepository)
    : IRequestHandler<AdicionarProdutoRequest>
{
    public async Task Handle(AdicionarProdutoRequest request, CancellationToken cancellationToken)
    {
        var inventario = await inventarioRepository.ObterInventarioAsync(request.InventarioId);

        if (inventario.StatusInventario != StatusInventario.Lancamento)
            throw new ValidationException(
                Localizer.Instance.GetMessage_Inventario_NaoPodeAdicionarProduto(request.InventarioId));

        var inventarioLancamento =
            inventario.InventarioLancamento.Single(c => c.CodigoSequencia == inventario.CodigoUltimoLancamento);
        var localEstoque = await localEstoqueRepository.ObterLocalEstoqueAsync(request.LocalEstoqueId);

        inventario.ValidarInventarioEspecificacaoPorLocalEstoque(localEstoque);

        var loteIds = request.Produtos.Select(c => c.LoteId);
        var saldosEstoque =
            await saldoEstoqueRepository.ObterSaldosEstoquePorLoteLocalEstoqueAsync(loteIds, localEstoque.Id);

        inventarioLancamento.CadastrarInventarioItensProdutoAdicionado(request.Produtos, saldosEstoque,
            localEstoque.Id);

        inventarioRepository.Update(inventario);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}