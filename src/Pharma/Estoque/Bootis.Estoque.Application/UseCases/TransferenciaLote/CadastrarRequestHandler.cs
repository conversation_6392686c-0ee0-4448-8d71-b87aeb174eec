using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Interfaces;
using Bootis.Estoque.Application.Requests.TransferenciaLote.Cadastrar;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;
using Bootis.Estoque.Resources;
using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.TransferenciaLote;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IServicoEstoque servicoEstoque,
    ITransferenciaLoteRepository transferenciaLoteRepository,
    ILocalEstoqueRepository localEstoqueRepository,
    IUsuarioRepository usuarioRepository,
    IProdutoRepository produtoRepository,
    ILoteRepository loteRepository)
    : IRequestHandler<CadastrarRequest>
{
    public async Task Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        if (request.LocalDeEstoqueOrigemId == request.LocalDeEstoqueDestinoId)
            throw new ValidationException(nameof(request.LocalDeEstoqueDestinoId),
                Localizer.Instance.GetMessage_LocalEstoque_NaoPodeSerIgual());

        var localEstoqueOrigem =
            await localEstoqueRepository.ObterLocalEstoqueAsync(request.LocalDeEstoqueOrigemId);
        var localEstoqueDestino =
            await localEstoqueRepository.ObterLocalEstoqueAsync(request.LocalDeEstoqueDestinoId);
        var usuario = await usuarioRepository.ObterUsuarioAsync(userContext.UserId);

        var transferenciaLote = new Domain.AggregatesModel.TransferenciaLoteAggregate.TransferenciaLote(usuario.Id,
            localEstoqueOrigem,
            localEstoqueDestino,
            request.Observacao);

        transferenciaLoteRepository.Add(transferenciaLote);

        var produtos =
            await produtoRepository.ObterProdutosAsync(
                request.TransferenciaLoteItens.Select(c => c.ProdutoId));
        var lotes = await loteRepository.ObterLotesAsync(request.TransferenciaLoteItens.Select(c => c.LoteId));

        foreach (var item in request.TransferenciaLoteItens)
        {
            var produto = produtos.Single(c => c.Id == item.ProdutoId);
            var lote = lotes.Single(c => c.Id == item.LoteId);

            var estoqueDisponivel = await servicoEstoque.VerificarEstoqueDisponivelAsync(
                lote.Id, localEstoqueOrigem.Id, item.QuantidadeTransferida);

            if (!estoqueDisponivel)
                throw new ValidationException(nameof(item.QuantidadeTransferida),
                    $"Estoque insuficiente para transferir {item.QuantidadeTransferida} unidades do produto {produto.Descricao}");

            var transacaoSaidaId = await servicoEstoque.RegistrarMovimentoSaidaAsync(
                produto.Id,
                lote.Id,
                localEstoqueOrigem.Id,
                item.QuantidadeTransferida,
                item.UnidadeMedidaId,
                usuario.Id,
                $"Transferência {transferenciaLote.Id}",
                cancellationToken);

            var transacaoEntradaId = await servicoEstoque.RegistrarMovimentoEntradaAsync(
                produto.Id,
                lote.Id,
                localEstoqueDestino.Id,
                item.QuantidadeTransferida,
                item.UnidadeMedidaId,
                usuario.Id,
                $"Transferência {transferenciaLote.Id}",
                cancellationToken);

            var transferenciaLoteItens = transferenciaLote
                .AdicionarTransferenciaLoteItem(produto, lote, transacaoSaidaId, transacaoEntradaId, item);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}