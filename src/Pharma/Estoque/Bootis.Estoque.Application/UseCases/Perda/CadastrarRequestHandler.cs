using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Interfaces;
using Bootis.Estoque.Application.Requests.Perda.Cadastrar;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.MotivoPerdaAggregate;
using Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;
using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.Perda;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IServicoEstoque servicoEstoque,
    IPerdaRepository perdaRepository,
    ILoteRepository loteRepository,
    IMotivoPerdaRepository motivoPerdaRepository,
    ILocalEstoqueRepository localEstoqueRepository,
    IUsuarioRepository usuarioRepository,
    IProdutoRepository produtoRepository)
    : IRequestHandler<CadastrarRequest>
{
    public async Task Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var usuario = await usuarioRepository.ObterUsuarioAsync(userContext.UserId);
        var lote = await loteRepository.ObterLoteAsync(request.LoteId);
        var localEstoque = await localEstoqueRepository.ObterLocalEstoqueAsync(request.LocalEstoqueId);
        var produto = await produtoRepository.ObterProdutoAsync(request.ProdutoId);
        var motivoPerda = await motivoPerdaRepository.ObterMotivoPerdaAsync(request.MotivoPerdaId);

        var estoqueDisponivel = await servicoEstoque.VerificarEstoqueDisponivelAsync(
            request.LoteId, request.LocalEstoqueId, request.Quantidade);

        if (!estoqueDisponivel)
            throw new InvalidOperationException("Estoque insuficiente para registrar a perda");

        var transacaoId = await servicoEstoque.RegistrarPerdaAsync(
            produto.Id,
            request.LoteId,
            request.LocalEstoqueId,
            request.Quantidade,
            request.UnidadeMedidaId,
            $"Perda - {motivoPerda.Descricao}: {request.Observacao}",
            usuario.Id,
            cancellationToken);

        var lancamentoPerda = new Domain.AggregatesModel.PerdaAggregate.Perda(
            request.DataPerda,
            lote.Id,
            produto.Id,
            localEstoque.Id,
            request.Quantidade,
            request.UnidadeMedidaId,
            motivoPerda.Id,
            request.Observacao,
            usuario.Id,
            transacaoId);

        perdaRepository.Add(lancamentoPerda);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}