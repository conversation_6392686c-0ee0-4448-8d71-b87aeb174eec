using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Application.Requests.ProjecaoEstoque.Atualizar;
using Bootis.Estoque.Domain.AggregatesModel.LivroRazaoEstoqueAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.ProjecaoEstoque;

public class AplicarProjecaoRequestHandler(
    IUnitOfWork unitOfWork,
    IProdutoRepository produtoRepository,
    ILivroRazaoEstoqueRepository livroRazaoRepository)
    : IRequestHandler<AplicarProjecaoRequest>
{
    public async Task Handle(AplicarProjecaoRequest request, CancellationToken cancellationToken)
    {
        var produtosSelecionadosIds = request.ProdutoIds;
        if (produtosSelecionadosIds?.Any() is not true) return;

        var produtosParaProjetar =
            await produtoRepository.ObterProdutosComGrupoAsync(produtosSelecionadosIds, true, cancellationToken);
        if (!produtosParaProjetar.Any()) return;

        var dataHoje = DateOnly.FromDateTime(DateTime.UtcNow);

        int? diasEstoqueMinimo = request.PeriodoProjecao != null
            ? (request.PeriodoProjecao.DataFim.ToDateTime(TimeOnly.MinValue) -
               request.PeriodoProjecao.DataInicio.ToDateTime(TimeOnly.MinValue)).Days
            : null;

        var diasEstoqueMaximo = diasEstoqueMinimo;

        int? diasInicioProjecao = request.PeriodoAnalise != null
            ? (dataHoje.ToDateTime(TimeOnly.MinValue) -
               request.PeriodoAnalise.DataInicio.ToDateTime(TimeOnly.MinValue)).Days
            : null;

        int? diasFimProjecao = request.PeriodoAnalise != null
            ? (dataHoje.ToDateTime(TimeOnly.MinValue) -
               request.PeriodoAnalise.DataFim.ToDateTime(TimeOnly.MinValue)).Days
            : null;

        var projecaoEngine = new Domain.AggregatesModel.ProjecaoEstoque.ProjecaoEstoque(
            produtosParaProjetar,
            true,
            false,
            diasEstoqueMinimo,
            diasEstoqueMaximo,
            diasInicioProjecao,
            diasFimProjecao
        );

        var movimentacoes = await livroRazaoRepository.ObterSaidasPeriodoAsync(
            produtosParaProjetar.Select(p => p.Id),
            projecaoEngine.DataInicioMovimentacoes,
            projecaoEngine.DataFimMovimentacoes
        );

        var resultadosProjecao = projecaoEngine.Calcular(movimentacoes.ToList());
        if (!resultadosProjecao.Any()) return;

        var dataAplicacao = DateOnly.FromDateTime(DateTime.UtcNow);

        foreach (var resultado in resultadosProjecao)
        {
            var produto = produtosParaProjetar.FirstOrDefault(p => p.Id == resultado.ProdutoId);
            if (produto is null) continue;

            produto.AplicarProjecao(
                resultado.EstoqueMinimoProjetado,
                resultado.EstoqueMaximoProjetado,
                resultado.MediaConsumoDiaria,
                dataAplicacao
            );
        }

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}