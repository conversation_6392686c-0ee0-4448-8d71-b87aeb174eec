using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Application.Dtos.ProjecaoEstoque;
using Bootis.Estoque.Domain.AggregatesModel.LivroRazaoEstoqueAggregate;
using MediatR;
using ProdutoAggregate = Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto;

namespace Bootis.Estoque.Application.UseCases.ProjecaoEstoque;

public class ProjecaoEstoqueAutomaticaHandler(
    IProdutoRepository produtoRepository,
    ILivroRazaoEstoqueRepository livroRazaoRepository)
    : IRequestHandler<FiltroProjecaoAutomaticaDto, ResultadoProjecaoEstoqueDto>
{
    public async Task<ResultadoProjecaoEstoqueDto> Handle(FiltroProjecaoAutomaticaDto filtro,
        CancellationToken cancellationToken)
    {
        var produtos = await ObterProdutosAsync(filtro, cancellationToken);
        if (!produtos.Any())
            return new ResultadoProjecaoEstoqueDto();

        var projecao = new Domain.AggregatesModel.ProjecaoEstoque.ProjecaoEstoque(
            produtos,
            false,
            true,
            null,
            null,
            null,
            null
        );

        return await Executar(projecao, produtos);
    }

    private async Task<ResultadoProjecaoEstoqueDto> Executar(
        Domain.AggregatesModel.ProjecaoEstoque.ProjecaoEstoque projecao, List<ProdutoAggregate> produtos)
    {
        var movimentacoes = await livroRazaoRepository.ObterSaidasPeriodoAsync(
            produtos.Select(p => p.Id),
            projecao.DataInicioMovimentacoes,
            projecao.DataFimMovimentacoes
        );

        if (projecao.RequerDadosManuais)
            return new ResultadoProjecaoEstoqueDto
            {
                RequerDadosManuais = true,
                Produtos = new List<ProdutoProjecaoEstoqueDto>()
            };

        var resultados = projecao.Calcular(movimentacoes.ToList());

        return new ResultadoProjecaoEstoqueDto
        {
            RequerDadosManuais = projecao.RequerDadosManuais,
            Produtos = resultados.Select(r => new ProdutoProjecaoEstoqueDto
            {
                ProdutoId = r.ProdutoId,
                Codigo = r.Codigo,
                Descricao = r.Descricao,
                GrupoDescricao = r.GrupoDescricao,
                UnidadeMedidaEstoqueId = r.UnidadeMedidaEstoqueId,
                MediaConsumoDiaria = r.MediaConsumoDiaria,
                EstoqueMinimoProjetado = r.EstoqueMinimoProjetado,
                EstoqueMaximoProjetado = r.EstoqueMaximoProjetado
            }).ToList()
        };
    }

    private async Task<List<ProdutoAggregate>> ObterProdutosAsync(FiltroProjecaoAutomaticaDto filtro,
        CancellationToken cancellationToken)
    {
        var produtos = new List<ProdutoAggregate>();

        if (filtro.ProdutoIds?.Any() == true)
            produtos.AddRange(
                await produtoRepository.ObterProdutosComGrupoAsync(filtro.ProdutoIds, false,
                    cancellationToken));

        if (filtro.GrupoIds?.Any() == true || filtro.SubGrupoIds?.Any() == true ||
            filtro.FornecedorIds?.Any() == true)
            produtos.AddRange(await produtoRepository.ObterPorFiltrosAsync(
                filtro.GrupoIds ?? new List<Guid>(),
                filtro.SubGrupoIds ?? new List<Guid>(),
                filtro.FornecedorIds ?? new List<Guid>()));

        if (!produtos.Any()) produtos.AddRange(await produtoRepository.ObterProdutosComGrupoAsync(cancellationToken));

        return produtos.DistinctBy(p => p.Id).ToList();
    }
}