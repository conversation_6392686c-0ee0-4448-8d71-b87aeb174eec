using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Application.Dtos.ProjecaoEstoque;
using Bootis.Estoque.Domain.AggregatesModel.LivroRazaoEstoqueAggregate;
using MediatR;
using ProdutoAggregate = Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto;

namespace Bootis.Estoque.Application.UseCases.ProjecaoEstoque;

public class CalcularProjecaoPersonalizadaHandler(
    IProdutoRepository produtoRepository,
    ILivroRazaoEstoqueRepository livroRazaoRepository)
    : IRequestHandler<FiltroProjecaoPersonalizadaDto, ResultadoProjecaoEstoqueDto>
{
    public async Task<ResultadoProjecaoEstoqueDto> Handle(FiltroProjecaoPersonalizadaDto filtro,
        CancellationToken cancellationToken)
    {
        var produtos = await ObterProdutosAsync(filtro, cancellationToken);
        if (!produtos.Any())
            return new ResultadoProjecaoEstoqueDto();

        var dataHoje = DateOnly.FromDateTime(DateTime.UtcNow);

        int? diasEstoqueMinimo = filtro.PeriodoProjecao != null
            ? (filtro.PeriodoProjecao.DataFim.ToDateTime(TimeOnly.MinValue) -
               filtro.PeriodoProjecao.DataInicio.ToDateTime(TimeOnly.MinValue)).Days
            : null;

        int? diasEstoqueMaximo = filtro.PeriodoProjecao != null
            ? (filtro.PeriodoProjecao.DataFim.ToDateTime(TimeOnly.MinValue) -
               filtro.PeriodoProjecao.DataInicio.ToDateTime(TimeOnly.MinValue)).Days
            : null;

        int? diasInicioProjecao = filtro.PeriodoAnalise != null
            ? (dataHoje.ToDateTime(TimeOnly.MinValue) - filtro.PeriodoAnalise.DataInicio.ToDateTime(TimeOnly.MinValue))
            .Days
            : null;

        int? diasFimProjecao = filtro.PeriodoAnalise != null
            ? (dataHoje.ToDateTime(TimeOnly.MinValue) - filtro.PeriodoAnalise.DataFim.ToDateTime(TimeOnly.MinValue))
            .Days
            : null;

        var projecao = new Domain.AggregatesModel.ProjecaoEstoque.ProjecaoEstoque(
            produtos,
            true,
            filtro.UsarDadosGrupo,
            diasEstoqueMinimo,
            diasEstoqueMaximo,
            diasInicioProjecao,
            diasFimProjecao
        );

        return await Executar(projecao, produtos);
    }

    private async Task<ResultadoProjecaoEstoqueDto> Executar(
        Domain.AggregatesModel.ProjecaoEstoque.ProjecaoEstoque projecao, List<ProdutoAggregate> produtos)
    {
        var movimentacoes = await livroRazaoRepository.ObterSaidasPeriodoAsync(
            produtos.Select(p => p.Id),
            projecao.DataInicioMovimentacoes,
            projecao.DataFimMovimentacoes
        );

        var resultados = projecao.Calcular(movimentacoes.ToList());

        return new ResultadoProjecaoEstoqueDto
        {
            RequerDadosManuais = projecao.RequerDadosManuais,
            Produtos = resultados.Select(r => new ProdutoProjecaoEstoqueDto
            {
                ProdutoId = r.ProdutoId,
                Codigo = r.Codigo,
                Descricao = r.Descricao,
                GrupoDescricao = r.GrupoDescricao,
                UnidadeMedidaEstoqueId = r.UnidadeMedidaEstoqueId,
                MediaConsumoDiaria = r.MediaConsumoDiaria,
                EstoqueMinimoProjetado = r.EstoqueMinimoProjetado,
                EstoqueMaximoProjetado = r.EstoqueMaximoProjetado
            }).ToList()
        };
    }

    private async Task<List<ProdutoAggregate>> ObterProdutosAsync(FiltroProjecaoPersonalizadaDto filtro,
        CancellationToken cancellationToken)
    {
        var produtos = new List<ProdutoAggregate>();

        if (filtro.ProdutoIds?.Any() == true)
            produtos.AddRange(
                await produtoRepository.ObterProdutosComGrupoAsync(filtro.ProdutoIds, false,
                    cancellationToken));

        if (filtro.GrupoIds?.Any() == true || filtro.SubGrupoIds?.Any() == true ||
            filtro.FornecedorIds?.Any() == true)
            produtos.AddRange(await produtoRepository.ObterPorFiltrosAsync(
                filtro.GrupoIds ?? new List<Guid>(),
                filtro.SubGrupoIds ?? new List<Guid>(),
                filtro.FornecedorIds ?? new List<Guid>()));

        if (!produtos.Any()) produtos.AddRange(await produtoRepository.ObterProdutosComGrupoAsync(cancellationToken));

        return produtos.DistinctBy(p => p.Id).ToList();
    }
}