using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.MovimentoEstoque.Remover;
using Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.MovimentoEstoque;

public class RemoverRequestHandler(
    IUnitOfWork unitOfWork,
    IMovimentoEstoqueRepository movimentoEstoqueRepository)
    : IRequestHandler<RemoverRequest>
{
    public async Task Handle(RemoverRequest request, CancellationToken cancellationToken)
    {
        var movimentoEstoque = await movimentoEstoqueRepository
            .ObterMovimentoEstoqueAsync(request.MovimentoEstoqueId).ConfigureAwait(false);

        movimentoEstoqueRepository.Remove(movimentoEstoque);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}