using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Interfaces;
using Bootis.Estoque.Application.Requests.AjusteSaldoEstoque.Cadastrar;
using Bootis.Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.AjusteSaldoEstoque;

public class CadastrarRequestHandler(
    IServicoEstoque servicoEstoque,
    ILocalEstoqueRepository localEstoqueRepository,
    ILoteRepository loteRepository,
    IAjusteSaldoEstoqueRepository ajusteSaldoRepository,
    IUnitOfWork unitOfWork,
    IUserContext userContext)
    : IRequestHandler<CadastrarRequest>
{
    public async Task Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var localEstoque = await localEstoqueRepository.ObterLocalEstoqueAsync(request.LocalEstoqueId);
        var lote = await loteRepository.ObterLotePorIdAsync(request.LoteId);

        var saldoAtual = await servicoEstoque.ObterSaldoAtualAsync(request.LoteId, localEstoque.Id);
        var quantidadeAjuste = request.NovoSaldo - saldoAtual;

        if (quantidadeAjuste == 0)
            return;

        var transacaoId = await servicoEstoque.RegistrarAjusteInventarioAsync(
            lote.ProdutoId,
            request.LoteId,
            localEstoque.Id,
            request.NovoSaldo,
            request.UnidadeMedidaId,
            "Ajuste manual de saldo",
            userContext.UserId,
            cancellationToken);

        var tipoOperacao = quantidadeAjuste > 0 ? TipoOperacao.Entrada : TipoOperacao.Saida;

        var ajusteSaldoEstoque = new Domain.AggregatesModel.AjusteSaldoEstoqueAggregate.AjusteSaldoEstoque(
            localEstoque.Id,
            request.LoteId,
            Math.Abs(quantidadeAjuste),
            saldoAtual,
            request.UnidadeMedidaId,
            userContext.UserId,
            tipoOperacao,
            transacaoId);

        ajusteSaldoRepository.Add(ajusteSaldoEstoque);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}