using Bootis.Estoque.Application.Commands.RegistrarMovimentoEstoque;
using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate.Events;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Application.Events;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.DomainEvents;

public class GerarMovimentoEstoqueNotificationHandler(IMediator mediator, IUnitOfWork unitOfWork) :
    INotificationHandler<DomainEventNotification<InventarioConferenciaEvent>>
{
    private readonly ILoteRepository _loteRepository = unitOfWork.GetRepository<ILoteRepository>();
    private readonly ISaldoEstoqueRepository _saldoRepository = unitOfWork.GetRepository<ISaldoEstoqueRepository>();

    public async Task Handle(DomainEventNotification<InventarioConferenciaEvent> notification,
        CancellationToken cancellationToken)
    {
        var inventarioConferenciaNotification = notification.DomainEvent;

        foreach (var inventarioItem in inventarioConferenciaNotification.Items)
        {
            if (inventarioItem.Diferenca is 0 or null)
                continue;

            var lote = await _loteRepository.ObterLotePorIdAsync(inventarioItem.LoteId);
            if (lote == null) continue;

            var saldo = await _saldoRepository.ObterSaldoEstoquePorLoteLocalEstoqueAsync(inventarioItem.LoteId,
                inventarioItem.LocalEstoqueId);
            if (saldo == null) continue;

            var tipoTransacao = inventarioItem.Diferenca > 0
                ? TipoTransacaoEstoque.AjusteEntrada
                : TipoTransacaoEstoque.AjusteSaida;

            var ehEntrada = inventarioItem.Diferenca > 0;
            var quantidadeAbsoluta = Math.Abs(inventarioItem.Diferenca.Value);

            var command = new RegistrarMovimentoEstoqueCommand
            {
                ProdutoId = lote.ProdutoId,
                LoteId = inventarioItem.LoteId,
                LocalEstoqueId = inventarioItem.LocalEstoqueId,
                EmpresaId = saldo.EmpresaId,
                Quantidade = quantidadeAbsoluta,
                UnidadeMedida = inventarioItem.UnidadeMedidaId,
                TipoTransacao = tipoTransacao,
                Motivo = $"Ajuste de inventário - Diferença: {inventarioItem.Diferenca}",
                UsuarioId = inventarioConferenciaNotification.ResponsavelInventarioId,
                ModuloOrigem = "Estoque.Inventario",
                EhEntrada = ehEntrada,
                Metadados = new Dictionary<string, object>
                {
                    { "DiferencaOriginal", inventarioItem.Diferenca.Value },
                    { "QuantidadeInventariada", inventarioItem.QuantidadeInventariada }
                }
            };

            await mediator.Send(command, cancellationToken);
        }
    }
}