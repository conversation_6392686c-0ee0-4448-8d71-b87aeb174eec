using Bootis.Shared.Common.Query;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Application.Requests.Rastreabilidade.Listar;

public class ListarTodosResponse
{
    public TotaisTransacaoEstoque TotalItens { get; set; }
    public PaginatedResult<TransacaoEstoque> Movimentos { get; set; }
}

public class TransacaoEstoque
{
    public DateTime DataMovimento { get; set; }
    public decimal QuantidadeMovimento { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao => UnidadeMedidaId.ToString();
    public Guid LoteId { get; set; }
    public string LoteNotaFiscalNumero { get; set; }
    public string LocalEstoqueDescricao { get; set; }
    public string TipoMovimento { get; set; }
    public string Operacao { get; set; }
}

public class TotaisTransacaoEstoque
{
    public int TotalGeral { get; set; }
    public int TotalReceitaManipulada { get; set; }
    public int TotalProducaoInterna { get; set; }
    public int TotalCompra { get; set; }
    public int TotalVenda { get; set; }
    public int TotalPerda { get; set; }
    public int TotalOutros { get; set; }
}