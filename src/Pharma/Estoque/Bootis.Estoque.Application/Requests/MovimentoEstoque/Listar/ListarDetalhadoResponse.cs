using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Application.Requests.MovimentoEstoque.Listar;

public class ListarDetalhadoResponse
{
    public Guid Id { get; set; }
    public DateTime DataMovimentacao { get; set; }
    public Guid ProdutoId { get; set; }
    public string ProdutoDescricao { get; set; }
    public Guid LocalEstoqueId { get; set; }
    public string LocalEstoqueDescricao { get; set; }
    public Guid LoteId { get; set; }
    public string LoteNumero { get; set; }
    public Guid FornecedorId { get; set; }
    public string NomeFornecedor { get; set; }
    public string NotaFiscalNumero { get; set; }
    public string Operacao { get; set; }
    public TipoOperacaoResponse TipoMovimentoId { get; set; }
    public string TipoMovimento => TipoMovimentoId.ToString();
    public decimal QuantidadeMovimento { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao { get; set; }
    public decimal CustoMovimentacao { get; set; }
    public Guid OperadorId { get; set; }
    public string OperadorNome { get; set; }
}