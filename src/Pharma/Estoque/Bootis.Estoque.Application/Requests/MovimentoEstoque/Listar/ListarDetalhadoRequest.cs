using Bootis.Shared.Common.Query;
using MediatR;

namespace Bootis.Estoque.Application.Requests.MovimentoEstoque.Listar;

public class ListarDetalhadoRequest : Filter, IRequest<PaginatedResult<ListarDetalhadoResponse>>
{
    public IEnumerable<Guid> ProdutoIds { get; set; }
    public IEnumerable<Guid> GrupoIds { get; set; }
    public IEnumerable<Guid> SubGrupoIds { get; set; }
    public IEnumerable<Guid> LocalEstoqueIds { get; set; }
    public IEnumerable<Guid> OperadorIds { get; set; }
    public List<string> NumerosLote { get; set; }
    public IEnumerable<Guid> FornecedorIds { get; set; }
    public List<string> NotaFiscalEntradaNumero { get; set; }
    public List<string> Operacao { get; set; }
    public List<int> TipoMovimentacao { get; set; }
    public DateTime? DataLancamentoInicial { get; set; }
    public DateTime? DataLancamentoFinal { get; set; }
    public decimal? QuantidadeMovimentada { get; set; }
    public decimal? CustoMovimentacao { get; set; }
}