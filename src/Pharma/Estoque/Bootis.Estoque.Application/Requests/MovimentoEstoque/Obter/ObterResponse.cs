using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate;
using UnidadeMedida = Bootis.Catalogo.Application.Requests.UnidadeMedida.UnidadeMedida;

namespace Bootis.Estoque.Application.Requests.MovimentoEstoque.Obter;

public class ObterResponse
{
    public Guid Id { get; set; }
    public DateTime DataLancamento { get; set; }
    public Guid EmpresaId { get; set; }
    public string NomeFantasia { get; set; }
    public string NumeroLote { get; set; }
    public Guid LoteId { get; set; }
    public TipoOperacaoResponse TipoOperacao { get; set; }
    public Guid OperacaoEstoqueId { get; set; }
    public string DescricaoOperacaoEstoque { get; set; }
    public Guid LocalEstoqueId { get; set; }
    public string LocalEstoqueDescricao { get; set; }
    public decimal Quantidade { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }

    public virtual UnidadeMedida UnidadeMedida
    {
        get
        {
            var unidadeMedidaModel = UnidadeMedidaCreator.Criar(UnidadeMedidaId);

            return new UnidadeMedida
            {
                Abreviacao = unidadeMedidaModel.Abreviacao.GetDescription(),
                Ativo = unidadeMedidaModel.Ativo,
                Descricao = unidadeMedidaModel.Descricao,
                UnidadeAlternativa = unidadeMedidaModel.UnidadeAlternativa
            };
        }
    }

    public string UsuarioNome { get; set; }
    public Guid UsuarioId { get; set; }
}