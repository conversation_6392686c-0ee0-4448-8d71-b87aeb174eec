using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Estoque.Application.Requests.MovimentoEstoque.Cadastrar;

public class CadastrarRequest : IRequest<Guid>
{
    public DateTime DataLancamento { get; set; }
    public Guid EmpresaId { get; set; }
    public Guid LoteId { get; set; }
    public Guid OperacaoEstoqueId { get; set; }
    public TipoOperacao TipoOperacao { get; set; }
    public TipoOperacaoEstoque OperacaoEstoque { get; set; }
    public Guid LocalEstoqueId { get; set; }
    public decimal Quantidade { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
}