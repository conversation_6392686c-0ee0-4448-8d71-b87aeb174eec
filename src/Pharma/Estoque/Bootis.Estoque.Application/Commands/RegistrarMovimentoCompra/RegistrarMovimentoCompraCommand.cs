using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Estoque.Application.Commands.RegistrarMovimentoCompra;

public class RegistrarMovimentoCompraCommand : IRequest<Guid>
{
    public Guid ProdutoId { get; set; }
    public Guid LoteId { get; set; }
    public Guid LocalEstoqueId { get; set; }
    public Guid EmpresaId { get; set; }
    public decimal Quantidade { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedida { get; set; }
    public Guid UsuarioId { get; set; }
    public string NotaFiscalNumero { get; set; }
    public string NotaFiscalSerie { get; set; }
    public Guid? NotaFiscalId { get; set; }
    public DateTime DataLancamento { get; set; }
}