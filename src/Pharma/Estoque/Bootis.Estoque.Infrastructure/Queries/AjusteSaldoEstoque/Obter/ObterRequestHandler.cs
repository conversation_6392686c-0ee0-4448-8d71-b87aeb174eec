using System.Data;
using System.Text;
using Bootis.Estoque.Application.Requests.AjusteSaldoEstoque.Obter;
using Bootis.Estoque.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.AjusteSaldoEstoque.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterAjusteSaldoEstoqueRequest, ObterAjusteSaldoEstoqueResponse>
{
    public async Task<ObterAjusteSaldoEstoqueResponse> Handle(ObterAjusteSaldoEstoqueRequest request,
        CancellationToken cancellationToken)
    {
        var sql = new StringBuilder($"""
                                     SELECT ase.data_lancamento,
                                            ase.id,
                                            usr.id AS usuario_id,
                                            CONCAT(usr.nome, ' ', usr.sobrenome) AS usuario_nome,
                                            prod.id AS produto_id,
                                            prod.descricao AS produto_descricao,
                                            lc.id AS local_estoque_id,
                                            lc.descricao AS local_estoque_descricao,
                                            emp.id AS empresa_id,
                                            emp.nome_fantasia AS empresa_nome_fantasia,
                                            lot.id AS lote_id,
                                            lot.numero AS lote_numero,
                                            ef.id AS fornecedor_id,
                                            ef.nome AS fornecedor_nome,
                                            lot.numero_nf AS nota_fiscal_numero,
                                            lot.serie_nf AS nota_fiscal_serie,
                                            ase.saldo_anterior,
                                            ase.quantidade_do_ajuste,
                                            ase.tipo_operacao,
                                            ase.unidade_medida_id,
                                            um.abreviacao AS unidade_medida_abreviacao,
                                            lre.saldo_final
                                       FROM ajuste_saldo_estoque ase
                                            LEFT JOIN usuarios usr ON
                                                      usr.id = ase.operador_id
                                            LEFT JOIN lotes lot ON
                                                      lot.id = ase.lote_id
                                            LEFT JOIN produtos prod ON
                                                      prod.id = lot.produto_id
                                            LEFT JOIN locais_estoque lc ON
                                                      lc.id = ase.local_estoque_id
                                            LEFT JOIN empresas emp ON
                                                      emp.id = lc.empresa_id
                                            LEFT JOIN fornecedores ef ON
                                                      ef.id = lot.fornecedor_id
                                            LEFT JOIN livro_razao_estoque lre ON
                                                      lre.id = ase.transacao_id
                                            LEFT JOIN unidades_medida um ON
                                                      um.id = ase.unidade_medida_id
                                      WHERE ase.id = '{request.Id}'
                                        AND ase.group_tenant_id = '{userContext.GroupTenantId}'
                                     """);

        var result = await connection.QueryFirstOrDefaultAsync<ObterAjusteSaldoEstoqueResponse>(sql.ToString(),
            new { request.Id, userContext.GroupTenantId });

        if (result is not null) return result;

        var message = Localizer.Instance.GetMessage_AjusteSaldoEstoque_GuidNaoEncontrado(request.Id);
        throw new DomainException(message);
    }
}