using System.Data;
using Bootis.Estoque.Application.Requests.AjusteSaldoEstoque.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.AjusteSaldoEstoque.Listar;

public class
    ListarDetalhadoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoRequest, PaginatedResult<ListarDetalhadoResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoResponse>> Handle(ListarDetalhadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT prod.descricao AS produto_descricao,
                                  ase.id,
                                  ase.data_lancamento,
                                  lot.numero AS lote_numero,
                                  le.descricao AS local_estoque_descricao,
                                  emp.nome_fantasia AS empresa_descricao,
                                  ase.quantidade_do_ajuste,
                                  ase.tipo_operacao,
                                  ase.unidade_medida_id,
                                  un.abreviacao AS unidade_medida_abreviacao,
                                  lre.saldo_final AS saldo
                             FROM ajuste_saldo_estoque ase
                                  LEFT JOIN lotes lot ON
                                            lot.id = ase.lote_id
                                  LEFT JOIN produtos prod ON
                                            prod.id = lot.produto_id
                                  LEFT JOIN locais_estoque le ON
                                            le.id = ase.local_estoque_id
                                  LEFT JOIN empresas emp ON
                                            emp.id = ase.tenant_id
                                  LEFT JOIN livro_razao_estoque lre ON
                                            lre.id = ase.transacao_id
                                  LEFT JOIN unidades_medida un ON
                                            un.id = ase.unidade_medida_id
                            WHERE ase.group_tenant_id = @GroupTenantId
                                  !@SEARCH_CONDITION@!
                           """;


        var searchProduto = new StringSearchField
        {
            Field = "ep.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchDataLancamento = new DateSearchField
        {
            Field = "eas.data_lancamento",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchLocal = new StringSearchField
        {
            Field = "ele.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchNumeroLote = new StringSearchField
        {
            Field = "el.numero",
            CompareType = StringCompareType.Contains
        };

        var searchEmpresa = new StringSearchField
        {
            Field = "emp.nome_fantasia",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarDetalhadoRequest, ListarDetalhadoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchProduto)
            .AddSearchField(searchDataLancamento)
            .AddSearchField(searchLocal)
            .AddSearchField(searchNumeroLote)
            .AddSearchField(searchEmpresa)
            .ExecuteAsync();
    }
}