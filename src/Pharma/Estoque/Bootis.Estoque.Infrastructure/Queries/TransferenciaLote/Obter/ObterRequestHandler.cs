using System.Data;
using Bootis.Estoque.Application.Requests.TransferenciaLote.Obter;
using Bootis.Estoque.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.TransferenciaLote.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                            SELECT tl.sequencia_numero_transferencia,
                                  tl.data_transferencia,
                                  tl.observacao,
                                  usr.id AS responsavel_id,
                                  concat(usr.nome, ' ', usr.sobrenome) AS responsavel_nome_completo,
                                  leo.id AS local_estoque_origem_id,
                                  leo.descricao AS local_estoque_origem_descricao,
                                  empo.nome_fantasia AS empresa_origem_descricao,
                                  led.id AS local_estoque_destino_id,
                                  led.descricao AS local_estoque_destino_descricao,
                                  empd.nome_fantasia AS empresa_destino_descricao
                             FROM transferencias_lote tl
                                  LEFT JOIN usuarios usr ON usr.id = tl.usuario_id
                                  LEFT JOIN locais_estoque leo ON leo.id = tl.local_de_estoque_origem_id
                                  LEFT JOIN empresas empo ON empo.id = leo.empresa_id
                                  LEFT JOIN locais_estoque led ON led.id = tl.local_de_estoque_destino_id
                                  LEFT JOIN empresas empd ON empd.id = led.empresa_id
                            WHERE tl.id = @id
                              AND tl.group_tenant_id = @groupTenantId;

                           SELECT tli.id AS transferencia_item_id,
                                  tli.quantidade_transferida,
                                  tli.unidade_medida_id,
                                  un.abreviacao AS unidade_medida_abreviacao,
                                  prod.id AS produto_id,
                                  prod.descricao AS produto_descricao,
                                  el.id AS lote_id,
                                  el.numero AS lote_numero,
                                  concat(el.numero_nf, '-', el.serie_nf) AS nota_fiscal_numero,
                                  meo.saldo_final AS saldo_final_origem,
                                  meo.unidade_medida_id AS unidade_medida_origem_id,
                                  uno.abreviacao AS unidade_medida_origem_abreviacao,
                                  med.saldo_final AS saldo_final_destino,
                                  med.unidade_medida_id AS unidade_medida_destino_id,
                                  und.abreviacao AS unidade_medida_destino_abreviacao
                             FROM transferencias_lote tl
                                  LEFT JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                                  LEFT JOIN produtos prod ON prod.id = tli.produto_id
                                  LEFT JOIN lotes el ON el.id = tli.lote_id
                                  LEFT JOIN livro_razao_estoque lre_saida ON lre_saida.id = tli.transacao_saida_id
                                  LEFT JOIN livro_razao_estoque lre_entrada ON lre_entrada.id = tli.transacao_entrada_id
                                  LEFT JOIN unidades_medida un ON un.id = tli.unidade_medida_id
                                  LEFT JOIN unidades_medida uno ON uno.id = lre_saida.unidade_medida_id
                                  LEFT JOIN unidades_medida und ON und.id = lre_entrada.unidade_medida_id
                            WHERE tl.id = @id
                              AND tl.group_tenant_id = @groupTenantId;
                           """;

        await using var query = await connection.QueryMultipleAsync(sql,
            new
            {
                id = request.Id, groupTenantId = userContext.GroupTenantId
            });
        var response = await query.ReadFirstOrDefaultAsync<ObterResponse>();

        if (response is null)
            throw new DomainException(
                Localizer.Instance.GetMessage_TransferenciaLote_GuidNaoEncontrado(request.Id));

        var itens = await query.ReadAsync<ItensResponse>();

        response.TransferenciaLoteItens = itens;

        return response;
    }
}