using System.Data;
using Bootis.Estoque.Application.Requests.Rastreabilidade.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using Dapper;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.Rastreabilidade;

public class ListarTodosRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ListarTodosRequest, ListarTodosResponse>
{
    //TODO Adicionar Transações de Estoque para Produto acabado e produção interna que hj nao existe rastreio por lote ainda
    public async Task<ListarTodosResponse> Handle(ListarTodosRequest request, CancellationToken cancellationToken)
    {
        var sql = """
                  WITH movimentos AS (
                  SELECT
                      TL.data_transferencia AS data_movimento,
                      tli.quantidade_transferida AS quantidade_movimento,
                      tli.unidade_medida_id,
                      un.abreviacao AS unidade_medida_abreviacao,
                      lot.id AS lote_id,
                      concat(lot.numero, '/', lot.numero_nf, '-', lot.serie_nf) AS lote_nota_fiscal_numero,
                      le.descricao AS local_estoque_descricao,
                      CAST('Saída' AS TEXT) AS tipo_movimento,
                      CAST('Transferência' AS TEXT) AS operacao
                  FROM transferencias_lote tl
                      JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                      JOIN lotes lot ON lot.id = tli.lote_id
                      JOIN livro_razao_estoque lre_saida ON lre_saida.id = tli.transacao_saida_id
                      JOIN livro_razao_estoque lre_entrada ON lre_entrada.id = tli.transacao_entrada_id
                      JOIN unidades_medida un ON un.id = tli.unidade_medida_id
                      JOIN locais_estoque le ON le.id = lre_saida.local_estoque_id
                      JOIN locais_estoque led ON led.id = lre_entrada.local_estoque_id
                  WHERE tl.group_tenant_id = @GroupTenantId
                      AND lot.id = @LoteId

                  UNION ALL

                  SELECT
                      TL.data_transferencia AS data_movimento,
                      tli.quantidade_transferida AS quantidade_movimento,
                      tli.unidade_medida_id,
                      un.abreviacao AS unidade_medida_abreviacao,
                      lot.id AS lote_id,
                      concat(lot.numero, '/', lot.numero_nf, '-', lot.serie_nf) AS lote_nota_fiscal_numero,
                      led.descricao AS local_estoque_descricao,
                      CAST('Entrada' AS TEXT) AS tipo_movimento,
                      CAST('Transferência' AS TEXT) AS operacao
                  FROM transferencias_lote tl
                      JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                      JOIN lotes lot ON lot.id = tli.lote_id
                      JOIN livro_razao_estoque lre_saida ON lre_saida.id = tli.transacao_saida_id
                      JOIN livro_razao_estoque lre_entrada ON lre_entrada.id = tli.transacao_entrada_id
                      JOIN unidades_medida un ON un.id = tli.unidade_medida_id
                      JOIN locais_estoque le ON le.id = lre_saida.local_estoque_id
                      JOIN locais_estoque led ON led.id = lre_entrada.local_estoque_id
                  WHERE tl.group_tenant_id = @GroupTenantId
                      AND lot.id = @LoteId

                  UNION ALL

                  SELECT 
                      per.data_perda AS data_movimento,
                      per.quantidade AS quantidade_movimento,
                      per.unidade_medida_id,
                      un.abreviacao AS unidade_medida_abreviacao,
                      lot.id AS lote_id,
                      concat(lot.numero, '/', lot.numero_nf, '-', lot.serie_nf) AS lote_nota_fiscal_numero,
                      le.descricao AS local_estoque_descricao,
                      CAST('Saída' AS TEXT) AS tipo_movimento,
                      CAST('Perda' AS TEXT) AS operacao
                  FROM perdas per
                      JOIN lotes lot ON lot.id = per.lote_id
                      JOIN locais_estoque le ON le.id = per.local_estoque_id
                      JOIN unidades_medida un ON un.id = per.unidade_medida_id
                  WHERE per.group_tenant_id = @GroupTenantId
                      AND lot.id = @LoteId

                  UNION ALL

                  SELECT 
                      eas.data_lancamento AS data_movimento,
                      eas.quantidade_do_ajuste AS quantidade_movimento,
                      eas.unidade_medida_id,
                      un.abreviacao AS unidade_medida_abreviacao,
                      lot.id AS lote_id,
                      concat(lot.numero, '/', lot.numero_nf, '-', lot.serie_nf) AS lote_nota_fiscal_numero,
                      ele.descricao AS local_estoque_descricao,
                      CASE 
                          WHEN eme.tipo_operacao = 0 THEN CAST('Saída' AS TEXT)
                          WHEN eme.tipo_operacao = 1 THEN CAST('Entrada' AS TEXT)
                          ELSE CAST('Dupla' AS TEXT)
                      END AS tipo_movimento,
                      CAST('Ajuste de Saldo' AS TEXT) AS operacao
                  FROM ajuste_saldo_estoque eas
                      JOIN lotes lot ON lot.id = eas.lote_id
                      JOIN locais_estoque ele ON ele.id = eas.local_estoque_id
                      JOIN livro_razao_estoque lre ON lre.id = eas.transacao_id
                      JOIN unidades_medida un ON un.id = eas.unidade_medida_id
                  WHERE eas.group_tenant_id = @GroupTenantId
                      AND lot.id = @LoteId

                  UNION ALL

                  SELECT
                      lot.data_lancamento AS data_movimento,
                      nfel.quantidade AS quantidade_movimento,
                      nfel.unidade_id AS unidade_medida_id,
                      um.abreviacao AS unidade_medida_abreviacao,
                      lot.id AS lote_id,
                      concat(lot.numero, '/', lot.numero_nf, '-', lot.serie_nf) AS lote_nota_fiscal_numero,
                      le.descricao AS local_estoque_descricao,
                      CAST('Entrada' AS TEXT) AS tipo_movimento,
                      CAST('Compra' AS TEXT) AS operacao
                  FROM lotes lot
                      JOIN notas_fiscais_entrada_lotes nfel ON nfel.numero_lote = lot.numero
                      JOIN notas_fiscais_entrada_itens nfei ON nfei.id = nfel.nota_fiscal_entrada_item_id
                      JOIN notas_fiscais_entrada nfe ON nfe.id = nfei.nota_fiscal_entrada_id AND nfe.numero = lot.numero_nf AND nfe.serie = lot.serie_nf
                      JOIN unidades_medida um ON um.id = nfel.unidade_id
                      JOIN locais_estoque le ON le.id = nfel.local_estoque_id
                  WHERE lot.group_tenant_id = @GroupTenantId
                      AND lot.id = @LoteId
                      
                  UNION ALL

                  SELECT
                    rm.data_emissao as data_movimento,
                    rmc.quantidade_calculada as quantidade_movimento,
                    rmc.unidade_medida_calculada_id as unidade_medida_id,
                    um.descricao AS unidade_medida_abreviacao,
                    l.id as lote_id,
                    concat(l.numero, '/', l.numero_nf, '-', l.serie_nf) AS lote_nota_fiscal_numero,
                    le.descricao AS local_estoque_descricao,
                    CAST('Saída' AS TEXT) AS tipo_movimento,
                    CAST('Receita Manipulada' AS TEXT) AS operacao
                  FROM receitas_manipuladas rm
                    join receitas_manipuladas_item rmi on rmi.receita_manipulada_id = rm.id
                    join formas_farmaceutica ff on ff.id = rm.forma_farmaceutica_id
                    join laboratorios l2 on l2.id = ff.laboratorio_id
                    join locais_estoque le on le.id = l2.local_estoque_id
                    join receita_manipulada_rastreio_calculos rmrc on rmrc.receita_manipulada_id = rm.id and rmrc.receita_manipulada_item_id = rmi.id
                    join receitas_manipuladas_calculos rmc on rmc.receita_manipulada_rastreio_calculo_id = rmrc.id and rmc.receita_manipulada_id = rmrc.receita_manipulada_id
                    join unidades_medida um on um.id = rmc.unidade_medida_calculada_id
                    join lotes l on l.id = rmrc.lote_id
                  WHERE rm.group_tenant_id = @GroupTenantId
                    AND l.id = @LoteId
                      
                  UNION ALL

                  SELECT
                    rm.data_emissao as data_movimento,
                    rmc.quantidade_calculada as quantidade_movimento,
                    rmc.unidade_medida_calculada_id as unidade_medida_id,
                    um.descricao AS unidade_medida_abreviacao,
                    l.id as lote_id,
                    concat(l.numero, '/', l.numero_nf, '-', l.serie_nf) AS lote_nota_fiscal_numero,
                    le.descricao AS local_estoque_descricao,
                    CAST('Saída' AS TEXT) AS tipo_movimento,
                    CAST('Produção Interna' AS TEXT) AS operacao
                  FROM receitas_manipuladas rm
                    join receitas_manipuladas_item rmi on rmi.receita_manipulada_id = rm.id
                    join formas_farmaceutica ff on ff.id = rm.forma_farmaceutica_id
                    join laboratorios l2 on l2.id = ff.laboratorio_id
                    join locais_estoque le on le.id = l2.local_estoque_id
                    join receita_manipulada_rastreio_calculos rmrc on rmrc.receita_manipulada_id = rm.id and rmrc.receita_manipulada_item_id = rmi.id
                    join receitas_manipuladas_calculos rmc on rmc.receita_manipulada_rastreio_calculo_id = rmrc.id and rmc.receita_manipulada_id = rmrc.receita_manipulada_id
                    join unidades_medida um on um.id = rmc.unidade_medida_calculada_id
                    join lotes l on l.id = rmrc.lote_id
                  WHERE rm.group_tenant_id = @GroupTenantId
                      AND l.id = @LoteId

                  UNION ALL

                  SELECT
                    lot.data_lancamento as data_movimento,
                    nfel.quantidade  as quantidade_movimento,
                    nfel.unidade_id as unidade_medida_id,
                    um.abreviacao AS unidade_medida_abreviacao,
                    lot.id as lote_id,
                    concat(lot.numero, '/', lot.numero_nf, '-', lot.serie_nf) AS lote_nota_fiscal_numero,
                    le.descricao AS local_estoque_descricao,
                    CAST('Saída' AS TEXT) AS tipo_movimento,
                    CAST('Venda' AS TEXT) AS operacao
                  FROM lotes lot
                    join notas_fiscais_entrada_lotes nfel on nfel.numero_lote = lot.numero
                    join notas_fiscais_entrada_itens nfei on nfei.id = nfel.nota_fiscal_entrada_item_id
                    join notas_fiscais_entrada nfe on nfe.id = nfei.nota_fiscal_entrada_id and nfe.numero = lot.numero_nf and nfe.serie = lot.serie_nf
                    join unidades_medida um on um.id = nfel.unidade_id
                    join locais_estoque le on le.id = nfel.local_estoque_id
                  WHERE lot.group_tenant_id = @GroupTenantId
                    AND lot.id = @LoteId)

                  SELECT  
                      data_movimento,
                      quantidade_movimento,
                      unidade_medida_id,
                      unidade_medida_abreviacao,
                      lote_id,
                      lote_nota_fiscal_numero,
                      local_estoque_descricao,
                      tipo_movimento,
                      operacao 
                  FROM movimentos
                  WHERE 1 = 1
                      !@SEARCH_CONDITION@!
                  """;

        var searchLocal = new StringSearchField
        {
            Field = "local_estoque_descricao",
            CompareType = StringCompareType.Contains
        };

        var searchDataMovimento = new DateSearchField
        {
            Field = "data_movimento",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchQuantidadeMovimento = new NumberSearchField
        {
            Field = "quantidade_movimento",
            CompareType = NumericCompareType.Contains
        };

        var searchUnidadeAbreviacao = new StringSearchField
        {
            Field = "unidade_medida_abreviacao",
            CompareType = StringCompareType.Contains
        };

        var searchLoteNf = new StringSearchField
        {
            Field = "lote_nota_fiscal_numero",
            CompareType = StringCompareType.Contains
        };

        var searchTipoMovimento = new StringSearchField
        {
            Field = "tipo_movimento",
            CompareType = StringCompareType.Contains
        };

        var searchOperacao = new StringSearchField
        {
            Field = "operacao",
            CompareType = StringCompareType.Contains
        };

        var response =
            await PaginatedQueryBuilder<ListarTodosRequest,
                    TransacaoEstoque>
                .Create(connection, sql, request, userContext)
                .AddSearchField(searchLocal)
                .AddSearchField(searchDataMovimento)
                .AddSearchField(searchQuantidadeMovimento)
                .AddSearchField(searchUnidadeAbreviacao)
                .AddSearchField(searchLoteNf)
                .AddSearchField(searchTipoMovimento)
                .AddSearchField(searchOperacao)
                .ExecuteAsync();

        var somaSql = """
                      SELECT 
                      (SELECT COUNT(*) 
                      FROM perdas per
                          JOIN lotes lot ON lot.id = per.lote_id
                       WHERE lot.id = @LoteId
                          AND lot.group_tenant_id = @GroupTenantId
                      ) AS TotalPerda,

                      (SELECT COUNT(*) 
                      FROM lotes lot
                          JOIN notas_fiscais_entrada_lotes nfel ON nfel.numero_lote = lot.numero
                       WHERE lot.id = @LoteId
                          AND lot.group_tenant_id = @GroupTenantId
                      ) AS TotalCompra,

                      (SELECT COUNT(*) 
                      FROM receitas_manipuladas rm
                          JOIN receita_manipulada_rastreio_calculos rmrc ON rmrc.receita_manipulada_id = rm.id
                          JOIN lotes l ON l.id = rmrc.lote_id
                       WHERE l.id = @LoteId
                          AND l.group_tenant_id = @GroupTenantId
                      ) AS TotalReceitaManipulada,

                      (SELECT COUNT(*) 
                      FROM receitas_manipuladas rm
                          JOIN receita_manipulada_rastreio_calculos rmrc ON rmrc.receita_manipulada_id = rm.id
                          JOIN lotes l ON l.id = rmrc.lote_id
                       WHERE l.id = @LoteId
                      ) AS TotalProducaoInterna,

                      (SELECT COUNT(*) 
                      FROM lotes lot
                          JOIN notas_fiscais_entrada_lotes nfel ON nfel.numero_lote = lot.numero
                       WHERE lot.id = @LoteId
                          AND lot.group_tenant_id = @GroupTenantId
                      ) AS TotalVenda,

                      (
                      (SELECT COUNT(*) 
                      FROM transferencias_lote tl
                          JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                          JOIN lotes lot ON lot.id = tli.lote_id
                       WHERE lot.id = @LoteId
                          AND lot.group_tenant_id = @GroupTenantId
                      )
                      +
                      (SELECT COUNT(*) 
                      FROM transferencias_lote tl
                          JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                          JOIN lotes el ON el.id = tli.lote_id
                       WHERE el.id = @LoteId
                          AND el.group_tenant_id = @GroupTenantId
                      )
                      +
                      (SELECT COUNT(*) 
                      FROM ajuste_saldo_estoque eas
                          JOIN lotes lot ON lot.id = eas.lote_id
                       WHERE lot.id = @LoteId
                          AND lot.group_tenant_id = @GroupTenantId
                          )
                      ) AS TotalOutros
                      """;

        var result = await connection.QueryFirstOrDefaultAsync<TotaisTransacaoEstoque>(
            somaSql, new
            {
                request.LoteId, userContext.GroupTenantId
            }
        );

        var totalItens = new TotaisTransacaoEstoque
        {
            TotalReceitaManipulada = result?.TotalReceitaManipulada ?? 0,
            TotalProducaoInterna = result?.TotalProducaoInterna ?? 0,
            TotalCompra = result?.TotalCompra ?? 0,
            TotalVenda = result?.TotalVenda ?? 0,
            TotalPerda = result?.TotalPerda ?? 0,
            TotalOutros = result?.TotalOutros ?? 0,
            TotalGeral = (result?.TotalReceitaManipulada ?? 0) +
                         (result?.TotalProducaoInterna ?? 0) +
                         (result?.TotalCompra ?? 0) +
                         (result?.TotalVenda ?? 0) +
                         (result?.TotalPerda ?? 0) +
                         (result?.TotalOutros ?? 0)
        };

        return new ListarTodosResponse
        {
            TotalItens = totalItens,
            Movimentos = response
        };
    }
}