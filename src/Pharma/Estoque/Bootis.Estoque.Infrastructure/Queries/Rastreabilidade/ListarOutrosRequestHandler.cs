using System.Data;
using Bootis.Estoque.Application.Requests.Rastreabilidade.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using Dapper;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.Rastreabilidade;

public class ListarOutrosRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ListarOutrosRequest, ListarOutrosResponse>
{
    public async Task<ListarOutrosResponse> Handle(ListarOutrosRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           WITH movimentos AS (
                           SELECT
                               TL.data_transferencia AS data_movimento,
                               TL.sequencia_numero_transferencia as numero_movimento,
                               tli.quantidade_transferida AS quantidade_movimento,
                               tli.unidade_medida_id,
                               un.abreviacao AS unidade_medida_abreviacao,
                               CAST('Saída' AS TEXT) AS tipo_movimento,
                               CAST('Transferência' AS TEXT) AS operacao,
                               p.valor_custo,
                               p.unidade_estoque_id,
                               CONCAT(u.nome, ' ', u.sobrenome) nome_operador
                           FROM transferencias_lote tl
                               JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                               join usuarios u on U.id = TL.usuario_id
                               JOIN produtos p ON p.id = tli.produto_id
                               JOIN lotes lot ON lot.id = tli.lote_id
                               JOIN livro_razao_estoque lre_saida ON lre_saida.id = tli.transacao_saida_id
                               JOIN livro_razao_estoque lre_entrada ON lre_entrada.id = tli.transacao_entrada_id
                               JOIN unidades_medida un ON un.id = tli.unidade_medida_id
                               JOIN locais_estoque le ON le.id = lre_saida.local_estoque_id
                               JOIN locais_estoque led ON led.id = lre_entrada.local_estoque_id
                           WHERE tl.group_tenant_id = @GroupTenantId
                               AND lot.id = @LoteId
                           	                  
                           UNION ALL
                           	                  
                           SELECT
                               TL.data_transferencia AS data_movimento,
                               TL.sequencia_numero_transferencia as numero_movimento,
                               tli.quantidade_transferida AS quantidade_movimento,
                               tli.unidade_medida_id,
                               un.abreviacao AS unidade_medida_abreviacao,
                               CAST('Entrada' AS TEXT) AS tipo_movimento,
                               CAST('Transferência' AS TEXT) AS operacao,
                               p.valor_custo,
                               p.unidade_estoque_id,
                               CONCAT(u.nome, ' ', u.sobrenome) nome_operador
                           FROM transferencias_lote tl
                               JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                               join usuarios u on U.id = TL.usuario_id
                               JOIN produtos p ON p.id = tli.produto_id
                               JOIN lotes lot ON lot.id = tli.lote_id
                               JOIN livro_razao_estoque lre_saida ON lre_saida.id = tli.transacao_saida_id
                               JOIN livro_razao_estoque lre_entrada ON lre_entrada.id = tli.transacao_entrada_id
                               JOIN unidades_medida un ON un.id = tli.unidade_medida_id
                               JOIN locais_estoque le ON le.id = lre_saida.local_estoque_id
                               JOIN locais_estoque led ON led.id = lre_entrada.local_estoque_id
                           WHERE tl.group_tenant_id = @GroupTenantId
                               AND lot.id = @LoteId
                               
                           UNION ALL    
                               
                           SELECT 
                               eas.data_lancamento AS data_movimento,
                               p.sequencia_group_tenant AS numero_movimento,
                               eas.quantidade_do_ajuste AS quantidade_movimento,
                               eas.unidade_medida_id,
                               un.abreviacao AS unidade_medida_abreviacao,
                               CASE 
                                   WHEN eme.tipo_operacao = 0 THEN CAST('Saída' AS TEXT)
                                   WHEN eme.tipo_operacao = 1 THEN CAST('Entrada' AS TEXT)
                                   ELSE CAST('Dupla' AS TEXT)
                               END AS tipo_movimento,
                               CAST('Ajuste de Saldo' AS TEXT) AS operacao,
                               p.valor_custo,
                               p.unidade_estoque_id,
                               CONCAT(u.nome, ' ', u.sobrenome) nome_operador
                           FROM ajuste_saldo_estoque eas
                               JOIN lotes lot ON lot.id = eas.lote_id
                               JOIN usuarios u on u.id = eas.operador_id
                               JOIN produtos p ON p.id = lot.produto_id
                               JOIN locais_estoque ele ON ele.id = eas.local_estoque_id
                               JOIN livro_razao_estoque lre ON lre.id = eas.transacao_id
                               JOIN unidades_medida un ON un.id = eas.unidade_medida_id
                           WHERE eas.group_tenant_id = @GroupTenantId
                               AND lot.id = @LoteId )
                               
                           SELECT  
                               data_movimento,
                               numero_movimento,
                               quantidade_movimento,
                               unidade_medida_id,
                               unidade_medida_abreviacao,
                               tipo_movimento,
                               operacao,
                               valor_custo,
                               unidade_estoque_id,
                               nome_operador 
                           FROM movimentos
                           WHERE 1 = 1
                               !@SEARCH_CONDITION@!
                           """;

        var searchDataMovimento = new DateSearchField
        {
            Field = "data_movimento",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchQuantidadeMovimento = new NumberSearchField
        {
            Field = "quantidade_movimento",
            CompareType = NumericCompareType.Contains
        };

        var searchUnidadeAbreviacao = new StringSearchField
        {
            Field = "unidade_medida_abreviacao",
            CompareType = StringCompareType.Contains
        };

        var searchTipoMovimento = new StringSearchField
        {
            Field = "operacao",
            CompareType = StringCompareType.Contains
        };

        var searchNomeOperador = new StringSearchField
        {
            Field = "nome_operador",
            CompareType = StringCompareType.Contains
        };

        var response = await PaginatedQueryBuilder<ListarOutrosRequest, ListarOutros>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchDataMovimento)
            .AddSearchField(searchQuantidadeMovimento)
            .AddSearchField(searchUnidadeAbreviacao)
            .AddSearchField(searchTipoMovimento)
            .AddSearchField(searchNomeOperador)
            .ExecuteAsync();

        var totalOutros = await connection.QueryFirstAsync<int?>(@"
                                                                       SELECT
                                                                       (SELECT 
                                                                           COUNT(*)
                                                                       FROM ajuste_saldo_estoque eas
                                                                           JOIN lotes lot ON lot.id = eas.lote_id
                                                                           JOIN usuarios u on u.id = eas.operador_id
                                                                           JOIN produtos p ON p.id = lot.produto_id
                                                                           JOIN locais_estoque ele ON ele.id = eas.local_estoque_id
                                                                           JOIN livro_razao_estoque lre ON lre.id = eas.transacao_id
                                                                           JOIN unidades_medida un ON un.id = eas.unidade_medida_id
                                                                       WHERE eas.group_tenant_id = @GroupTenantId
                                                                           AND lot.id = @LoteId)
                                                                       +
                                                                       (SELECT
                                                                           COUNT(*)
                                                                       FROM transferencias_lote tl
                                                                           JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                                                                           join usuarios u on U.id = TL.usuario_id
                                                                           JOIN produtos p ON p.id = tli.produto_id
                                                                           JOIN lotes lot ON lot.id = tli.lote_id
                                                                           JOIN livro_razao_estoque lre_saida ON lre_saida.id = tli.transacao_saida_id
                                                                           JOIN livro_razao_estoque lre_entrada ON lre_entrada.id = tli.transacao_entrada_id
                                                                           JOIN unidades_medida un ON un.id = tli.unidade_medida_id
                                                                           JOIN locais_estoque le ON le.id = lre_saida.local_estoque_id
                                                                           JOIN locais_estoque led ON led.id = lre_entrada.local_estoque_id
                                                                       WHERE tl.group_tenant_id = @GroupTenantId
                                                                           AND lot.id = @LoteId)
                                                                       +
                                                                       (SELECT
                                                                           COUNT(*)
                                                                       FROM transferencias_lote tl
                                                                           JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                                                                           join usuarios u on U.id = TL.usuario_id
                                                                           JOIN produtos p ON p.id = tli.produto_id
                                                                           JOIN lotes lot ON lot.id = tli.lote_id
                                                                           JOIN livro_razao_estoque lre_saida ON lre_saida.id = tli.transacao_saida_id
                                                                           JOIN livro_razao_estoque lre_entrada ON lre_entrada.id = tli.transacao_entrada_id
                                                                           JOIN unidades_medida un ON un.id = tli.unidade_medida_id
                                                                           JOIN locais_estoque le ON le.id = lre_saida.local_estoque_id
                                                                           JOIN locais_estoque led ON led.id = lre_entrada.local_estoque_id
                                                                       WHERE tl.group_tenant_id = @GroupTenantId
                                                                           AND lot.id = @LoteId)",
            new
            {
                loteId = request.LoteId,
                groupTenantId = userContext.GroupTenantId
            });

        return new ListarOutrosResponse
        {
            TotalItens = totalOutros ?? 0,
            Outros = response
        };
    }
}