using System.Data;
using Bootis.Estoque.Application.Requests.MovimentoEstoque.Obter;
using Bootis.Estoque.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.MovimentoEstoque.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        var sql = """
                  SELECT em.id,
                         em.id,
                         em.data_lancamento,
                         ee.nome_fantasia,
                         ee.id AS empresa_id,
                         el.numero AS numero_lote,
                         el.id AS lote_id,
                         em.tipo_operacao,
                         eo.descricao AS descricao_operacao_estoque,
                         eo.id AS operacao_estoque_id,
                         ele.descricao AS local_estoque_descricao,
                         ele.id AS local_estoque_id,
                         em.quantidade,
                         em.unidade_medida_id,
                         eu.nome AS usuario_nome,
                         eu.id AS usuario_id
                  FROM movimentos_estoque em
                  LEFT JOIN empresas ee ON
                            ee.id = em.empresa_id
                  LEFT JOIN lotes el ON
                            el.id = em.lote_id
                  LEFT JOIN operacoes_estoque eo ON
                            eo.id = em.operacao_estoque_id
                  LEFT JOIN usuarios eu ON
                            eu.id = em.usuario_id
                  LEFT JOIN locais_estoque ele ON
                            ele.id = em.local_estoque_id
                  WHERE em.id = @id
                    AND em.group_tenant_id = @groupTenantId;
                  """;

        var movimentos = await connection
            .QueryFirstOrDefaultAsync<ObterResponse>(sql,
                new { request.Id, userContext.GroupTenantId });

        if (movimentos is null)
            throw new DomainException(
                Localizer.Instance.GetMessage_MovimentoEstoque_GuidNaoEncontrado(request.Id));

        return movimentos;
    }
}