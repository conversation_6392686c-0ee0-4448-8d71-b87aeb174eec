using Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Estoque.Infrastructure.EntityConfigurations;

public class TransferenciaLoteItensEntityTypeConfiguration : BaseEntityTypeConfiguration<TransferenciaLoteItens>
{
    public override void Configure(EntityTypeBuilder<TransferenciaLoteItens> builder)
    {
        builder.ToTable("transferencias_lote_itens");

        builder
            .HasOne(c => c.Produto)
            .WithMany()
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder
            .Property(c => c.LoteId)
            .IsRequired();

        builder
            .HasOne(x => x.Lote)
            .WithMany()
            .HasForeignKey(x => x.LoteId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.QuantidadeTransferida)
            .EstoquePrecisao()
            .IsRequired();

        builder
            .Property(c => c.UnidadeMedidaId)
            .IsRequired();

        builder
            .HasOne(c => c.TransferenciaLote)
            .WithMany(c => c.TransferenciaLoteItens)
            .HasForeignKey(c => c.TransferenciaLoteId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        builder
            .Property(c => c.TransacaoSaidaId)
            .IsRequired();

        builder
            .Property(c => c.TransacaoEntradaId)
            .IsRequired();

        base.Configure(builder);
    }
}