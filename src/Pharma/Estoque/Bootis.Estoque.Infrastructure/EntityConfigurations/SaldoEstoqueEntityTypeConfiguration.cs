using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Estoque.Infrastructure.EntityConfigurations;

public class SaldoEstoqueEntityTypeConfiguration : BaseEntityTypeConfiguration<SaldoEstoque>
{
    public override void Configure(EntityTypeBuilder<SaldoEstoque> builder)
    {
        builder.ToTable("saldos_estoque");

        builder
            .HasOne(c => c.Empresa)
            .WithMany()
            .HasForeignKey(c => c.EmpresaId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired();

        builder
            .HasOne(c => c.Produto)
            .WithMany()
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(c => c.Lote)
            .WithMany()
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(c => c.LocalEstoque)
            .WithMany(c => c.SaldosEstoque)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder
            .Property(c => c.Saldo)
            .EstoquePrecisao()
            .IsRequired();

        builder
            .Property(c => c.UnidadeMedidaId)
            .IsRequired();

        builder
            .Property(c => c.Bloqueado)
            .IsRequired()
            .HasDefaultValue(false);

        builder
            .Property(c => c.UltimaAtualizacao)
            .IsRequired();

        builder
            .Property(c => c.UltimaTransacaoId)
            .IsRequired(false);

        builder
            .Property(c => c.Versao)
            .IsRequired()
            .HasDefaultValue(0)
            .IsConcurrencyToken(); // Controle de concorrência otimista

        // Índice único - só pode haver um saldo por lote/local/tenant
        builder.HasIndex(s => new { s.LoteId, s.LocalEstoqueId, s.TenantId })
            .IsUnique()
            .HasDatabaseName("UK_SaldoEstoque_LoteLocalTenant");

        // Índices para queries
        builder.HasIndex(s => s.ProdutoId)
            .HasDatabaseName("IX_SaldoEstoque_ProdutoId");

        builder
            .Ignore(c => c.IsRemoved);

        base.Configure(builder);
    }
}