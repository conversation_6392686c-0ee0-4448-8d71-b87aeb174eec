using System.Text.Json;
using Bootis.Estoque.Domain.AggregatesModel.LivroRazaoEstoqueAggregate;
using Bootis.Shared.Infrastructure.ValueGenerators;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Estoque.Infrastructure.EntityConfigurations;

public class LivroRazaoEstoqueEntityTypeConfiguration : IEntityTypeConfiguration<LivroRazaoEstoque>
{
    public void Configure(EntityTypeBuilder<LivroRazaoEstoque> builder)
    {
        builder.ToTable("LivroRazaoEstoque");

        builder.HasKey(l => l.Id);

        // Sequence para garantir ordem por GroupTenant
        builder.Property(l => l.SequenciaGroupTenant)
            .HasValueGenerator<GroupTenantSequenceValueGenerator>();

        builder.Property(l => l.TransacaoId)
            .IsRequired();

        builder.Property(l => l.DataOcorrencia)
            .IsRequired();

        builder.Property(l => l.ProdutoId)
            .IsRequired();

        builder.Property(l => l.LoteId)
            .IsRequired();

        builder.Property(l => l.LocalEstoqueId)
            .IsRequired();

        builder.Property(l => l.EmpresaId)
            .IsRequired();

        builder.Property(l => l.Quantidade)
            .HasPrecision(18, 6)
            .IsRequired();

        builder.Property(l => l.UnidadeMedidaPadraoLote)
            .HasConversion<string>()
            .IsRequired();

        builder.Property(l => l.TipoTransacao)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(l => l.Motivo)
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(l => l.TipoDocumentoOrigem)
            .HasMaxLength(100);

        builder.Property(l => l.UsuarioId)
            .IsRequired();

        builder.Property(l => l.NomeUsuario)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(l => l.ModuloOrigem)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(l => l.Metadados)
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));

        // Índices para performance
        builder.HasIndex(l => l.TransacaoId)
            .HasDatabaseName("IX_LivroRazaoEstoque_TransacaoId");

        builder.HasIndex(l => new { l.LoteId, l.LocalEstoqueId })
            .HasDatabaseName("IX_LivroRazaoEstoque_LoteLocal");

        builder.HasIndex(l => l.ProdutoId)
            .HasDatabaseName("IX_LivroRazaoEstoque_ProdutoId");

        builder.HasIndex(l => l.DataOcorrencia)
            .HasDatabaseName("IX_LivroRazaoEstoque_DataOcorrencia");

        builder.HasIndex(l => l.SequenciaGroupTenant)
            .HasDatabaseName("IX_LivroRazaoEstoque_SequenciaGroupTenant");

        builder.HasIndex(l => l.EmpresaId)
            .HasDatabaseName("IX_LivroRazaoEstoque_EmpresaId");

        builder.HasIndex(l => l.TipoTransacao)
            .HasDatabaseName("IX_LivroRazaoEstoque_TipoTransacao");

        // Relacionamentos
        builder.HasOne(l => l.Produto)
            .WithMany()
            .HasForeignKey(l => l.ProdutoId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(l => l.Lote)
            .WithMany()
            .HasForeignKey(l => l.LoteId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(l => l.LocalEstoque)
            .WithMany()
            .HasForeignKey(l => l.LocalEstoqueId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(l => l.Empresa)
            .WithMany()
            .HasForeignKey(l => l.EmpresaId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}