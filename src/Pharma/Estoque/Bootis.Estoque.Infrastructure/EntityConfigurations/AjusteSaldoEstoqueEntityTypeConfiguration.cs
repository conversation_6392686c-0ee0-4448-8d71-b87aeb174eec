using Bootis.Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Estoque.Infrastructure.EntityConfigurations;

public class AjusteSaldoEstoqueEntityTypeConfiguration : BaseEntityTypeConfiguration<AjusteSaldoEstoque>
{
    public override void Configure(EntityTypeBuilder<AjusteSaldoEstoque> builder)
    {
        builder.ToTable("ajuste_saldo_estoque");

        builder
            .Property(c => c.QuantidadeDoAjuste)
            .EstoquePrecisao()
            .IsRequired();

        builder
            .Property(c => c.SaldoAnterior)
            .EstoquePrecisao()
            .IsRequired();

        builder
            .Property(c => c.DataLancamento)
            .DataHora()
            .IsRequired();

        builder
            .Property(c => c.TipoOperacao)
            .IsRequired();

        builder
            .Property(c => c.OperadorId)
            .IsRequired();

        builder
            .HasOne(c => c.Operador)
            .WithMany()
            .HasForeignKey(c => c.OperadorId)
            .OnDelete(DeleteBehavior.Restrict);


        builder.HasOne(c => c.Lote)
            .WithMany()
            .HasForeignKey(c => c.LoteId)
            .IsRequired();

        builder.HasOne(c => c.LocalEstoque)
            .WithMany()
            .HasForeignKey(c => c.LocalEstoqueId)
            .IsRequired();

        builder
            .Property(c => c.UnidadeMedidaId)
            .IsRequired();

        builder
            .Property(c => c.TransacaoId)
            .IsRequired();

        base.Configure(builder);
    }
}