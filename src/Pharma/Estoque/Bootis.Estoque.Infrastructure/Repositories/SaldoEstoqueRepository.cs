using Bootis.Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Estoque.Infrastructure.Repositories;

public class SaldoEstoqueRepository(IUserContext userContext, IDbContext context)
    : Repository<SaldoEstoque>(context), ISaldoEstoqueRepository
{
    public async Task<SaldoEstoque> ObterSaldoEstoquePorLoteLocalEstoqueIdAsync(Guid loteId,
        Guid localEstoqueId)
    {
        return await DbSet
            .Include(c => c.LocalEstoque)
            .Include(c => c.Lote)
            .FirstOrDefaultAsync(c =>
                c.Lote.Id == loteId && c.LocalEstoque.Id == localEstoqueId &&
                c.GroupTenantId == userContext.GroupTenantId);
    }

    public async Task<SaldoEstoque> ObterSaldoEstoquePorLoteLocalEstoqueAsync(Guid loteId, Guid localEstoqueId)
    {
        return await DbSet
            .Include(c => c.LocalEstoque)
            .Include(c => c.Lote)
            .FirstOrDefaultAsync(c =>
                c.LoteId == loteId && c.LocalEstoqueId == localEstoqueId &&
                c.GroupTenantId == userContext.GroupTenantId);
    }

    public Task<SaldoEstoque[]> ObterSaldoEstoquePorProdutoAsync(Guid produtoId)
    {
        return Context.Set<SaldoEstoque>()
            .Where(c => c.ProdutoId == produtoId && c.GroupTenantId == userContext.GroupTenantId)
            .ToArrayAsync();
    }

    public Task<List<SaldoEstoque>> ObterSaldosEstoquePorLoteLocalEstoqueAsync(IEnumerable<Guid> loteIds,
        Guid localEstoqueId)
    {
        return Context.Set<SaldoEstoque>()
            .Where(c => loteIds.Contains(c.LoteId)
                        && c.LocalEstoqueId == localEstoqueId
                        && c.GroupTenantId == userContext.GroupTenantId)
            .ToListAsync();
    }

    public void Add(AjusteSaldoEstoque ajusteSaldoEstoque)
    {
        Context.Set<AjusteSaldoEstoque>()
            .Add(ajusteSaldoEstoque);
    }

    public void Update(AjusteSaldoEstoque ajusteSaldoEstoque)
    {
        Context.Set<AjusteSaldoEstoque>()
            .Update(ajusteSaldoEstoque);
    }

    public void Remove(AjusteSaldoEstoque ajusteSaldoEstoque)
    {
        Context.Set<AjusteSaldoEstoque>()
            .Remove(ajusteSaldoEstoque);
    }

    public Task<SaldoEstoque[]> ObterSaldosEstoquePorLoteAsync(Guid loteId)
    {
        return Context.Set<SaldoEstoque>()
            .Include(c => c.LocalEstoque)
            .Where(c => c.LoteId == loteId && c.TenantId == userContext.TenantId)
            .ToArrayAsync();
    }
}