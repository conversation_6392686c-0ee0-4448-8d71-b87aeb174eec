using Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Estoque.Infrastructure.Repositories;

public class OperacaoEstoqueRepository(IDbContext context)
    : Repository<OperacaoEstoque>(context), IOperacaoEstoqueRepository
{
    public Task<OperacaoEstoque> ObterOperacaoEstoquePorTipoAsync(TipoOperacao tipoOperacao)
    {
        return ObterOperacaoEstoquePorDescricaoETipoAsync(TipoOperacaoEstoque.TransacaoEstoque, tipoOperacao);
    }

    public async Task<OperacaoEstoque> ObterOperacaoEstoqueInventarioAsync()
    {
        var descricaoOpercaoEstoque = "Inventário";

        return await DbSet
            .FirstOrDefaultAsync(c => c.<PERSON> == descricaoOpercaoEstoque);
    }

    public async Task<OperacaoEstoque> ObterOperacaoEstoquePorDescricaoETipo(string descricao,
        TipoOperacao tipoOperacao)
    {
        return await DbSet
            .FirstOrDefaultAsync(c => c.Descricao == descricao && c.TipoOperacao == tipoOperacao);
    }

    public Task<OperacaoEstoque> ObterOperacaoEstoquePorDescricaoETipoAsync(TipoOperacaoEstoque tipoOperacaoEstoque,
        TipoOperacao tipoOperacao)
    {
        return DbSet.FirstOrDefaultAsync(c =>
            c.Descricao == tipoOperacaoEstoque.GetDescription());
    }
}