using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.Events;
using Bootis.Shared.Common.Events;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Estoque.Infrastructure.Repositories;

public class LocalEstoqueRepository(IDbContext context) : Repository<LocalEstoque>(context), ILocalEstoqueRepository
{
    public Task<LocalEstoque> ObterLocalEstoquePorDescricaoAsync(string descricao, Guid id, Guid empresaId)
    {
        return DbSet
            .Include(c => c.<PERSON>dosEstoque)
            .Where(c => c.Id != id)
            .FirstOrDefaultAsync(c => c.Descricao == descricao && c.EmpresaId == empresaId);
    }

    public async Task<LocalEstoque> ObterPorIdAsync(Guid id)
    {
        return await DbSet
            .Include(c => c.Empresa)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public Task<List<LocalEstoque>> ObterPorIdsAsync(IEnumerable<Guid> ids)
    {
        return Context.Set<LocalEstoque>()
            .Where(c => ids.Contains(c.Id))
            .Include(c => c.Empresa)
            .ToListAsync();
    }

    public Task<bool> VerificarDependenciaLocalEstoqueAsync(Guid id)
    {
        const string sql = """
                            SELECT 
                               CASE
                                   WHEN EXISTS (SELECT 1 FROM saldos_estoque WHERE local_estoque_id = l.id) THEN 1
                                   WHEN EXISTS (SELECT 1 FROM perdas WHERE local_estoque_id = l.id) THEN 1
                                   WHEN EXISTS (SELECT 1 FROM ajuste_saldo_estoque WHERE local_estoque_id = l.id) THEN 1
                                   WHEN EXISTS (SELECT 1 FROM livro_razao_estoque WHERE local_estoque_id = l.id) THEN 1
                                   WHEN EXISTS (SELECT 1 FROM transferencias_lote WHERE local_de_estoque_origem_id = l.id) THEN 1
                                   WHEN EXISTS (SELECT 1 FROM transferencias_lote WHERE local_de_estoque_destino_id = l.id) THEN 1
                                   WHEN EXISTS (SELECT 1 FROM laboratorios WHERE local_estoque_id = l.id) THEN 1
                                   WHEN EXISTS (SELECT 1 FROM notas_fiscais_entrada_lotes WHERE local_estoque_id = l.id) THEN 1
                                   WHEN EXISTS (SELECT 1 FROM produto_lotes_em_uso WHERE local_estoque_id = l.id) THEN 1 
                                   ELSE 0
                               END AS tem_dependencia
                           FROM locais_estoque l
                           WHERE l.id = @id;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return dbConnection.QueryFirstOrDefaultAsync<bool>(sql, new { id },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public Task<LocalEstoque> ObterLocalEstoquePorDescricaoCadastroAsync(string descricao, Guid empresaId)
    {
        return DbSet
            .Include(c => c.SaldosEstoque)
            .FirstOrDefaultAsync(c => c.Descricao == descricao && c.EmpresaId == empresaId);
    }

    public override void Update(LocalEstoque entity)
    {
        DomainEvent.RaiseExternal(new LocalEstoqueAlteradoEvent(entity));

        base.Update(entity);
    }
}