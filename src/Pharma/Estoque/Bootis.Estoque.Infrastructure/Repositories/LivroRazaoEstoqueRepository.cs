using Bootis.Estoque.Domain.AggregatesModel.LivroRazaoEstoqueAggregate;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Estoque.Infrastructure.Repositories;

public class LivroRazaoEstoqueRepository(IDbContext context)
    : Repository<LivroRazaoEstoque>(context), ILivroRazaoEstoqueRepository
{
    public async Task<LivroRazaoEstoque> ObterPorIdAsync(Guid id)
    {
        return await DbSet
            .Include(l => l.Produto)
            .Include(l => l.Lote)
            .Include(l => l.LocalEstoque)
            .Include(l => l.Empresa)
            .FirstOrDefaultAsync(l => l.Id == id);
    }

    public async Task<IEnumerable<LivroRazaoEstoque>> ObterHistoricoLoteAsync(Guid loteId, Guid localEstoqueId)
    {
        return await DbSet
            .Where(l => l.LoteId == loteId && l.LocalEstoqueId == localEstoqueId)
            .OrderBy(l => l.SequenciaGroupTenant)
            .Include(l => l.LocalEstoque)
            .ToListAsync();
    }

    public async Task<IEnumerable<LivroRazaoEstoque>> ObterPorTransacaoAsync(Guid transacaoId)
    {
        return await DbSet
            .Where(l => l.TransacaoId == transacaoId)
            .Include(l => l.LocalEstoque)
            .Include(l => l.Lote)
            .OrderBy(l => l.SequenciaGroupTenant)
            .ToListAsync();
    }

    public async Task<decimal> CalcularSaldoAsync(Guid loteId, Guid localEstoqueId)
    {
        return await DbSet
            .Where(l => l.LoteId == loteId && l.LocalEstoqueId == localEstoqueId)
            .SumAsync(l => l.Quantidade);
    }

    public async Task<IEnumerable<LivroRazaoEstoque>> ObterMovimentacoesPorProdutoAsync(Guid produtoId,
        DateTime? dataInicio = null, DateTime? dataFim = null)
    {
        var query = DbSet
            .Where(l => l.ProdutoId == produtoId);

        if (dataInicio.HasValue)
            query = query.Where(l => l.DataOcorrencia >= dataInicio.Value);

        if (dataFim.HasValue)
            query = query.Where(l => l.DataOcorrencia <= dataFim.Value);

        return await query
            .Include(l => l.Produto)
            .Include(l => l.Lote)
            .Include(l => l.LocalEstoque)
            .OrderBy(l => l.DataOcorrencia)
            .ThenBy(l => l.SequenciaGroupTenant)
            .ToListAsync();
    }

    public async Task<IEnumerable<LivroRazaoEstoque>> ObterMovimentacoesPorLoteAsync(Guid loteId,
        DateTime? dataInicio = null, DateTime? dataFim = null)
    {
        var query = DbSet
            .Where(l => l.LoteId == loteId);

        if (dataInicio.HasValue)
            query = query.Where(l => l.DataOcorrencia >= dataInicio.Value);

        if (dataFim.HasValue)
            query = query.Where(l => l.DataOcorrencia <= dataFim.Value);

        return await query
            .Include(l => l.Lote)
            .Include(l => l.LocalEstoque)
            .OrderBy(l => l.DataOcorrencia)
            .ThenBy(l => l.SequenciaGroupTenant)
            .ToListAsync();
    }

    public async Task<IEnumerable<LivroRazaoEstoque>> ObterSaidasPeriodoAsync(IEnumerable<Guid> produtoIds,
        DateTime dataInicio, DateTime dataFim)
    {
        return await DbSet
            .Where(l => produtoIds.Contains(l.ProdutoId) &&
                        l.DataOcorrencia >= dataInicio &&
                        l.DataOcorrencia <= dataFim &&
                        l.EhSaida())
            .Include(l => l.Produto)
            .Include(l => l.Lote)
            .Include(l => l.LocalEstoque)
            .OrderBy(l => l.DataOcorrencia)
            .ThenBy(l => l.SequenciaGroupTenant)
            .ToListAsync();
    }
}