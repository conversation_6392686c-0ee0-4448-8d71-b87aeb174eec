using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.Services;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Microsoft.Extensions.Logging;

namespace Bootis.Estoque.Infrastructure.Services;

public class ServicoConversaoUnidadeLote : IServicoConversaoUnidadeLote
{
    private readonly ILogger<ServicoConversaoUnidadeLote> _logger;
    private readonly IProdutoRepository _produtoRepository;
    private readonly IUnitOfWork _unitOfWork;

    public ServicoConversaoUnidadeLote(
        ILogger<ServicoConversaoUnidadeLote> logger,
        IUnitOfWork unitOfWork)
    {
        _logger = logger;
        _unitOfWork = unitOfWork;
        _produtoRepository = unitOfWork.GetRepository<IProdutoRepository>();
    }

    public async Task<decimal> ConverterParaUnidadeLote(
        decimal quantidade,
        UnidadeMedidaAbreviacao unidadeOrigem,
        UnidadeMedidaAbreviacao unidadeLote,
        Guid? produtoId = null)
    {
        if (unidadeOrigem == unidadeLote)
            return quantidade;

        await ValidarConversaoPossivel(unidadeOrigem, unidadeLote);

        var fatorConversao = await ObterFatorConversao(unidadeOrigem, unidadeLote, produtoId);

        var quantidadeConvertida = quantidade * fatorConversao;

        _logger.LogInformation(
            "Conversão: {Quantidade} {UnidadeOrigem} → {QuantidadeConvertida} {UnidadeLote} (Produto: {ProdutoId})",
            quantidade, unidadeOrigem, quantidadeConvertida, unidadeLote, produtoId);

        return quantidadeConvertida;
    }

    public async Task ValidarConversaoPossivel(
        UnidadeMedidaAbreviacao unidadeOrigem,
        UnidadeMedidaAbreviacao unidadeLote)
    {
        var conversao = ConversaoUnidadeMedidaCreator.Criar(unidadeOrigem, unidadeLote);

        if (conversao is null)
            throw new ValidationException(
                nameof(unidadeOrigem),
                $"Não é possível converter {unidadeOrigem} para {unidadeLote}");

        await Task.CompletedTask;
    }

    private async Task<decimal> ObterFatorConversao(
        UnidadeMedidaAbreviacao unidadeOrigem,
        UnidadeMedidaAbreviacao unidadeLote,
        Guid? produtoId)
    {
        var conversao = ConversaoUnidadeMedidaCreator.Criar(unidadeOrigem, unidadeLote);

        if (conversao is null)
            throw new ValidationException(
                nameof(unidadeOrigem),
                $"Não é possível converter {unidadeOrigem} para {unidadeLote}");

        var densidade = 1m;

        if (produtoId.HasValue)
        {
            var produto = await _produtoRepository.ObterProdutoAsync(produtoId.Value);
            densidade = produto?.ProdutoMateriaPrima?.Densidade ?? 1m;
        }

        // Calcular a conversão (quantidade = 1 para obter o fator)
        var fatorConversao = conversao.CalcularConversao(1m, densidade);

        await Task.CompletedTask;

        return fatorConversao;
    }
}