using Bootis.Estoque.Domain.AggregatesModel.LivroRazaoEstoqueAggregate;
using Bootis.Estoque.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.ValuesObject;

public class ProjecaoEstoqueProduto : ValueObject
{
    private ProjecaoEstoqueProduto(
        decimal mediaConsumoDiaria,
        decimal estoqueMinimoProjetado,
        decimal estoqueMaximoProjetado)
    {
        MediaConsumoDiaria = mediaConsumoDiaria;
        EstoqueMinimoProjetado = estoqueMinimoProjetado;
        EstoqueMaximoProjetado = estoqueMaximoProjetado;
    }

    public decimal MediaConsumoDiaria { get; }
    public decimal EstoqueMinimoProjetado { get; }
    public decimal EstoqueMaximoProjetado { get; }

    public static ProjecaoEstoqueProduto Criar(
        IEnumerable<LivroRazaoEstoque> movimentacoes,
        int diasMin,
        int diasMax,
        int diasInicio,
        int diasFim,
        DateTime dataReferencia)
    {
        if (diasInicio <= diasFim)
            throw new DomainException(Localizer.Instance.GetMessage_ProjecaoEstoque_IntervaloInvalido());

        var dataInicioPeriodo = dataReferencia.AddDays(-diasInicio);
        var dataFimPeriodo = dataReferencia.AddDays(-diasFim);

        // Para LivroRazaoEstoque, consumo é quantidade negativa (saídas)
        var totalConsumido = movimentacoes
            .Where(m => m.EhSaida())
            .Sum(m => m.QuantidadeAbsoluta());

        var diasDePeriodo = (dataFimPeriodo.Date - dataInicioPeriodo.Date).Days + 1;
        var media = diasDePeriodo > 0 ? totalConsumido / diasDePeriodo : 0;

        return new ProjecaoEstoqueProduto(
            media,
            media * diasMin,
            media * diasMax
        );
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return MediaConsumoDiaria;
        yield return EstoqueMinimoProjetado;
        yield return EstoqueMaximoProjetado;
    }
}