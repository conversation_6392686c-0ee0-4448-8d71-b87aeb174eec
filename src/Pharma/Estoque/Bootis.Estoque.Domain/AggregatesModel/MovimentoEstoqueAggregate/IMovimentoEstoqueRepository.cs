using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate;

public interface IMovimentoEstoqueRepository : IRepository<MovimentoEstoque>, IScopedService
{
    Task<MovimentoEstoque> ObterPorIdAsync(Guid id);

    Task<List<MovimentoEstoque>> ObterSaidasPeriodoAsync(IEnumerable<Guid> produtoIds, DateTime dataInicio,
        DateTime dataFim);
}