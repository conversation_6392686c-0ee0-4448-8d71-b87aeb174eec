using Bootis.Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Estoque.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Events;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate;

public class MovimentoEstoque() : Entity, IAggregateRoot, ITenant
{
    public MovimentoEstoque(DateTime dataLancamento,
        Guid empresaId,
        Guid loteId,
        Guid operacaoEstoqueId,
        TipoOperacao tipoOperacao,
        Guid localEstoqueId,
        decimal quantidade,
        UnidadeMedidaAbreviacao unidadeMedidaId,
        Guid usuarioId) : this()
    {
        DataLancamento = dataLancamento;
        EmpresaId = empresaId;
        LoteId = loteId;
        OperacaoEstoqueId = operacaoEstoqueId;
        TipoOperacao = tipoOperacao;
        LocalEstoqueId = localEstoqueId;
        Quantidade = quantidade;
        UnidadeMedidaId = unidadeMedidaId;
        UsuarioId = usuarioId;
    }

    public MovimentoEstoque(DateTime dataLancamento,
        Guid empresaId,
        Lote lote,
        Guid operacaoEstoqueId,
        TipoOperacao tipoOperacao,
        Guid localEstoqueId,
        decimal quantidade,
        UnidadeMedidaAbreviacao unidadeMedidaId,
        Guid usuarioId) : this()
    {
        DataLancamento = dataLancamento;
        EmpresaId = empresaId;
        Lote = lote;
        OperacaoEstoqueId = operacaoEstoqueId;
        TipoOperacao = tipoOperacao;
        LocalEstoqueId = localEstoqueId;
        Quantidade = quantidade;
        UnidadeMedidaId = unidadeMedidaId;
        UsuarioId = usuarioId;
    }

    public MovimentoEstoque(DateTime dataLancamento,
        Guid empresaId,
        Guid loteId,
        Guid operacaoEstoqueId,
        TipoOperacao tipoOperacao,
        Guid localEstoqueId,
        decimal quantidade,
        UnidadeMedidaAbreviacao unidadeMedidaId,
        Guid usuarioId,
        SaldoEstoque saldoEstoque,
        decimal quantidadeConvertida) : this()
    {
        DataLancamento = dataLancamento;
        EmpresaId = empresaId;
        LoteId = loteId;
        OperacaoEstoqueId = operacaoEstoqueId;
        TipoOperacao = tipoOperacao;
        LocalEstoqueId = localEstoqueId;
        Quantidade = quantidade;
        UnidadeMedidaId = unidadeMedidaId;
        UsuarioId = usuarioId;

        AjustarSaldoEstoque(this, saldoEstoque, quantidadeConvertida);
    }

    public DateTime DataLancamento { get; private set; }
    public Guid EmpresaId { get; private set; }
    public Guid LoteId { get; private set; }
    public Guid OperacaoEstoqueId { get; private set; }
    public TipoOperacao TipoOperacao { get; private set; }
    public Guid LocalEstoqueId { get; private set; }
    public decimal Quantidade { get; private set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; private set; }
    public decimal SaldoFinal { get; private set; }
    public Guid UsuarioId { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void AtualizarQuantidade(decimal quantidade)
    {
        Quantidade = quantidade;
    }

    public void AtualizarSaldoFinal(decimal saldoFinal)
    {
        SaldoFinal = saldoFinal;
    }

    private void AjustarSaldoEstoque(MovimentoEstoque movimentoEstoque, SaldoEstoque saldoEstoque,
        decimal quantidadeConvertida)
    {
        if (saldoEstoque == null) return;

        switch (movimentoEstoque.TipoOperacao)
        {
            case TipoOperacao.Entrada when saldoEstoque.Id != Guid.Empty:
                saldoEstoque.AtualizarSaldo(saldoEstoque.Saldo + quantidadeConvertida);
                break;

            case TipoOperacao.Saida when saldoEstoque.Saldo < movimentoEstoque.Quantidade:
                throw new ValidationException(nameof(movimentoEstoque.Quantidade),
                    Localizer.Instance
                        .GetMessage_LancamentoPerda_SaldoEstoqueInsuficiente(movimentoEstoque.Quantidade));

            case TipoOperacao.Saida:
                saldoEstoque.AtualizarSaldo(saldoEstoque.Saldo - quantidadeConvertida);
                break;
        }

        movimentoEstoque.AtualizarSaldoFinal(saldoEstoque.Saldo);

        DomainEvent.Raise(new SaldoAtualizadoEvent(saldoEstoque.Id, saldoEstoque.Saldo));
    }

    #region Navigation properties

    public virtual Lote Lote { get; set; }
    public virtual OperacaoEstoque OperacaoEstoque { get; set; }
    public virtual LocalEstoque LocalEstoque { get; set; }
    public virtual ICollection<AjusteSaldoEstoque> AjustesSaldoEstoque { get; set; } = new List<AjusteSaldoEstoque>();
    public virtual ICollection<Perda> LancamentosPerda { get; set; } = new List<Perda>();

    public virtual ICollection<TransferenciaLoteItens> TransferenciaLoteItensOrigem { get; set; } =
        new List<TransferenciaLoteItens>();

    public virtual ICollection<TransferenciaLoteItens> TransferenciaLoteItensDestino { get; set; } =
        new List<TransferenciaLoteItens>();

    #endregion
}