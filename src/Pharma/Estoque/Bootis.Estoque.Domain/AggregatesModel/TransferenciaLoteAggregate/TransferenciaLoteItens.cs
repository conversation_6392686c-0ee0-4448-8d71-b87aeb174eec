using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;

public class TransferenciaLoteItens : Entity, IAggregateRoot, ITenant
{
    public TransferenciaLoteItens()
    {
    }

    public TransferenciaLoteItens(Guid produtoId,
        Guid loteId,
        decimal quantidadeTransferida,
        UnidadeMedidaAbreviacao unidadeMedidaId,
        Guid transacaoSaidaId,
        Guid transacaoEntradaId,
        TransferenciaLote transferenciaLote) : this()
    {
        ProdutoId = produtoId;
        LoteId = loteId;
        QuantidadeTransferida = quantidadeTransferida;
        UnidadeMedidaId = unidadeMedidaId;
        TransacaoSaidaId = transacaoSaidaId;
        TransacaoEntradaId = transacaoEntradaId;
        TransferenciaLote = transferenciaLote;
    }

    public Guid ProdutoId { get; private set; }
    public Guid LoteId { get; private set; }
    public decimal QuantidadeTransferida { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; private set; }
    public Guid TransferenciaLoteId { get; private set; }
    public Guid TransacaoSaidaId { get; private set; } // Ref para LivroRazaoEstoque (saída)
    public Guid TransacaoEntradaId { get; private set; } // Ref para LivroRazaoEstoque (entrada)
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    #region Navigation properties

    public virtual Produto Produto { get; private set; }
    public virtual Lote Lote { get; private set; }
    public virtual TransferenciaLote TransferenciaLote { get; private set; }

    #endregion
}