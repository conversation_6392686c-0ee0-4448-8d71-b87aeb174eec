using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Domain.Dtos.TransferenciaLote;
using Bootis.Estoque.Resources;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;

public class TransferenciaLote() : Entity, IAggregateRoot, ITenant
{
    public TransferenciaLote(Guid usuarioId,
        LocalEstoque localDeEstoqueOrigem,
        LocalEstoque localDeEstoqueDestino,
        string observacao) : this()
    {
        DataTransferencia = DateTime.UtcNow;
        UsuarioId = usuarioId;
        LocalEstoqueOrigem = localDeEstoqueOrigem;
        LocalEstoqueDestino = localDeEstoqueDestino;
        Observacao = observacao;
    }

    public int SequenciaNumeroTransferencia { get; private set; }
    public DateTime DataTransferencia { get; private set; }
    public Guid LocalDeEstoqueOrigemId { get; private set; }
    public virtual LocalEstoque LocalEstoqueOrigem { get; private set; }
    public Guid LocalDeEstoqueDestinoId { get; private set; }
    public virtual LocalEstoque LocalEstoqueDestino { get; private set; }
    public virtual Usuario Usuario { get; private set; }
    public string Observacao { get; private set; }
    public Guid UsuarioId { get; private set; }


    #region Navigation properties

    public virtual ICollection<TransferenciaLoteItens> TransferenciaLoteItens { get; set; } =
        new List<TransferenciaLoteItens>();

    #endregion


    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public TransferenciaLoteItens AdicionarTransferenciaLoteItem(Produto produto, Lote lote,
        Guid transacaoSaidaId, Guid transacaoEntradaId, TransferenciaLoteItensDto transferenciaLoteItensDto)
    {
        var transferenciaLoteItens = new TransferenciaLoteItens(produto.Id,
            lote.Id,
            transferenciaLoteItensDto.QuantidadeTransferida,
            transferenciaLoteItensDto.UnidadeMedidaId,
            transacaoSaidaId,
            transacaoEntradaId,
            this);

        TransferenciaLoteItens.Add(transferenciaLoteItens);

        return transferenciaLoteItens;
    }

    public static void ValidarSaldoEstoqueTransferenciaLoteItens(SaldoEstoque saldoEstoque, Produto produto,
        TransferenciaLoteItensDto transferenciaLoteItensDto)
    {
        if (saldoEstoque.Produto.Id != produto.Id)
            throw new ValidationException(nameof(transferenciaLoteItensDto.ProdutoId),
                Localizer.Instance.GetMessage_LocalEstoque_NaoPossuiOProduto(
                    transferenciaLoteItensDto.ProdutoId));

        if (saldoEstoque.Saldo < transferenciaLoteItensDto.QuantidadeTransferida)
            throw new ValidationException(nameof(saldoEstoque),
                Localizer.Instance.GetMessage_Lote_NaoPossuiQuantidadeDeProduto(
                    transferenciaLoteItensDto.LoteId, transferenciaLoteItensDto.QuantidadeTransferida));
    }
}