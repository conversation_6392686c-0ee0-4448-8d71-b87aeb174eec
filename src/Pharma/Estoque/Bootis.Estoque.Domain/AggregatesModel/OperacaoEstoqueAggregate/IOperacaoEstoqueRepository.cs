using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate;

public interface IOperacaoEstoqueRepository : IRepository<OperacaoEstoque>, IScopedService
{
    Task<OperacaoEstoque> ObterOperacaoEstoquePorDescricaoETipo(string descricao, TipoOperacao tipoOperacao);

    Task<OperacaoEstoque> ObterOperacaoEstoquePorDescricaoETipoAsync(TipoOperacaoEstoque tipoOperacaoEstoque,
        TipoOperacao tipoOperacao);

    Task<OperacaoEstoque> ObterOperacaoEstoquePorTipoAsync(TipoOperacao tipoOperacao);

    Task<OperacaoEstoque> ObterOperacaoEstoqueInventarioAsync();
}