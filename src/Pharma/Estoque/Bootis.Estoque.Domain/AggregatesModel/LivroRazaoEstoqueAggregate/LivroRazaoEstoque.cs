using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.LivroRazaoEstoqueAggregate;

public class LivroRazaoEstoque : Entity, IAggregateRoot, ITenant
{
    private LivroRazaoEstoque()
    {
    }

    public LivroRazaoEstoque(
        Guid transacaoId,
        Guid produtoId,
        Guid loteId,
        Guid localEstoqueId,
        Guid empresaId,
        decimal quantidade,
        TipoTransacaoEstoque tipoTransacao,
        string motivo,
        Guid usuarioId,
        string moduloOrigem)
    {
        TransacaoId = transacaoId;
        DataOcorrencia = DateTime.UtcNow;
        ProdutoId = produtoId;
        LoteId = loteId;
        LocalEstoqueId = localEstoqueId;
        EmpresaId = empresaId;
        Quantidade = quantidade;
        TipoTransacao = tipoTransacao;
        Motivo = motivo;
        UsuarioId = usuarioId;
        NomeUsuario = string.Empty;
        ModuloOrigem = moduloOrigem;
        Metadados = new Dictionary<string, object>();
    }

    public Guid TransacaoId { get; private set; }
    public int SequenciaGroupTenant { get; private set; }

    public DateTime DataOcorrencia { get; private set; }
    public Guid ProdutoId { get; private set; }
    public Guid LoteId { get; private set; }
    public Guid LocalEstoqueId { get; private set; }
    public Guid EmpresaId { get; private set; }

    public decimal Quantidade { get; }
    public UnidadeMedidaAbreviacao UnidadeMedidaPadraoLote { get; private set; }

    public TipoTransacaoEstoque TipoTransacao { get; private set; }
    public string Motivo { get; private set; }
    public Guid? DocumentoOrigemId { get; private set; }
    public string TipoDocumentoOrigem { get; private set; }

    public Guid UsuarioId { get; private set; }
    public string NomeUsuario { get; private set; }
    public string ModuloOrigem { get; private set; }
    public Dictionary<string, object> Metadados { get; }

    public virtual Produto Produto { get; private set; }
    public virtual Lote Lote { get; private set; }
    public virtual LocalEstoque LocalEstoque { get; private set; }
    public virtual Empresa Empresa { get; private set; }

    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void DefinirUnidadePadraoLote(UnidadeMedidaAbreviacao unidadePadraoLote)
    {
        UnidadeMedidaPadraoLote = unidadePadraoLote;
    }

    public void DefinirDocumentoOrigem(Guid? documentoOrigemId, string tipoDocumentoOrigem)
    {
        DocumentoOrigemId = documentoOrigemId;
        TipoDocumentoOrigem = tipoDocumentoOrigem;
    }

    public void DefinirNomeUsuario(string nomeUsuario)
    {
        NomeUsuario = nomeUsuario;
    }

    public void AdicionarMetadado(string chave, object valor)
    {
        Metadados[chave] = valor;
    }

    public bool EhEntrada()
    {
        return Quantidade > 0;
    }

    public bool EhSaida()
    {
        return Quantidade < 0;
    }

    public decimal QuantidadeAbsoluta()
    {
        return Math.Abs(Quantidade);
    }
}