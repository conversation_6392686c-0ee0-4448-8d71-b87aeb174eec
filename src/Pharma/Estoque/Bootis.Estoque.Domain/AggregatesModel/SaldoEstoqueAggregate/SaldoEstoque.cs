using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Resources;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;

public class SaldoEstoque : Entity, IAggregateRoot, ITenant
{
    public SaldoEstoque()
    {
    }

    public SaldoEstoque(Guid empresaId,
        Guid produtoId,
        Guid loteId,
        decimal saldo,
        UnidadeMedidaAbreviacao unidadeMedidaId,
        Guid localEstoqueId) : this()
    {
        EmpresaId = empresaId;
        ProdutoId = produtoId;
        LoteId = loteId;
        LocalEstoqueId = localEstoqueId;
        Saldo = saldo;
        UnidadeMedidaId = unidadeMedidaId;
        UltimaAtualizacao = DateTime.UtcNow;
        Versao = 0;
    }

    public SaldoEstoque(Empresa empresa,
        Produto produto,
        Guid loteId,
        decimal saldo,
        UnidadeMedidaAbreviacao unidadeMedidaId,
        Guid localEstoqueId) : this()
    {
        Empresa = empresa;
        Produto = produto;
        LoteId = loteId;
        LocalEstoqueId = localEstoqueId;
        Saldo = saldo;
        UnidadeMedidaId = unidadeMedidaId;
        UltimaAtualizacao = DateTime.UtcNow;
        Versao = 0;
    }

    public Guid EmpresaId { get; private set; }
    public virtual Empresa Empresa { get; set; }
    public Guid ProdutoId { get; private set; }
    public virtual Produto Produto { get; private set; }
    public Guid LoteId { get; private set; }
    public virtual Lote Lote { get; private set; }
    public Guid LocalEstoqueId { get; private set; }
    public virtual LocalEstoque LocalEstoque { get; private set; }
    public decimal Saldo { get; private set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; private set; }
    public bool Bloqueado { get; private set; }
    public DateTime UltimaAtualizacao { get; private set; }
    public Guid? UltimaTransacaoId { get; private set; }
    public int Versao { get; private set; } // Para controle de concorrência
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void AtualizarSaldo(decimal saldo, Guid? transacaoId)
    {
        if (Bloqueado) throw new DomainException(Localizer.Instance.GetMessage_SaldoEstoque_SaldoBloqueado(Id));
        if (saldo < 0) throw new DomainException("Saldo não pode ser negativo");

        Saldo = saldo;
        UltimaTransacaoId = transacaoId;
        UltimaAtualizacao = DateTime.UtcNow;
        Versao++;
    }

    public void AtualizarUnidadeMedida(UnidadeMedidaAbreviacao unidadeMedidaId)
    {
        UnidadeMedidaId = unidadeMedidaId;
    }

    public void BloquearSaldo()
    {
        Bloqueado = true;
    }

    public void DesbloquearSaldo()
    {
        Bloqueado = false;
    }
}