using Bootis.Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;

public interface ISaldoEstoqueRepository : IRepository<SaldoEstoque>, IScopedService
{
    Task<SaldoEstoque> ObterSaldoEstoquePorLoteLocalEstoqueIdAsync(Guid loteId, Guid localEstoqueId);
    Task<SaldoEstoque[]> ObterSaldoEstoquePorProdutoAsync(Guid produtoId);
    Task<SaldoEstoque> ObterSaldoEstoquePorLoteLocalEstoqueAsync(Guid loteId, Guid localEstoqueId);
    Task<List<SaldoEstoque>> ObterSaldosEstoquePorLoteLocalEstoqueAsync(IEnumerable<Guid> loteIds, Guid localEstoqueId);
    Task<SaldoEstoque[]> ObterSaldosEstoquePorLoteAsync(Guid loteId);
    void Add(AjusteSaldoEstoque ajusteSaldoEstoque);
    void Update(AjusteSaldoEstoque ajusteSaldoEstoque);
    void Remove(AjusteSaldoEstoque ajusteSaldoEstoque);
}