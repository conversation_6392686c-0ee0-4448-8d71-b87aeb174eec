using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.AjusteSaldoEstoqueAggregate;

public class AjusteSaldoEstoque : Entity, IAggregateRoot, ITenant
{
    protected AjusteSaldoEstoque()
    {
    }

    public AjusteSaldoEstoque(Guid localEstoqueId,
        Guid loteId,
        decimal quantidadeDoAjuste,
        decimal saldoAnterior,
        UnidadeMedidaAbreviacao unidadeMedidaId,
        Guid operadorId,
        TipoOperacao tipoOperacao,
        Guid transacaoId)
    {
        LocalEstoqueId = localEstoqueId;
        LoteId = loteId;
        QuantidadeDoAjuste = quantidadeDoAjuste;
        UnidadeMedidaId = unidadeMedidaId;
        DataLancamento = DateTime.UtcNow;
        OperadorId = operadorId;
        SaldoAnterior = saldoAnterior;
        TipoOperacao = tipoOperacao;
        TransacaoId = transacaoId;
    }

    public Guid LocalEstoqueId { get; private set; }
    public Guid LoteId { get; private set; }
    public decimal QuantidadeDoAjuste { get; private set; }
    public decimal SaldoAnterior { get; private set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; private set; }
    public DateTime DataLancamento { get; private set; }
    public Guid OperadorId { get; private set; }
    public TipoOperacao TipoOperacao { get; private set; }
    public Guid TransacaoId { get; private set; } // Ref para LivroRazaoEstoque
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    #region Navigation properties

    public virtual LocalEstoque LocalEstoque { get; set; }
    public virtual Lote Lote { get; set; }
    public virtual Usuario Operador { get; set; }

    #endregion
}