using Bootis.Catalogo.Domain.AggregatesModel.GrupoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LivroRazaoEstoqueAggregate;
using Bootis.Estoque.Domain.ValuesObject;
using Bootis.Estoque.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Domain.AggregatesModel.ProjecaoEstoque;

public class ProjecaoEstoque
{
    private readonly DateTime _dataAtual;
    private readonly bool _definirPeriodoPersonalizado;
    private readonly int? _diasEstoqueMaximo;
    private readonly int? _diasEstoqueMinimo;
    private readonly int? _diasFimProjecao;
    private readonly int? _diasInicioProjecao;
    private readonly List<Produto> _produtos;
    private readonly bool _usarDadosGrupo;

    public ProjecaoEstoque(
        List<Produto> produtos,
        bool definirPeriodoPersonalizado,
        bool usarDadosGrupo,
        int? diasEstoqueMinimo,
        int? diasEstoqueMaximo,
        int? diasInicioProjecao,
        int? diasFimProjecao)
    {
        _produtos = produtos;
        _definirPeriodoPersonalizado = definirPeriodoPersonalizado;
        _usarDadosGrupo = usarDadosGrupo;
        _diasEstoqueMinimo = diasEstoqueMinimo;
        _diasEstoqueMaximo = diasEstoqueMaximo;
        _diasInicioProjecao = diasInicioProjecao;
        _diasFimProjecao = diasFimProjecao;
        _dataAtual = DateTime.UtcNow.Date;

        var (_, semDados) = SepararProdutosPorGrupo(produtos);
        RequerDadosManuais = semDados.Any();

        DataInicioMovimentacoes = CalcularMaiorDataInicioMovimentacoes(produtos);
        DataFimMovimentacoes = CalcularMenorDataFimMovimentacoes(produtos);
    }

    public DateTime DataInicioMovimentacoes { get; private set; }
    public DateTime DataFimMovimentacoes { get; private set; }
    public bool RequerDadosManuais { get; private set; }

    public List<ResultadoProjecaoEstoque> Calcular(List<LivroRazaoEstoque> todasMovimentacoes)
    {
        var resultado = new List<ResultadoProjecaoEstoque>();

        foreach (var produto in _produtos)
        {
            var parametros = ObterParametrosParaProduto(produto);

            var dataInicio = _dataAtual.AddDays(-parametros.DiasConsumoInicio);
            var dataFim = _dataAtual.AddDays(-parametros.DiasConsumoFim);

            var movimentacoesDoProduto = todasMovimentacoes
                .Where(m => m.ProdutoId == produto.Id &&
                            m.DataOcorrencia.Date >= dataInicio &&
                            m.DataOcorrencia.Date <= dataFim)
                .ToList();

            if (!movimentacoesDoProduto.Any()) continue;

            var projecao = ProjecaoEstoqueProduto.Criar(
                movimentacoesDoProduto,
                parametros.DiasMinEstoque,
                parametros.DiasMaxEstoque,
                parametros.DiasConsumoInicio,
                parametros.DiasConsumoFim,
                _dataAtual);

            if (projecao == null) continue;

            resultado.Add(new ResultadoProjecaoEstoque(
                produto.Id,
                produto.SequenciaGroupTenant,
                produto.Descricao,
                produto.SubGrupo?.Grupo?.Descricao ?? string.Empty,
                produto.UnidadeEstoqueId,
                projecao.MediaConsumoDiaria,
                projecao.EstoqueMinimoProjetado,
                projecao.EstoqueMaximoProjetado
            ));
        }

        return resultado;
    }

    private static (List<Produto> comDados, List<Produto> semDados) SepararProdutosPorGrupo(List<Produto> produtos)
    {
        var lookup = produtos.ToLookup(p => p.SubGrupo?.Grupo == null || GrupoSemDados(p.SubGrupo.Grupo));
        return (lookup[false].ToList(), lookup[true].ToList());
    }

    private static bool GrupoSemDados(Grupo grupo)
    {
        if (grupo == null) return true;
        return !grupo.DiasEstoqueMinimo.HasValue
               || !grupo.DiasInicioProjecao.HasValue
               || !grupo.DiasFimProjecao.HasValue;
    }

    private DateTime CalcularMaiorDataInicioMovimentacoes(IEnumerable<Produto> produtos)
    {
        if (!produtos.Any()) return _dataAtual;

        var diasGrupo = produtos
            .Where(p => p.SubGrupo?.Grupo?.DiasInicioProjecao != null)
            .Select(p => p.SubGrupo.Grupo!.DiasInicioProjecao!.Value)
            .DefaultIfEmpty()
            .Max();

        var diasManual = _diasInicioProjecao ?? 0;

        var dias = Math.Max(diasGrupo, diasManual);

        return _dataAtual.AddDays(-dias);
    }

    private DateTime CalcularMenorDataFimMovimentacoes(IEnumerable<Produto> produtos)
    {
        if (!produtos.Any()) return _dataAtual;

        var diasGrupo = produtos
            .Where(p => p.SubGrupo?.Grupo?.DiasFimProjecao != null)
            .Select(p => p.SubGrupo.Grupo!.DiasFimProjecao!.Value)
            .DefaultIfEmpty(int.MaxValue)
            .Min();

        var diasManual = _diasFimProjecao ?? int.MaxValue;

        var dias = Math.Min(diasGrupo, diasManual);

        return _dataAtual.AddDays(-(dias == int.MaxValue ? 0 : dias));
    }

    private ParametrosProjecao ObterParametrosParaProduto(Produto produto)
    {
        var grupo = produto.SubGrupo?.Grupo;
        var usarManual = DeveUsarDadosManuais(produto);

        if (usarManual)
        {
            if (!_diasEstoqueMinimo.HasValue || !_diasInicioProjecao.HasValue || !_diasFimProjecao.HasValue)
                throw new DomainException(Localizer.Instance.GetMessage_ProjecaoEstoque_DadosIncompletos());

            return new ParametrosProjecao(
                _diasEstoqueMinimo.Value,
                _diasEstoqueMaximo ?? 0,
                _diasInicioProjecao.Value,
                _diasFimProjecao.Value
            );
        }

        return new ParametrosProjecao(
            grupo!.DiasEstoqueMinimo!.Value,
            grupo.DiasEstoqueMaximo ?? 0,
            grupo.DiasInicioProjecao!.Value,
            grupo.DiasFimProjecao!.Value
        );
    }

    private bool DeveUsarDadosManuais(Produto produto)
    {
        return !_usarDadosGrupo || GrupoSemDados(produto.SubGrupo?.Grupo);
    }

    private record ParametrosProjecao(
        int DiasMinEstoque,
        int DiasMaxEstoque,
        int DiasConsumoInicio,
        int DiasConsumoFim);

    public record ResultadoProjecaoEstoque(
        Guid ProdutoId,
        int Codigo,
        string Descricao,
        string GrupoDescricao,
        UnidadeMedidaAbreviacao UnidadeMedidaEstoqueId,
        decimal MediaConsumoDiaria,
        decimal EstoqueMinimoProjetado,
        decimal? EstoqueMaximoProjetado);
}