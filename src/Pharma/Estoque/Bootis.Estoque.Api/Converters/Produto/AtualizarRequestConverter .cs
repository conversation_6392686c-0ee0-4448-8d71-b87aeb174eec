using Bootis.Catalogo.Application.Requests.Produto.Atualizar;
using Bootis.Shared.Api.Converter;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Api.Converters.Produto;

public class AtualizarRequestConverter()
    : AbstractConverter<AtualizarRequest, TipoClasseProdutoAbreviacao>(nameof(AtualizarRequest.ClasseProdutoId))
{
    private static readonly Dictionary<TipoClasseProdutoAbreviacao, Type> Types =
        new()
        {
            { TipoClasseProdutoAbreviacao.MateriaPrima, typeof(AtualizarMateriaPrimaRequest) },
            { TipoClasseProdutoAbreviacao.Embalagem, typeof(AtualizarEmbalagemRequest) },
            { TipoClasseProdutoAbreviacao.TipoCapsula, typeof(AtualizarTipoCapsulaRequest) },
            { TipoClasseProdutoAbreviacao.CapsulaPronta, typeof(AtualizarCapsulaProntaRequest) },
            { TipoClasseProdutoAbreviacao.ProdutoAcabado, typeof(AtualizarBaseRequest) },
            { TipoClasseProdutoAbreviacao.Servico, typeof(AtualizarBaseRequest) },
            { TipoClasseProdutoAbreviacao.UsoConsumo, typeof(AtualizarBaseRequest) }
        };

    protected override Type GetType(TipoClasseProdutoAbreviacao typeId)
    {
        return Types[typeId];
    }
}

public class NAtualizarRequestConverter() : Shared.Api.Converter.NewtonSoft.AbstractConverter<AtualizarRequest,
    TipoClasseProdutoAbreviacao>(nameof(AtualizarRequest.ClasseProdutoId))
{
    private static readonly Dictionary<TipoClasseProdutoAbreviacao, Type> Types =
        new()
        {
            { TipoClasseProdutoAbreviacao.MateriaPrima, typeof(AtualizarMateriaPrimaRequest) },
            { TipoClasseProdutoAbreviacao.Embalagem, typeof(AtualizarEmbalagemRequest) },
            { TipoClasseProdutoAbreviacao.TipoCapsula, typeof(AtualizarTipoCapsulaRequest) },
            { TipoClasseProdutoAbreviacao.CapsulaPronta, typeof(AtualizarCapsulaProntaRequest) },
            { TipoClasseProdutoAbreviacao.ProdutoAcabado, typeof(AtualizarBaseRequest) },
            { TipoClasseProdutoAbreviacao.Servico, typeof(AtualizarBaseRequest) },
            { TipoClasseProdutoAbreviacao.UsoConsumo, typeof(AtualizarBaseRequest) }
        };

    protected override Type GetType(TipoClasseProdutoAbreviacao typeId)
    {
        return Types[typeId];
    }
}