using System.Data;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Interfaces;
using Dapper;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.ReceitaManipulada.Obter;

public class ObterHistoricoTecnicoRequestHandler(
    IUserContext userContext,
    IDbConnection connection,
    IReceitaManipuladaRepository receitaManipuladaRepository)
    : IRequestHandler<ObterHistoricoTecnicoRequest, ObterHistoricoTecnicoResponse>, IScopedService
{
    public async Task<ObterHistoricoTecnicoResponse> Handle(ObterHistoricoTecnicoRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                            SELECT rht.id,
                                   rht.data_movimento AS DataHora,
                                   rht.tipo_registro AS TipoRegistro,
                                   rht.descricao AS Descricao,
                                   rht.produtos_envolvidos AS ProdutosEnvolvidos,
                                   CONCAT(us.nome, ' ', us.sobrenome) AS Usuario
                              FROM receitas_manipuladas_historico_tecnico rht
                                   LEFT JOIN usuarios us ON
                                             us.id = rht.usuario_id
                                   LEFT JOIN receitas_manipuladas rm ON
                                             rm.id = rht.receita_manipulada_id
                             WHERE rm.id = @ReceitaManipuladaId
                               AND rm.group_tenant_id = @GroupTenantId
                             ORDER BY rht.data_movimento DESC
                           """;

        var historicoTecnico = await connection.QueryAsync<HistoricoTecnicoReceita>(sql,
            new { request.ReceitaManipuladaId, groupTenantId = userContext.GroupTenantId });
        
        await receitaManipuladaRepository.ObterReceitaManipuladaPorIdAsync(request.ReceitaManipuladaId);

        return new ObterHistoricoTecnicoResponse
        {
            HistoricoTecnico = historicoTecnico.ToList()
        };
    }
}
