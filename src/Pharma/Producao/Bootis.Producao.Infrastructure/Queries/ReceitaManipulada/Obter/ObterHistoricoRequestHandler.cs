using System.Data;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Shared.Application.Interfaces;
using Dapper;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.ReceitaManipulada.Obter;

public class ObterHistoricoRequestHandler(
    IUserContext userContext,
    IDbConnection connection,
    IReceitaManipuladaRepository receitaManipuladaRepository)
    : IRequestHandler<ObterHistoricoRequest, ObterHistoricoResponse>
{
    public async Task<ObterHistoricoResponse> Handle(ObterHistoricoRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                            SELECT rh.id,
                                   rh.data_hora AS DataHora,
                                   rh.status AS Status,
                                   rh.estorno AS Estorno,
                                   CONCAT(us.nome, ' ', us.sobrenome) AS Usuario
                              FROM receitas_manipuladas_historico rh
                                   LEFT JOIN usuarios us ON
                                             us.id = rh.usuario_id
                                   LEFT JOIN receitas_manipuladas rm ON
                                             rm.id = rh.receita_manipulada_id
                             WHERE rm.id = @ReceitaManipuladaId
                               AND rm.group_tenant_id = @GroupTenantId
                           """;

        var historico = await connection.QueryAsync<HistoricoReceita>(sql,
            new { request.ReceitaManipuladaId, groupTenantId = userContext.GroupTenantId });
        
        await receitaManipuladaRepository.ObterReceitaManipuladaPorIdAsync(request.ReceitaManipuladaId);

        var response = new ObterHistoricoResponse
        {
            Historico = historico.OrderByDescending(h => h.DataHora).ToList()
        };
        
        var primeiro = response.Historico.OrderBy(h => h.DataHora).FirstOrDefault();
        if (primeiro != null)
            primeiro.EhCriacao = true;

        return response;
    }
}