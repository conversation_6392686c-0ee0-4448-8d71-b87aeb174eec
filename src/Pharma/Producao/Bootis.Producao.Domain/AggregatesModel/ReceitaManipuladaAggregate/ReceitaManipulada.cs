using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ModeloOrdemManipulacaoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate;
using Bootis.Producao.Domain.AggregatesModel.PosologiaAggregate;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Domain.Services.ReceitaManipulada;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;

public class ReceitaManipulada : Entity, IAggregateRoot, ITenant
{
    public ReceitaManipulada()
    {
    }

    public ReceitaManipulada(
        Cliente paciente,
        Prescritor prescritor,
        DateOnly dataPrescricao,
        FormaFarmaceutica formaFarmaceutica,
        decimal quantidadeReceita,
        int quantidadeRepetir,
        decimal? quantidadeDose)
    {
        InitializePatientAndPrescriber(paciente, prescritor, formaFarmaceutica);

        PacienteId = paciente.Id;
        DataPrescricao = dataPrescricao;
        FormaFarmaceuticaId = formaFarmaceutica.Id;
        QuantidadeReceita = quantidadeReceita;
        QuantidadeRepetir = quantidadeRepetir;
        QuantidadeDose = quantidadeDose;
        Status = StatusReceita.Orcada;
        DataEmissao = DateTime.UtcNow;
        PrevisaoEntrega = DateTime.UtcNow.AddDays(7); //TODO: aguardando definição do time de negócios
        CodigoBarras = "0123456789012"; //TODO: aguardando definição do time de negócios
    }

    public Guid PacienteId { get; private set; }
    public Guid FormaFarmaceuticaId { get; private set; }
    public Guid? PosologiaId { get; private set; }
    public Guid? PrescritorId { get; private set; }
    public decimal QuantidadeReceita { get; private set; }
    public decimal? QuantidadeDose { get; private set; }
    public int QuantidadeRepetir { get; private set; }
    public string Observacao { get; private set; }
    public string CodigoBarras { get; private set; }
    public DateTime DataEmissao { get; private set; }
    public DateOnly DataPrescricao { get; private set; }
    public DateTime PrevisaoEntrega { get; private set; }
    public DateTime DataValidadePrescricao { get; private set; }
    public TipoTarjaMedicamento? TarjaReceitaId { get; private set; }
    public StatusReceita Status { get; private set; }

    public Guid? ModeloRotuloId { get; set; }
    public StatusImpressao StatusImpressaoRotulo { get; set; }
    public Guid? ModeloOrdemManipulacaoId { get; set; }
    public StatusImpressao StatusImpressaoOrdemManipulacao { get; set; }
    public int SequenciaGroupTenant { get; private set; }
    public TipoDesconto? TipoDescontoManual { get; private set; }
    public decimal? DescontoManual { get; private set; }
    public decimal? PercentualDescontoManual { get; private set; }
    public TipoCalculoUsoContinuo? TipoUsoContinuo { get; private set; }
    public bool UsoContinuo { get; private set; }
    public decimal? QuantidadeReSell { get; private set; }
    public PeriodosPosologia? PeriodicidadeReSell { get; private set; }
    public int? DuracaoPrevistaReSellDias { get; private set; }
    public DateTime? PrevisaoTerminoReSell { get; private set; }
    
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    private void LimparCamposReSell()
    {
        TipoUsoContinuo = null;
        QuantidadeReSell = null;
        PeriodicidadeReSell = null;
        DuracaoPrevistaReSellDias = null;
        PrevisaoTerminoReSell = null;
    }

    public void DesativarReSell()
    {
        UsoContinuo = false;
        LimparCamposReSell();
    }

    public void DefinirReSellPorDose(decimal quantDoses, PeriodosPosologia periodicidade)
    {
        if (quantDoses <= 0)
            throw new DomainException("Quantidade de Doses deve ser maior que zero.");

        UsoContinuo = true;
        TipoUsoContinuo = TipoCalculoUsoContinuo.PorDose;
        QuantidadeReSell = quantDoses;
        PeriodicidadeReSell = periodicidade;
        DuracaoPrevistaReSellDias = null;
        PrevisaoTerminoReSell = null;
    }

    public void DefinirReSellPorDuracao(decimal quantDoses, PeriodosPosologia periodicidade)
    {
        if (quantDoses <= 0)
            throw new DomainException("Quantidade de Doses deve ser maior que zero.");

        UsoContinuo = true;
        TipoUsoContinuo = TipoCalculoUsoContinuo.PorDuracao;
        QuantidadeReSell = quantDoses;
        PeriodicidadeReSell = periodicidade;
        DuracaoPrevistaReSellDias = null;
        PrevisaoTerminoReSell = null;
    }

    public void AplicarResultadoReSell(int duracaoDias, DateTime terminoUtc)
    {
        if (!UsoContinuo || TipoUsoContinuo is null)
            throw new DomainException("ReSell não configurado para aplicar resultado.");

        if (duracaoDias < 0) duracaoDias = 0;

        DuracaoPrevistaReSellDias = duracaoDias;
        PrevisaoTerminoReSell = terminoUtc;
    }
    
    private void InitializePatientAndPrescriber(Cliente paciente, Prescritor prescritor,
        FormaFarmaceutica formaFarmaceutica)
    {
        Paciente = paciente;
        Prescritor = prescritor;
        FormaFarmaceutica = formaFarmaceutica;
    }

    public void AlterarPaciente(Cliente paciente)
    {
        Paciente = paciente;
        PacienteId = paciente.Id;
    }

    public void AlterarPrescritor(Prescritor prescritor)
    {
        Prescritor = prescritor;
        PrescritorId = prescritor?.Id;
    }

    public void AlterarPosologia(Guid? posologiaId)
    {
        PosologiaId = posologiaId;
    }

    public async Task AlterarCapsulaAsync(Guid produtoCapsulaId, IReceitaManipuladaDomainService receitaService)
    {
        var caspula = await receitaService.ObterProdutoTipoCapsulaPorIdAsync(produtoCapsulaId);

        if (caspula.TipoCapsula is null)
            throw new DomainException("O produto informado não é uma cápsula válida.");

        var itemCapsula = Itens
            .FirstOrDefault(i => i.Produto.ClasseProdutoId == TipoClasseProdutoAbreviacao.TipoCapsula);

        if (itemCapsula is not null)
            Itens.Remove(itemCapsula);

        var ordem = Itens.Any()
            ? Itens.Max(i => i.Ordem) + 1
            : 1;

        AdicionarItemReceita(
            caspula.Produto,
            caspula.Produto.Descricao,
            0,
            UnidadeMedidaAbreviacao.un.ToInt(),
            TipoComponente.Normal,
            ordem,
            true
        );
    }

    public Task AlterarEmbalagemAsync(ProdutoEmbalagem novaEmbalagem)
    {
        if (novaEmbalagem is null)
            throw new DomainException("A embalagem fornecida é inválida.");

        var embalagem = novaEmbalagem.Produto;

        var itemEmbalagem = Itens
            .FirstOrDefault(i => i.Produto.ClasseProdutoId == TipoClasseProdutoAbreviacao.Embalagem);

        if (itemEmbalagem is not null)
            Itens.Remove(itemEmbalagem);

        var ordem = Itens.Any()
            ? Itens.Max(i => i.Ordem) + 1
            : 1;

        AdicionarItemReceita(
            embalagem,
            embalagem.Descricao,
            1,
            UnidadeMedidaAbreviacao.un.ToInt(),
            TipoComponente.Normal,
            ordem,
            true
        );
        return Task.CompletedTask;
    }

    public void AlterarFormaFarmaceutica(FormaFarmaceutica forma)
    {
        if (forma is null)
            throw new DomainException("Forma farmacêutica não pode ser nula.");

        FormaFarmaceutica = forma;
        FormaFarmaceuticaId = forma.Id;
    }

    public void AlterarQuantidadeReceita(decimal novaQuantidade)
    {
        QuantidadeReceita = novaQuantidade;
    }

    public void AlterarQuantidadeRepetir(int repetir)
    {
        QuantidadeRepetir = repetir;
    }

    public void AlterarQuantidadeDose(decimal? novaDose)
    {
        QuantidadeDose = novaDose;
    }

    public void AlterarObservacao(string observacao)
    {
        Observacao = observacao;
    }

    public void DefinirDescontoManual(decimal desconto, decimal percentual, TipoDesconto tipo)
    {
        TipoDescontoManual = tipo;

        DescontoManual = tipo == TipoDesconto.DescontoMonetario ? desconto : null;
        PercentualDescontoManual = tipo == TipoDesconto.DescontoPorcentagem ? percentual : null;
    }

    public void AdicionarItemReceita(Produto produto, string descricao, decimal quantidade, int unidadeMedidaId,
        TipoComponente tipo, int ordem, bool ocultaRotulo, ProdutoSinonimo sinonimo = null,
        decimal? fatorEquivalencia = null, decimal? fatorCorrecao = null)
    {
        if (Itens.Any(i => i.Ordem == ordem))
            throw new DomainException($"Já existe um item com ordem {ordem}");

        if (produto.ClasseProdutoId == TipoClasseProdutoAbreviacao.Embalagem)
            ModeloRotuloId = produto.ProdutoEmbalagem.ModeloRotuloEmbalagemAssociacao.SingleOrDefault(
                c => c.FormaFarmaceuticaId == FormaFarmaceuticaId || c.FormaFarmaceuticaId == null)?.ModeloRotuloId;

        var item = sinonimo != null
            ? new ReceitaManipuladaItemSinonimo(sinonimo, fatorEquivalencia, fatorCorrecao, this, produto, descricao,
                quantidade, unidadeMedidaId, tipo, ordem,
                ocultaRotulo)
            : new ReceitaManipuladaItem(this, produto, descricao, quantidade, unidadeMedidaId, tipo, ordem,
                ocultaRotulo);

        Itens.Add(item);
    }

    public void AplicarResultadoCalculo(CalculoReceitaManipulada resultado)
    {
        BaseCalculo = resultado.BaseCalculo;
        Valores = resultado.Valores;
        TarjaReceitaId = resultado.TarjaReceitaIdResultado;
        DataValidadePrescricao = resultado.BaseCalculo.DataValidade.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);

        CalculosItens.Clear();
        CalculosItens.AddRange(resultado.Calculos);

        RastreioCalculos.Clear();
        RastreioCalculos.AddRange(resultado.Rastreios);
    }

    public void LimparItens()
    {
        Itens.Clear();
    }

    public void GerarHistorico(Guid usuarioId, StatusReceita status)
    {
        var receitaHistorico = new ReceitaManipuladaHistorico(usuarioId, this, status);

        Historico.Add(receitaHistorico);
    }

    public void AtualizarStatus(StatusReceita novoStatus, Guid usuarioId)
    {
        if (Status == novoStatus) return;

        Status = novoStatus;

        var historico = new ReceitaManipuladaHistorico(usuarioId, this, novoStatus);
        Historico.Add(historico);
    }

    public void CancelarReceita(Guid usuarioId, string motivoCancelamento)
    {
        if (Status is StatusReceita.Cancelada) return;

        Status = StatusReceita.Cancelada;

        var historico = new ReceitaManipuladaHistorico(usuarioId, this, StatusReceita.Cancelada, motivoCancelamento);
        Historico.Add(historico);
    }

    public void Estornar(Guid usuarioId)
    {
        if (Status != StatusReceita.ConferenciaFinalizada)
            throw new DomainException(
                $"Não é possível estornar a conferência. A receita deve estar com status 'Conferência Finalizada', mas está com status '{Status.GetDescription()}'.");

        Status = StatusReceita.AguardandoConferencia;

        var historico = new ReceitaManipuladaHistorico(usuarioId, this, StatusReceita.AguardandoConferencia, true);
        Historico.Add(historico);
    }

    public void CriarEdicaoRotulo(Guid usuarioId, Guid? prescritoUfRegistroId, Guid? tipoRegistroId,
        IReceitaManipuladaRotuloDto receitaManipuladaEtiquetaDto)
    {
        EdicaoEtiqueta = new ReceitaManipuladaRotulo(
            Id,
            usuarioId,
            prescritoUfRegistroId,
            tipoRegistroId,
            receitaManipuladaEtiquetaDto);
    }

    private void AdicionarRegistroTecnico(Guid usuarioId, TipoRegistroTecnico tipoRegistro, string descricao, IEnumerable<string> produtosEnvolvidos = null)
    {
        var registroTecnico = new ReceitaManipuladaHistoricoTecnico(
            this,
            usuarioId,
            tipoRegistro,
            descricao,
            produtosEnvolvidos);

        HistoricoTecnico.Add(registroTecnico);
    }

    public void RegistrarIncompatibilidade(Guid usuarioId, NivelIncompatibilidade nivelIncompatibilidade, string descricaoIncompatibilidade, IEnumerable<string> produtosEnvolvidos)
    {
        if (nivelIncompatibilidade == NivelIncompatibilidade.Ok)
            return;

        var tipoRegistro = nivelIncompatibilidade switch
        {
            NivelIncompatibilidade.ApenasAviso => TipoRegistroTecnico.AvisoIncompatibilidade,
            NivelIncompatibilidade.LiberacaoSenha => TipoRegistroTecnico.IncompatibilidadeLiberadaComSenha,
            _ => TipoRegistroTecnico.AvisoIncompatibilidade
        };

        AdicionarRegistroTecnico(usuarioId, tipoRegistro, descricaoIncompatibilidade, produtosEnvolvidos);
    }

    public void RegistrarValidacaoFarmacotecnica(Guid usuarioId, string descricao)
    {
        AdicionarRegistroTecnico(usuarioId, TipoRegistroTecnico.ValidacaoFarmacotecnica, descricao);
    }

    public void RegistrarAlteracaoFormula(Guid usuarioId, string descricaoAlteracao)
    {
        AdicionarRegistroTecnico(usuarioId, TipoRegistroTecnico.AlteracaoFormula, descricaoAlteracao);
    }

    public void RegistrarObservacaoTecnica(Guid usuarioId, string observacao)
    {
        AdicionarRegistroTecnico(usuarioId, TipoRegistroTecnico.ObservacaoTecnica, observacao);
    }

    private bool TemLiberacaoParaIncompatibilidade(IEnumerable<string> produtosEnvolvidos)
    {
        if (produtosEnvolvidos == null || !produtosEnvolvidos.Any())
            return false;

        var produtosList = produtosEnvolvidos.ToList();

        return HistoricoTecnico.Any(h =>
            h.TipoRegistro == TipoRegistroTecnico.IncompatibilidadeLiberadaComSenha &&
            h.ProdutosEnvolvidos != null &&
            h.ProdutosEnvolvidos.Any(p => produtosList.Any(prod =>
                p.Contains(prod, StringComparison.OrdinalIgnoreCase))));
    }

    public bool TemLiberacaoParaIncompatibilidadePorDescricoes(IEnumerable<string> descricoesIncompatibilidades)
    {
        if (descricoesIncompatibilidades == null || !descricoesIncompatibilidades.Any())
            return false;

        return TemLiberacaoParaIncompatibilidade(descricoesIncompatibilidades);
    }

    #region Navigation Properties

    public virtual Cliente Paciente { get; set; }
    public virtual Prescritor Prescritor { get; set; }
    public virtual Posologia Posologia { get; set; }
    public virtual FormaFarmaceutica FormaFarmaceutica { get; set; }
    public virtual ReceitaManipuladaRotulo EdicaoEtiqueta { get; set; }
    public virtual ReceitaManipuladaBaseCalculo BaseCalculo { get; set; }
    public virtual ReceitaManipuladaValores Valores { get; set; }
    public virtual ModeloRotulo ModeloRotulo { get; set; }
    public virtual ModeloOrdemManipulacao ModeloOrdemManipulacao { get; set; }
    public virtual ICollection<ReceitaManipuladaItem> Itens { get; set; } = new List<ReceitaManipuladaItem>();

    public virtual ICollection<ReceitaManipuladaCalculo> CalculosItens { get; set; } =
        new List<ReceitaManipuladaCalculo>();

    public virtual ICollection<ReceitaManipuladaRastreioCalculo> RastreioCalculos { get; set; } =
        new List<ReceitaManipuladaRastreioCalculo>();

    public virtual ICollection<ReceitaManipuladaHistorico> Historico { get; set; } =
        new List<ReceitaManipuladaHistorico>();

    public virtual ICollection<ReceitaManipuladaHistoricoTecnico> HistoricoTecnico { get; set; } =
        new List<ReceitaManipuladaHistoricoTecnico>();

    #endregion
}