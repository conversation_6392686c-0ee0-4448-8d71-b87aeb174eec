using System.Runtime.Serialization;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;

public class ObterCalculoResponse
{
    public Guid Id { get; set; }
    public string DescricaoFormaFarmaceutica { get; set; }
    public decimal QuantidadeReceita { get; set; }
    public decimal? QuantidadeDose { get; set; }
    public string ApresentacaoFormaFarmaceutica { get; set; }
    public decimal QuantidadeRepetir { get; set; }
    public decimal VolumeCalculado { get; set; }
    public static int UnidadeMedidaVolumeId => UnidadeMedidaAbreviacao.mL.ToInt();
    public decimal QuantidadeEmbalagens { get; set; }
    public DateTime PrevisaoEntrega { get; set; }
    public IEnumerable<ObterComponentesCalculados> ComponentesCalculados { get; set; }
    public ObterValoresCalculados ValoresReceita { get; set; }
}

public class ObterComponentesCalculados
{
    public Guid ComponenteId { get; set; }
    public Guid ComponenteProdutoId { get; set; }
    public Guid? ProdutoOrigemId { get; set; }
    public string DescricaoProdutoEditada { get; set; }
    public string DescricaoProdutoSinonimo { get; set; }
    public string DescricaoProdutoOriginal { get; set; }
    public bool IsExcipiente { get; set; }
    public bool IsQsp { get; set; }
    public decimal QuantidadePrescrita { get; set; }
    public int Fator { get; set; }
    public int Densidade { get; set; }
    public int UnidadeMedidaPrescritaId { get; set; }
    public string UnidadeMedidaPrescritaAbreviacao { get; set; }
    public decimal QuantidadeCalculada { get; set; }
    public int UnidadeMedidaCalculadaId { get; set; }
    public string UnidadeMedidaCalculadaAbreviacao { get; set; }
    public decimal QuantidadeVolume { get; set; }
    public string PrecoCusto { get; set; }
    public decimal PercentualLucro { get; set; }
    public string PrecoVenda { get; set; }

    [IgnoreDataMember] public int ClasseProduto { get; set; }

    public TipoOrigem TipoOrigem { get; set; }
    public int? TamanhoCapsula { get; set; }
    public decimal? VolumeMaximoCapsula { get; set; }
    public decimal? CapacidadeMaximaEmbalagem { get; set; }
    public decimal? QuantidadeAssociada { get; set; }
    public bool? Acumula { get; set; }
    public decimal? DosagemMinima { get; set; }
    public decimal? DosagemMaxima { get; set; }
    public int? UnidadeMedidaDosagemId { get; set; }
    public bool DesmembraFormula { get; set; }
    public FormulaPadraoDesmembramento TipoDesmembramento { get; set; }
    public bool EhSinonimo { get; set; }
    public bool EhEditadoNoRotulo { get; set; }

    [IgnoreDataMember] public Guid? FormulaPadraoId { get; set; }

    public TipoComponenteReceita TipoComponente { get; set; }

    public bool IsAssociado => TipoOrigem == TipoOrigem.Associado;

    public List<ObterComponentesCalculados> ComponentesFormula { get; set; } = new();
}

public class ObterValoresCalculados
{
    public decimal ValorBruto { get; set; }
    public decimal DescontoTotal { get; set; }
    public decimal ValorReceitaTotal { get; set; }
}