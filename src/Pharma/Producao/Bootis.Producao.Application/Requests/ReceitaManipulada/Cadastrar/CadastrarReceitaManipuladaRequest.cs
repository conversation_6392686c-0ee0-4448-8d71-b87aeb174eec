using Bootis.Catalogo.Domain.Enumerations;
using MediatR;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Cadastrar;

public class CadastrarReceitaManipuladaRequest : IRequest<CadastrarReceitaManipuladaResponse>
{
    public Guid PedidoVendaId { get; set; }
    
    public Guid PacienteId { get; set; }
    public Guid FormaFarmaceuticaId { get; set; }
    public Guid? PrescritorId { get; set; }
    public DateOnly DataPrescricao { get; set; }
    public decimal QuantidadeReceita { get; set; }
    public int QuantidadeRepetir { get; set; }
    public decimal? QuantidadeDose { get; set; }
    public IEnumerable<CadastrarReceitaItemRequest> Componentes { get; set; }
}

public class CadastrarReceitaItemRequest
{
    public Guid ProdutoId { get; set; }
    public Guid? ProdutoSinonimoId { get; set; }
    public string DescricaoRotulo { get; set; }
    public decimal Quantidade { get; set; }
    public int UnidadeMedidaId { get; set; }
    public TipoComponente TipoQuantificacao { get; set; } = TipoComponente.Normal;
    public int Ordem { get; set; }
    public bool OcultaRotulo { get; set; }
}