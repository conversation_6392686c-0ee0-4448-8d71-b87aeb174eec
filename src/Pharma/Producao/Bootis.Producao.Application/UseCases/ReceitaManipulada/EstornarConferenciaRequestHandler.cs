using Bootis.Estoque.Domain.AggregatesModel.LivroRazaoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Estoque.Domain.Services;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Atualizar;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using MediatR;
using UUIDNext;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class EstornarConferenciaRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IReceitaManipuladaRepository receitaManipuladaRepository,
    ILivroRazaoEstoqueRepository livroRazaoEstoqueRepository,
    ISaldoEstoqueRepository saldoEstoqueRepository,
    IServicoLivroRazaoEstoque servicoLivroRazaoEstoque)
    : IRequestHandler<EstornarConferenciaRequest>
{
    public async Task Handle(EstornarConferenciaRequest request, CancellationToken cancellationToken)
    {
        var receita = await receitaManipuladaRepository.ObterReceitaManipuladaPorIdAsync(request.ReceitaManipuladaId);
        
        if (receita == null)
            throw new DomainException($"Receita manipulada com ID {request.ReceitaManipuladaId} não encontrada.");
        // Obter todas as movimentações de saída relacionadas à receita
        var movimentacoesSaida = await ObterMovimentacoesSaidaAsync(receita);

        // Verificar se já existem estornos para esta receita
        if (await JaExistemEstornosAsync(movimentacoesSaida, receita.Id))
        {
            throw new DomainException(
                "Não é possível estornar a conferência pois já existem movimentações de estorno relacionadas a esta receita.");
        }

        // Validar se todas as movimentações de saída estão completas
        ValidarMovimentacoesSaida(movimentacoesSaida);

        // Criar movimentações de estorno (entrada) para cada movimentação de saída
        await CriarMovimentacoesEstornoAsync(movimentacoesSaida, receita.Id);

        // Executar o estorno da receita
        receita.Estornar(userContext.UserId);

        receitaManipuladaRepository.Update(receita);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private async Task<List<LivroRazaoEstoque>> ObterMovimentacoesSaidaAsync(Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada receita)
    {
        var movimentacoesSaida = new List<LivroRazaoEstoque>();

        foreach (var rastreio in receita.RastreioCalculos)
        {
            if (rastreio.LoteId.HasValue)
            {
                var movimentacoesLote = await livroRazaoEstoqueRepository
                    .ObterMovimentacoesPorLoteAsync(rastreio.LoteId.Value);

                // Filtrar apenas movimentações de saída relacionadas à produção/consumo desta receita
                var movimentacoesSaidaLote = movimentacoesLote
                    .Where(m => m.EhSaida() &&
                               (m.TipoTransacao == TipoTransacaoEstoque.Producao ||
                                m.TipoTransacao == TipoTransacaoEstoque.Consumo) &&
                               m.ReceitaManipuladaId == receita.Id)
                    .ToList();

                movimentacoesSaida.AddRange(movimentacoesSaidaLote);
            }
        }

        return movimentacoesSaida;
    }

    private async Task<bool> JaExistemEstornosAsync(List<LivroRazaoEstoque> movimentacoesSaida, Guid receitaManipuladaId)
    {
        // Verificar se já existem movimentações de estorno para esta receita
        // Buscar por movimentações do tipo EstornoConferencia nos mesmos lotes/locais
        foreach (var movimentacaoSaida in movimentacoesSaida)
        {
            var movimentacoesLote = await livroRazaoEstoqueRepository
                .ObterMovimentacoesPorLoteAsync(movimentacaoSaida.LoteId);

            var jaTemEstorno = movimentacoesLote.Any(m =>
                m.TipoTransacao == TipoTransacaoEstoque.EstornoConferencia &&
                m.LocalEstoqueId == movimentacaoSaida.LocalEstoqueId &&
                m.ProdutoId == movimentacaoSaida.ProdutoId &&
                m.Metadados.ContainsKey("ReceitaManipuladaId") &&
                m.Metadados["ReceitaManipuladaId"].ToString() == receitaManipuladaId.ToString());

            if (jaTemEstorno)
                return true;
        }

        return false;
    }

    private static void ValidarMovimentacoesSaida(List<LivroRazaoEstoque> movimentacoesSaida)
    {
        // Verificar se todas as movimentações de saída estão completas
        var movimentacoesIncompletas = movimentacoesSaida
            .Where(m => m.Quantidade >= 0) // Saídas devem ter quantidade negativa
            .ToList();

        if (movimentacoesIncompletas.Any())
        {
            throw new DomainException(
                "Não é possível estornar a conferência pois existem movimentações de estoque incompletas relacionadas a esta receita.");
        }
    }

    private async Task CriarMovimentacoesEstornoAsync(List<LivroRazaoEstoque> movimentacoesSaida, Guid receitaManipuladaId)
    {
        foreach (var movimentacaoSaida in movimentacoesSaida)
        {
            // Obter o saldo atual do lote/local
            var saldoAtual = await saldoEstoqueRepository
                .ObterSaldoEstoquePorLoteLocalEstoqueAsync(movimentacaoSaida.LoteId, movimentacaoSaida.LocalEstoqueId);

            if (saldoAtual == null)
                throw new DomainException($"Saldo não encontrado para lote {movimentacaoSaida.LoteId} no local {movimentacaoSaida.LocalEstoqueId}.");

            // Criar movimentação de entrada (estorno)
            var transacaoId = Uuid.NewSequential();
            var quantidadeEstorno = Math.Abs(movimentacaoSaida.Quantidade); // Converter para positivo (entrada)

            var movimentacaoEstorno = new LivroRazaoEstoque(
                transacaoId,
                movimentacaoSaida.ProdutoId,
                movimentacaoSaida.LoteId,
                movimentacaoSaida.LocalEstoqueId,
                movimentacaoSaida.EmpresaId,
                quantidadeEstorno,
                TipoTransacaoEstoque.EstornoConferencia,
                $"Estorno de conferência farmacêutica - Receita {receitaManipuladaId}",
                userContext.UserId,
                "Producao.EstornoConferencia");

            // Adicionar metadados para rastreabilidade
            movimentacaoEstorno.Metadados["ReceitaManipuladaId"] = receitaManipuladaId.ToString();
            movimentacaoEstorno.Metadados["MovimentacaoOriginalId"] = movimentacaoSaida.Id.ToString();
            movimentacaoEstorno.Metadados["TipoEstorno"] = "ConferenciaFarmaceutica";

            movimentacaoEstorno.DefinirUnidadePadraoLote(saldoAtual.UnidadeMedidaId);

            // Atualizar o saldo do estoque
            var novoSaldo = saldoAtual.Saldo + quantidadeEstorno;
            saldoAtual.AtualizarSaldo(novoSaldo, transacaoId);

            // Adicionar as movimentações ao repositório
            livroRazaoEstoqueRepository.Add(movimentacaoEstorno);
            saldoEstoqueRepository.Update(saldoAtual);
        }
    }
}
