using Bootis.Estoque.Domain.AggregatesModel.LivroRazaoEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Atualizar;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class EstornarConferenciaRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IReceitaManipuladaRepository receitaManipuladaRepository,
    ILivroRazaoEstoqueRepository livroRazaoEstoqueRepository)
    : IRequestHandler<EstornarConferenciaRequest>
{
    public async Task Handle(EstornarConferenciaRequest request, CancellationToken cancellationToken)
    {
        var receita = await receitaManipuladaRepository.ObterReceitaManipuladaPorIdAsync(request.ReceitaManipuladaId);
        
        if (receita == null)
            throw new DomainException($"Receita manipulada com ID {request.ReceitaManipuladaId} não encontrada.");
        
        await ValidarMovimentacoesEstoqueAsync(receita);

        // Executar o estorno
        receita.Estornar(userContext.UserId);

        receitaManipuladaRepository.Update(receita);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private async Task ValidarMovimentacoesEstoqueAsync(Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada receita)
    {
        var movimentacoesReceita = new List<LivroRazaoEstoque>();

        foreach (var rastreio in receita.RastreioCalculos)
        {
            if (rastreio.LoteId.HasValue)
            {
                var movimentacoesLote = await livroRazaoEstoqueRepository
                    .ObterMovimentacoesPorLoteAsync(rastreio.LoteId.Value);
                
                var movimentacoesSaida = movimentacoesLote
                    .Where(m => m.EhSaida() && 
                               (m.TipoTransacao == TipoTransacaoEstoque.Producao || 
                                m.TipoTransacao == TipoTransacaoEstoque.Consumo))
                    .ToList();

                movimentacoesReceita.AddRange(movimentacoesSaida);
            }
        }
        
        var movimentacoesEstornadas = movimentacoesReceita
            .Where(m => m.TipoTransacao == TipoTransacaoEstoque.Devolucao)
            .ToList();

        if (movimentacoesEstornadas.Any())
        {
            throw new DomainException(
                "Não é possível estornar a conferência pois existem movimentações de estoque já estornadas relacionadas a esta receita.");
        }

        var movimentacoesIncompletas = movimentacoesReceita
            .Where(m => m.Quantidade == 0)
            .ToList();

        if (movimentacoesIncompletas.Any())
        {
            throw new DomainException(
                "Não é possível estornar a conferência pois existem movimentações de estoque incompletas relacionadas a esta receita.");
        }
    }
}
