using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate;
using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Resources;
using Bootis.Producao.Application.Requests.CertificadoAnalise.Cadastrar;
using Bootis.Producao.Domain.AggregatesModel.CertificadoAnaliseAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Producao.Application.UseCases.CertificadoAnalise;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork,
    ICertificadoAnaliseRepository certificadoAnaliseRepository,
    IProdutoFichaTecnicaRepository produtoFichaTecnicaRepository,
    ILoteRepository loteRepository) 
    : IRequestHandler<CadastrarRequest>
{
    public async Task Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var lote = await loteRepository.ObterLoteAsync(request.LoteId);
        var produtoFichaTecnica = await produtoFichaTecnicaRepository.ObterProdutoFichaTecnicaPorProdutoIdAsync(lote.ProdutoId);

        if(lote.Situacao != Estoque.Domain.Enumerations.SituacaoLote.ControleQualidade)
        {
            throw new ValidationException(
                Localizer.Instance.GetMessage_Lote_SituacaoDivergenteControleQualidade(lote.Id));
        }

        var certificadoAnalise = new Domain.AggregatesModel.CertificadoAnaliseAggregate.CertificadoAnalise(lote.Id,
            request.QuantidadeAmostragem,
            request.UnidadeAmostragemId,
            request.InformacaoesComplementares,
            request.Aprovado);

        certificadoAnalise.AdicionarControleQualidade(request.ControleQualidade, produtoFichaTecnica.Especificacoes
            .Where(c => c.MostrarCertificadoAnalise is true)
            .Select(c => c.EnsaioControleQualidadeId));

        certificadoAnaliseRepository.Add(certificadoAnalise);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}
