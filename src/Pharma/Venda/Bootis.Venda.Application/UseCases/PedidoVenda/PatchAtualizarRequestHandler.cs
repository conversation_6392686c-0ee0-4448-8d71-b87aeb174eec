using Bootis.Venda.Application.Extensions;
using Bootis.Venda.Application.Requests.PedidoVenda.Atualizar;
using Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;
using MediatR;

namespace Bootis.Venda.Application.UseCases.PedidoVenda;

public class PatchAtualizarRequestHandler(
    IPedidoVendaRepository pedidoVendaRepository,
    IMediator mediator)
    : IRequestHandler<PatchAtualizarRequest>
{
    public async Task Handle(PatchAtualizarRequest request, CancellationToken cancellationToken)
    {
        var pedidoVenda = await pedidoVendaRepository.ObterPedidoVendaAsync(request.Id);

        if (pedidoVenda == null)
            return;

        var command = new AtualizarRequest
        {
            PedidoVendaId = pedidoVenda.Id,
            TipoDesconto = pedidoVenda.TipoDesconto,
            Desconto = pedidoVenda.Desconto,
            ValorTotal = null,
            Taxa = pedidoVenda.Entrega is PedidoVendaEntrega entregaDomicilio ? entregaDomicilio.Taxa : 0
        };

        const string observacaoPath = Constants.ObservacaoPath;

        var observacaoPatch = request.PatchDocument.Operations.FirstOrDefault(op => op.path == observacaoPath);

        if (observacaoPatch?.value != null) command.Observacao = observacaoPatch.value.ToString();
        request.PatchDocument.ApplyTo(command);

        if (!(pedidoVenda.Entrega is PedidoVendaEntrega) && command.Taxa != 0)
            return;

        await mediator.Send(command, cancellationToken);
    }

    private static class Constants
    {
        public const string ObservacaoPath = "/Observacao";
    }
}