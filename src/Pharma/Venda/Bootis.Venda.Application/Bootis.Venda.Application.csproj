<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{7f264eb4-72b4-4ffc-b450-6dc672b4cc26}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MediatR" Version="13.0.0"/>
        <PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="9.0.9"/>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Estoque\Bootis.Estoque.Application\Bootis.Estoque.Application.csproj"/>
        <ProjectReference Include="..\..\Organizacional\Bootis.Organizacional.Application\Bootis.Organizacional.Application.csproj"/>
        <ProjectReference Include="..\..\Pessoa\Bootis.Pessoa.Application\Bootis.Pessoa.Application.csproj"/>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Application\Bootis.Shared.Application.csproj"/>
        <ProjectReference Include="..\Bootis.Venda.Domain\Bootis.Venda.Domain.csproj"/>
    </ItemGroup>


</Project>

