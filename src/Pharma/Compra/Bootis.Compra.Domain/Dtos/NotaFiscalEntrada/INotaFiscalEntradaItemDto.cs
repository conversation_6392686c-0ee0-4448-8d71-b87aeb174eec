using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Compra.Domain.Dtos.NotaFiscalEntrada;

public class NotaFiscalEntradaItemDto
{
    public Guid? NotaFiscalEntradaItemId { get; set; }
    public Guid ProdutoId { get; set; }
    public Guid NcmId { get; set; }
    public Guid CfopId { get; set; }
    public Guid CstCsosnId { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public decimal QuantidadeComprada { get; set; }
    public decimal ValorUnitario { get; set; }
    public decimal BaseCalculoIcms { get; set; }
    public decimal ValorIcms { get; set; }
    public decimal AliquotaIpi { get; set; }
    public decimal ValorIpi { get; set; }
}