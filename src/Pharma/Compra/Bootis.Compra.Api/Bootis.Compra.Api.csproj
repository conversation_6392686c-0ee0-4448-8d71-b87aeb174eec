<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <AssemblyName>Bootis.Compra.Api</AssemblyName>
        <RootNamespace>Bootis.Compra.Api</RootNamespace>
        <LangVersion>default</LangVersion>
        <ProjectGuid>49f1aa9f-7da3-418b-9a6c-2e4b11ae2755</ProjectGuid>
    </PropertyGroup>


    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.9">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Api\Bootis.Shared.Api.csproj"/>
        <ProjectReference Include="..\Bootis.Compra.Infrastructure\Bootis.Compra.Infrastructure.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Content Remove="appsettings*.json"/>
    </ItemGroup>

    <ItemGroup Condition="'$(IsPublishable)' == 'true' AND '$(MSBuildProjectName)' == 'Bootis.Pharma.Api'">
        <None Include="appsettings*.json">
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </None>
    </ItemGroup>
</Project>
