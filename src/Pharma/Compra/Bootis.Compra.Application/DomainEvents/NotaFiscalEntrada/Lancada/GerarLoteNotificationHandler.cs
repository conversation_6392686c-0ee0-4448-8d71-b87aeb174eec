using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoMateriaPrima;
using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;
using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.Events;
using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Interfaces;
using Bootis.Estoque.Application.Mappers;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.ProdutoLoteEmUsoAggregate;
using Bootis.Estoque.Domain.Dtos.Lote;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Events;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Compra.Application.DomainEvents.NotaFiscalEntrada.Lancada;

public class GerarLoteNotificationHandler(
    IUserContext userContext,
    IUnitOfWork unitOfWork,
    IServicoEstoque servicoEstoque)
    : INotificationHandler<DomainEventNotification<NotaFiscalEntradaLancadaEvent>>
{
    private readonly ILoteRepository _loteRepository = unitOfWork.GetRepository<ILoteRepository>();

    private readonly IProdutoLoteEmUsoRepository _produtoLoteEmUsoRepository =
        unitOfWork.GetRepository<IProdutoLoteEmUsoRepository>();

    private readonly IProdutoRepository _produtoRepository = unitOfWork.GetRepository<IProdutoRepository>();
    private readonly IUsuarioRepository _usuarioRepository = unitOfWork.GetRepository<IUsuarioRepository>();

    public async Task Handle(DomainEventNotification<NotaFiscalEntradaLancadaEvent> notification,
        CancellationToken cancellationToken)
    {
        var notaFiscalEntrada = notification.DomainEvent.NotaFiscalEntrada;
        var usuario = await _usuarioRepository.ObterUsuarioLogado();

        foreach (var notaFiscalEntradaItem in notaFiscalEntrada.NotaFiscalEntradaItem)
        {
            var situacao = notaFiscalEntradaItem.Produto.ControlaQualidade
                ? SituacaoLote.ControleQualidade
                : SituacaoLote.Liberado;

            foreach (var notaFiscalEntradaLote in notaFiscalEntradaItem.NotaFiscalEntradaLote)
            {
                await _loteRepository.ValidarLotePorNumeroAsync(notaFiscalEntradaLote.NumeroLote,
                    notaFiscalEntradaItem.Produto.Id, notaFiscalEntrada.Numero, notaFiscalEntrada.Serie);

                var lote = await CriarLote(notaFiscalEntradaLote, notaFiscalEntradaItem, notaFiscalEntrada, usuario.Id,
                    situacao);

                if (notaFiscalEntradaItem.Produto.ClasseProdutoId ==
                    TipoClasseProdutoAbreviacao.MateriaPrima)
                {
                    var loteInformacaoTecnica = CriarLoteInformacaoTecnica(notaFiscalEntradaLote, lote.Id);
                    await AtualizarInformacaoTecnicaProdutoMateriaPrima(notaFiscalEntradaItem, loteInformacaoTecnica);
                }

                await RegistrarEntradaEstoque(
                    notaFiscalEntradaItem,
                    lote,
                    notaFiscalEntradaLote,
                    notaFiscalEntrada,
                    usuario);

                var localEstoqueId = notaFiscalEntradaLote.LocalEstoqueId!.Value;
                await AdicionarLoteEmUso(notaFiscalEntradaItem, lote, localEstoqueId);
            }
        }
    }

    private async Task RegistrarEntradaEstoque(
        NotaFiscalEntradaItem notaFiscalEntradaItem,
        Lote lote,
        NotaFiscalEntradaLote notaFiscalEntradaLote,
        Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada notaFiscalEntrada,
        Usuario usuario)
    {
        await servicoEstoque.RegistrarMovimentoCompraAsync(
            notaFiscalEntradaItem.Produto.Id,
            lote.Id,
            notaFiscalEntradaLote.LocalEstoqueId!.Value,
            usuario.EmpresaId,
            notaFiscalEntradaLote.Quantidade!.Value,
            notaFiscalEntradaItem.UnidadeId!.Value,
            userContext.UserId,
            notaFiscalEntrada.Numero.ToString(),
            notaFiscalEntrada.Serie.ToString(),
            notaFiscalEntrada.Id);
    }

    private async Task<Lote> CriarLote(NotaFiscalEntradaLote notaFiscalEntradaLote,
        NotaFiscalEntradaItem notaFiscalEntradaItem,
        Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada notaFiscalEntrada, Guid usuarioId,
        SituacaoLote situacao)
    {
        var lote = new Lote(
            notaFiscalEntradaLote.NumeroLote,
            notaFiscalEntradaItem.Produto,
            notaFiscalEntrada.Fornecedor,
            notaFiscalEntradaLote.DataFabricacao!.Value,
            notaFiscalEntradaLote.DataValidade!.Value,
            notaFiscalEntrada.Numero,
            notaFiscalEntrada.Serie,
            usuarioId,
            situacao);

        lote.ValidarDataValidade();
        _loteRepository.Add(lote);
        await unitOfWork.SaveChangesAsync();

        return lote;
    }

    private LoteInformacaoTecnica CriarLoteInformacaoTecnica(NotaFiscalEntradaLote notaFiscalEntradaLote, Guid loteId)
    {
        var loteInformacaoTecnicaDto = new LoteInformacaoTecnicaDto
        {
            DiluicaoFornecedor = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.DiluicaoFornecedor
                .GetValueOrDefault(),
            FatorDiluicaoFornecedor = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica
                .FatorDiluicaoFornecedor.GetValueOrDefault(),
            ConcentracaoAgua = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.ConcentracaoAgua
                .GetValueOrDefault(),
            FatorConcentracaoAgua = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.FatorConcentracaoAgua
                .GetValueOrDefault(),
            Densidade = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.Densidade.GetValueOrDefault()
        };

        var informacaoTecnica = new LoteInformacaoTecnica(loteId,
            notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.PaisOrigemId,
            null,
            loteInformacaoTecnicaDto);

        if (notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.NotaFiscalEntradaLoteUnidadeAlternativa.Count >
            0)
        {
            var dto = new LoteUnidadeAlternativaDto
            {
                UnidadeAlternativaId = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica
                    .NotaFiscalEntradaLoteUnidadeAlternativa.First().UnidadeAlternativaId.GetValueOrDefault(),
                UnidadeAlternativaConversaoId = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica
                    .NotaFiscalEntradaLoteUnidadeAlternativa.First().UnidadeAlternativaConversaoId.GetValueOrDefault(),
                QuantidadeUnidadeAlternativa = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica
                    .NotaFiscalEntradaLoteUnidadeAlternativa.First().QuantidadeUnidadeAlternativa.GetValueOrDefault()
            };

            var unidadeAlternativa1 = LoteUnidadeAlternativaMapper.LoteUnidadeAlternativaFrom(dto);
            informacaoTecnica.AdicionarUnidadeAlternativa(unidadeAlternativa1);
        }

        if (notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.NotaFiscalEntradaLoteUnidadeAlternativa.Count >
            1)
        {
            var dto = new LoteUnidadeAlternativaDto
            {
                UnidadeAlternativaId = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica
                    .NotaFiscalEntradaLoteUnidadeAlternativa.Last().UnidadeAlternativaId.GetValueOrDefault(),
                UnidadeAlternativaConversaoId = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica
                    .NotaFiscalEntradaLoteUnidadeAlternativa.Last().UnidadeAlternativaConversaoId.GetValueOrDefault(),
                QuantidadeUnidadeAlternativa = notaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica
                    .NotaFiscalEntradaLoteUnidadeAlternativa.Last().QuantidadeUnidadeAlternativa.GetValueOrDefault()
            };

            var unidadeAlternativa2 = LoteUnidadeAlternativaMapper.LoteUnidadeAlternativaFrom(dto);
            informacaoTecnica.AdicionarUnidadeAlternativa(unidadeAlternativa2);
        }

        _loteRepository.Add(informacaoTecnica);

        return informacaoTecnica;
    }

    private async Task AtualizarInformacaoTecnicaProdutoMateriaPrima(NotaFiscalEntradaItem notaFiscalEntradaItem,
        LoteInformacaoTecnica loteInformacaoTecnica)
    {
        if (loteInformacaoTecnica == null)
            return;

        var produto = await _produtoRepository.ObterProdutoPorIdAsync(notaFiscalEntradaItem.Produto.Id);
        var informacaoTecnicaDto = CriarInformacaoTecnicaDto(loteInformacaoTecnica);

        produto.ProdutoMateriaPrima.AtualizarInformacoesTecnicas(informacaoTecnicaDto);
    }

    private static ProdutoMateriaPrimaInformacaoTecnicaDto CriarInformacaoTecnicaDto(
        LoteInformacaoTecnica loteInformacaoTecnica)
    {
        return new ProdutoMateriaPrimaInformacaoTecnicaDto
        {
            DiluicaoFornecedor = loteInformacaoTecnica.DiluicaoFornecedor,
            FatorDiluicaoFornecedor = loteInformacaoTecnica.FatorDiluicaoFornecedor,
            ConcentracaoAgua = loteInformacaoTecnica.ConcentracaoAgua,
            FatorConcentracaoAgua = loteInformacaoTecnica.FatorConcentracaoAgua,
            DiluicaoInterna = loteInformacaoTecnica.DiluicaoInterna,
            FatorDiluicaoInterna = loteInformacaoTecnica.FatorDiluicaoInterna,
            Densidade = loteInformacaoTecnica.Densidade,
            UnidadeAlternativaId1 =
                loteInformacaoTecnica.LotesUnidadeAlternativa?.FirstOrDefault()?.UnidadeAlternativaId,
            UnidadeAlternativaConversaoId1 = loteInformacaoTecnica.LotesUnidadeAlternativa?.FirstOrDefault()
                ?.UnidadeAlternativaConversaoId,
            QuantidadeUnidadeAlternativa1 = loteInformacaoTecnica.LotesUnidadeAlternativa?.FirstOrDefault()
                ?.QuantidadeUnidadeAlternativa,
            UnidadeAlternativaId2 = loteInformacaoTecnica.LotesUnidadeAlternativa?.ElementAtOrDefault(1)
                ?.UnidadeAlternativaId,
            UnidadeAlternativaConversaoId2 = loteInformacaoTecnica.LotesUnidadeAlternativa?.ElementAtOrDefault(1)
                ?.UnidadeAlternativaConversaoId,
            QuantidadeUnidadeAlternativa2 = loteInformacaoTecnica.LotesUnidadeAlternativa?.ElementAtOrDefault(1)
                ?.QuantidadeUnidadeAlternativa
        };
    }

    private async Task AdicionarLoteEmUso(NotaFiscalEntradaItem notaFiscalEntradaItem, Lote lote, Guid localEstoqueId)
    {
        if (await _produtoLoteEmUsoRepository.VerificarLoteEmUsoAsync(notaFiscalEntradaItem.Produto.Id)) return;

        var loteEmUso = new ProdutoLoteEmUso(notaFiscalEntradaItem.Produto.Id, lote, localEstoqueId);

        _produtoLoteEmUsoRepository.Add(loteEmUso);
    }
}