using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;
using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate.Events;
using Bootis.Estoque.Application.Interfaces;
using Bootis.Estoque.Resources;
using Bootis.Shared.Application.Events;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida;
using MediatR;

namespace Bootis.Compra.Application.DomainEvents.NotaFiscalEntrada.Lancada;

public class
    AtualizarValorCustoNotificationHandler(
        IServicoEstoque servicoEstoque,
        IProdutoRepository produtoRepository)
    : INotificationHandler<DomainEventNotification<NotaFiscalEntradaLancadaEvent>>
{
    public async Task Handle(DomainEventNotification<NotaFiscalEntradaLancadaEvent> notaFiscalEntradaLancada,
        CancellationToken cancellationToken)
    {
        var notaFiscalEntrada = notaFiscalEntradaLancada.DomainEvent.NotaFiscalEntrada;

        foreach (var notaFiscalEntradaItem in notaFiscalEntrada.NotaFiscalEntradaItem)
        {
            var somaSaldos = await ObterSaldoProduto(notaFiscalEntradaItem);
            var conversaoUnidadeMedidaEntrada =
                ConversaoUnidadeMedidaCreator.Criar(notaFiscalEntradaItem.UnidadeId!.Value,
                    notaFiscalEntradaItem.Produto.UnidadeEstoqueId);

            var quantideEntrada =
                conversaoUnidadeMedidaEntrada.CalcularConversao(notaFiscalEntradaItem.QuantidadeComprada!.Value);

            var valorEntrada =
                conversaoUnidadeMedidaEntrada.CalcularConversaoMonetario(notaFiscalEntradaItem.ValorUnitario!.Value);

            if (somaSaldos + quantideEntrada == 0)
                throw new ValidationException(
                    Localizer.Instance.GetMessage_SaldoInformado_Invalido());

            var custoMedio =
                (quantideEntrada * valorEntrada + somaSaldos * notaFiscalEntradaItem.Produto.ValorCusto) / (somaSaldos +
                    quantideEntrada);

            notaFiscalEntradaItem.Produto.AlterarValorCusto(custoMedio);

            produtoRepository.Update(notaFiscalEntradaItem.Produto);
        }
    }

    private async Task<decimal> ObterSaldoProduto(NotaFiscalEntradaItem notaFiscalEntradaItem)
    {
        return await servicoEstoque.ObterSaldoTotalProdutoAsync(notaFiscalEntradaItem.Produto.Id);
    }
}