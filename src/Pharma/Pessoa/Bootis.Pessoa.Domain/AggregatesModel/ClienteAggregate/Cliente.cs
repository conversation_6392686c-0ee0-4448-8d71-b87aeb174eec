using Bootis.Pessoa.Domain.Enumerations;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;

public class Cliente : Entity, IAggregateRoot, ITenant
{
    public Cliente()
    {
    }

    public Cliente(string nome,
        TipoPessoa pessoa,
        string cpf) : this()
    {
        Nome = nome;
        Pessoa = pessoa;
        Cpf = cpf;
        Ativo = true;
    }

    public Cliente(string nome,
        TipoPessoa pessoa,
        string cnpj,
        string razaoSocial) : this()
    {
        Nome = nome;
        Pessoa = pessoa;
        Cnpj = cnpj;
        RazaoSocial = razaoSocial;
        Ativo = true;
    }

    public Cliente(string nome,
        TipoPessoa pessoa,
        string cpf,
        DateOnly? dataNascimento,
        string observacao,
        decimal? descontoProdutosAcabados,
        decimal? descontoFormulas,
        string linkImagem) : this()
    {
        Nome = nome;
        Pessoa = pessoa;
        Cpf = cpf;
        DataNascimento = dataNascimento;
        Observacao = observacao;
        DescontoProdutosAcabados = descontoProdutosAcabados;
        DescontoFormulas = descontoFormulas;
        LinkImagem = linkImagem;
        Ativo = true;
    }

    public Cliente(string nome,
        TipoPessoa pessoa,
        string documento,
        string razaoSocial,
        DateOnly? dataNascimento,
        string observacao,
        decimal? descontoProdutosAcabados,
        decimal? descontoFormulas) : this()
    {
        Nome = nome;
        Pessoa = pessoa;
        Cpf = pessoa == TipoPessoa.Fisica ? documento : null;
        Cnpj = pessoa == TipoPessoa.Juridica ? documento : null;
        RazaoSocial = razaoSocial;
        DataNascimento = dataNascimento;
        Observacao = observacao;
        DescontoProdutosAcabados = descontoProdutosAcabados;
        DescontoFormulas = descontoFormulas;
        Ativo = true;
    }

    public string Nome { get; private set; }
    public TipoPessoa Pessoa { get; private set; }
    public string Cpf { get; private set; }
    public string Cnpj { get; private set; }
    public string LinkImagem { get; private set; }
    public string RazaoSocial { get; private set; }
    public string Observacao { get; private set; }
    public decimal? DescontoProdutosAcabados { get; private set; }
    public decimal? DescontoFormulas { get; private set; }
    public bool Ativo { get; private set; }
    public int SequenciaGroupTenant { get; private set; }
    public DateOnly? DataNascimento { get; private set; }

    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void Atualizar(string nome, DateOnly? dataNascimento,
        decimal? descontoProdutosAcabados, decimal? descontoFormulas, string observacao,
        TipoPessoa pessoa, string cpf, string cnpj, string razaoSocial, string linkImagem)
    {
        Nome = nome;
        DataNascimento = dataNascimento;
        DescontoProdutosAcabados = descontoProdutosAcabados;
        DescontoFormulas = descontoFormulas;
        Observacao = observacao;
        LinkImagem = linkImagem;

        switch (pessoa)
        {
            case TipoPessoa.Fisica:
                Pessoa = TipoPessoa.Fisica;
                Cpf = cpf;
                Cnpj = null;
                RazaoSocial = null;
                break;
            case TipoPessoa.Juridica:
                Pessoa = TipoPessoa.Juridica;
                RazaoSocial = razaoSocial;
                Cnpj = cnpj;
                Cpf = null;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(pessoa), pessoa, null);
        }
    }

    public void AtualizarAtivo(bool ativo)
    {
        Ativo = ativo;
    }

    public void AdicionarContato(ClienteContato contato)
    {
        ValidarContatoPrincipal(contato.Principal);
        Contatos.Add(contato);
    }

    public void AtualizarContato(ClienteContato contato)
    {
        var contatoExistente = Contatos.FirstOrDefault(x => x.Id == contato.Id);

        if (contatoExistente != null) Contatos.Remove(contato);

        AdicionarContato(contato);
    }

    public void AdicionarEndereco(ClienteEndereco endereco)
    {
        ValidarEnderecoPrincipal(endereco.Principal);
        Enderecos.Add(endereco);
    }

    public void AtualizarEndereco(ClienteEndereco endereco)
    {
        var enderecoExistente = Enderecos.FirstOrDefault(x => x.Id == endereco.Id);

        if (enderecoExistente != null)
            Enderecos.Remove(endereco);

        AdicionarEndereco(endereco);
    }

    public void AdicionarDocumento(ClienteDocumento documento)
    {
        Documentos.Add(documento);
    }

    private void ValidarContatoPrincipal(bool ehContatoPrincipal)
    {
        if (!ehContatoPrincipal || !Contatos.Any(x => x.Principal)) return;

        foreach (var contato in Contatos.Where(x => x.Principal)) contato.AtualizarPrincipal(false);
    }

    private void ValidarEnderecoPrincipal(bool ehEnderecoPrincipal)
    {
        if (!ehEnderecoPrincipal || !Enderecos.Any(x => x.Principal)) return;

        foreach (var endereco in Enderecos.Where(x => x.Principal)) endereco.AtualizarPrincipal(false);
    }

    #region Navigation Properties

    public virtual ICollection<ClienteEndereco> Enderecos { get; set; } = new List<ClienteEndereco>();
    public virtual ICollection<ClienteContato> Contatos { get; set; } = new List<ClienteContato>();
    public virtual ICollection<ClienteDocumento> Documentos { get; set; } = new List<ClienteDocumento>();

    #endregion
}