using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.BibliografiaAggregate;

public class Bibliografia() : Entity, IAggregateRoot, ITenant
{
    public Bibliografia(string descricao) : this()
    {
        Descricao = descricao;
        Ativo = true;
    }

    public string Descricao { get; private set; }
    public bool Ativo { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }
}

