using Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemAssociacao;
using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoEmbalagem;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;

public class ProdutoEmbalagem()
{
    public ProdutoEmbalagem(Produto produto,
        Guid? embalagemClassificacaoId,
        decimal? volume) : this()
    {
        Produto = produto;
        EmbalagemClassificacaoId = embalagemClassificacaoId;
        Volume = volume;
    }

    public Guid ProdutoId { get; private set; }
    public Guid? EmbalagemClassificacaoId { get; private set; }
    public decimal? Volume { get; private set; }

    public void AtualizarProdutoEmbalagem(decimal? volume, EmbalagemClassificacao embalagemClassificacao,
        IEnumerable<ProdutoEmbalagemAssociacaoDto> produtoEmbalagemAssociacaoDtos,
        IEnumerable<ProdutoEmbalagem> produtosEmbalagem,
        IEnumerable<ProdutoEmbalagemCapsulaTamanhoAssociacaoDto> produtoEmbalagemCapsulaTamanhoAssociacaoDtos,
        IEnumerable<ModeloRotuloEmbalagemAssociacaoDto> modeloRotuloEmbalagemAssociacaoDto,
        IEnumerable<CapsulaTamanho> capsulasTamanho,
        List<FormaFarmaceutica> formasFarmaceuticas,
        List<ModeloRotulo> modelosRotulo)
    {
        Volume = volume;
        EmbalagemClassificacaoId = embalagemClassificacao?.Id;

        AtualizarAssociacoesProdutoEmbalagem(produtoEmbalagemAssociacaoDtos, produtosEmbalagem);
        AtualizarAssociacoesCapsulaTamanho(produtoEmbalagemCapsulaTamanhoAssociacaoDtos, capsulasTamanho);
        AtualizarAssociacoesModeloRotulo(modeloRotuloEmbalagemAssociacaoDto, formasFarmaceuticas, modelosRotulo);
    }

    public void RemoverClassificaçãoEmbalagem()
    {
        EmbalagemClassificacao = null;
    }

    #region Navigation properties

    public virtual Produto Produto { get; set; }
    public virtual EmbalagemClassificacao EmbalagemClassificacao { get; set; }

    public virtual ICollection<EmbalagemCapsulaTamanhoAssociacao> EmbalagemCapsulaTamanhoAssociacao { get; set; } =
        new List<EmbalagemCapsulaTamanhoAssociacao>();

    public virtual ICollection<EmbalagemAssociacao.EmbalagemAssociacao> EmbalagemAssociacao { get; set; } =
        new List<EmbalagemAssociacao.EmbalagemAssociacao>();

    public virtual ICollection<ModeloRotuloEmbalagemAssociacao> ModeloRotuloEmbalagemAssociacao { get; set; } =
        new List<ModeloRotuloEmbalagemAssociacao>();

    #endregion

    #region Operações CRUD CapsulaProntaAssociacao

    public void CadastrarAssociacoesCapsulaTamanho(
        IEnumerable<ProdutoEmbalagemCapsulaTamanhoAssociacaoDto> produtoEmbalagemCapsulaTamanhoAssociacaoDtos,
        IEnumerable<CapsulaTamanho> capsulasTamanho)
    {
        foreach (var produtoEmbalagemCapsulaTamanhoAssociacaoDto in produtoEmbalagemCapsulaTamanhoAssociacaoDtos)
        {
            if (produtoEmbalagemCapsulaTamanhoAssociacaoDto.NumeroCapsulaId == null) continue;

            var capsulaTamanho = capsulasTamanho
                .Single(c => c.Id == produtoEmbalagemCapsulaTamanhoAssociacaoDto.NumeroCapsulaId);

            var capsulaTamanhoAssociacao = new EmbalagemCapsulaTamanhoAssociacao(this,
                capsulaTamanho.Id,
                produtoEmbalagemCapsulaTamanhoAssociacaoDto.QuantidadeCapsula.GetValueOrDefault());

            AdicionarAssociacaoCapsulaTamanho(capsulaTamanhoAssociacao);
        }
    }

    private void AtualizarAssociacoesCapsulaTamanho(
        IEnumerable<ProdutoEmbalagemCapsulaTamanhoAssociacaoDto> produtoEmbalagemCapsulaTamanhoAssociacaoDtos,
        IEnumerable<CapsulaTamanho> capsulasTamanho)
    {
        var capsulasTamanhoAssociacao = EmbalagemCapsulaTamanhoAssociacao.ToList();

        if (produtoEmbalagemCapsulaTamanhoAssociacaoDtos is not null)
            foreach (var produtoEmbalagemCapsulaTamanhoAssociacaoDto in produtoEmbalagemCapsulaTamanhoAssociacaoDtos)
            {
                var capsulaTamanho = capsulasTamanho
                    .Single(c => c.Id == produtoEmbalagemCapsulaTamanhoAssociacaoDto.NumeroCapsulaId.GetValueOrDefault());

                var capsulaTamanhoAssociacao = EmbalagemCapsulaTamanhoAssociacao
                    .SingleOrDefault(c => c.CapsulaTamanhoId == capsulaTamanho.Id);

                if (capsulaTamanhoAssociacao is not null)
                {
                    capsulaTamanhoAssociacao.AtualizarCapsulaTamanhoAssociacao(
                        produtoEmbalagemCapsulaTamanhoAssociacaoDto.QuantidadeCapsula.GetValueOrDefault());
                    capsulasTamanhoAssociacao.Remove(capsulaTamanhoAssociacao);
                }
                else
                {
                    capsulaTamanhoAssociacao = new EmbalagemCapsulaTamanhoAssociacao(this,
                        capsulaTamanho.Id,
                        produtoEmbalagemCapsulaTamanhoAssociacaoDto.QuantidadeCapsula.GetValueOrDefault());

                    AdicionarAssociacaoCapsulaTamanho(capsulaTamanhoAssociacao);
                }
            }

        foreach (var capsulaTamanhoAssociacao in capsulasTamanhoAssociacao)
            RemoverAssociacoesCapsulaTamanho(capsulaTamanhoAssociacao);
    }

    private void AdicionarAssociacaoCapsulaTamanho(EmbalagemCapsulaTamanhoAssociacao capsulaTamanhoAssociacao)
    {
        EmbalagemCapsulaTamanhoAssociacao.Add(capsulaTamanhoAssociacao);
    }

    private void RemoverAssociacoesCapsulaTamanho(EmbalagemCapsulaTamanhoAssociacao capsulaTamanhoAssociacao)
    {
        capsulaTamanhoAssociacao.Remove();
        EmbalagemCapsulaTamanhoAssociacao.Remove(capsulaTamanhoAssociacao);
    }

    #endregion
    
    #region Operações CRUD EmbalagemAssociacao

    public void CadastrarAssociacacoesProdutoEmbalagem(
        IEnumerable<ProdutoEmbalagemAssociacaoDto> produtoEmbalagemAssociacaoDtos,
        IEnumerable<ProdutoEmbalagem> produtosEmbalagem)
    {
        foreach (var produtoEmbalagemAssociacaoDto in produtoEmbalagemAssociacaoDtos)
        {
            if (produtoEmbalagemAssociacaoDto.ProdutoEmbalagemId == null) continue;

            var produtoEmbalagemAssociacao = produtosEmbalagem
                .Single(c =>
                    c.Produto.Id ==
                    produtoEmbalagemAssociacaoDto.ProdutoEmbalagemId.GetValueOrDefault());

            var embalagemAssociacao = new EmbalagemAssociacao.EmbalagemAssociacao(this,
                produtoEmbalagemAssociacao,
                produtoEmbalagemAssociacaoDto.QuantidadeEmbalagem.GetValueOrDefault());

            AdicionarAssociacaoProdutoEmbalagem(embalagemAssociacao);
        }
    }

    private void AtualizarAssociacoesProdutoEmbalagem(
        IEnumerable<ProdutoEmbalagemAssociacaoDto> produtoEmbalagemAssociacaoDtos,
        IEnumerable<ProdutoEmbalagem> produtosEmbalagem)
    {
        var embalagensAssociacao = EmbalagemAssociacao.ToList();

        if (produtoEmbalagemAssociacaoDtos is not null)
            foreach (var produtoEmbalagemAssociacaoDto in produtoEmbalagemAssociacaoDtos)
            {
                var produtoEmbalagemAssociada = produtosEmbalagem
                    .Single(c =>
                        c.Produto.Id ==
                        produtoEmbalagemAssociacaoDto.ProdutoEmbalagemId.GetValueOrDefault());

                var embalagemAssociacao = EmbalagemAssociacao
                    .SingleOrDefault(c => c.ProdutoEmbalagemAssociada == produtoEmbalagemAssociada);

                if (embalagemAssociacao is not null)
                {
                    embalagemAssociacao.AtualizarEmbalagemAssociacao(produtoEmbalagemAssociacaoDto.QuantidadeEmbalagem
                        .GetValueOrDefault());
                    embalagensAssociacao.Remove(embalagemAssociacao);
                }
                else
                {
                    embalagemAssociacao = new EmbalagemAssociacao.EmbalagemAssociacao(this,
                        produtoEmbalagemAssociada,
                        produtoEmbalagemAssociacaoDto.QuantidadeEmbalagem.GetValueOrDefault());

                    AdicionarAssociacaoProdutoEmbalagem(embalagemAssociacao);
                }
            }

        foreach (var embalagemAssociacao in embalagensAssociacao)
            RemoverAssociacoesProdutoEmbalagem(embalagemAssociacao);
    }


    private void AdicionarAssociacaoProdutoEmbalagem(EmbalagemAssociacao.EmbalagemAssociacao produtoEmbalagemAssociacao)
    {
        EmbalagemAssociacao.Add(produtoEmbalagemAssociacao);
    }

    private void RemoverAssociacoesProdutoEmbalagem(EmbalagemAssociacao.EmbalagemAssociacao produtoEmbalagemAssociacao)
    {
        produtoEmbalagemAssociacao.Remove();
        EmbalagemAssociacao.Remove(produtoEmbalagemAssociacao);
    }

    #endregion
    
    #region Operações CRUD ModeloRotuloAssociacao

    public void CadastrarAssociacoesModeloRotulo(
       IEnumerable<ModeloRotuloEmbalagemAssociacaoDto> modeloRotuloEmbalagemAssociacaoDtos,
       List<FormaFarmaceutica> formasFarmaceuticas,
       List<ModeloRotulo> modelosRotulos)
    {
        foreach (var modeloRotuloEmbalagemAssociacaoDto in modeloRotuloEmbalagemAssociacaoDtos)
        {
            if (modeloRotuloEmbalagemAssociacaoDto.ModeloRotuloId == Guid.Empty) continue;

            var modeloRotulo = modelosRotulos
                .First(c => c.Id == modeloRotuloEmbalagemAssociacaoDto.ModeloRotuloId).Id;

            var formaFarmaceutica = formasFarmaceuticas
                .SingleOrDefault(c => c.Id == modeloRotuloEmbalagemAssociacaoDto.FormaFarmaceuticaId.GetValueOrDefault())?.Id;

            var modeloRotuloEmbalagemAssociacao = new ModeloRotuloEmbalagemAssociacao(this, formaFarmaceutica, modeloRotulo);

            ModeloRotuloEmbalagemAssociacao.Add(modeloRotuloEmbalagemAssociacao);
        }
    }

    private void AtualizarAssociacoesModeloRotulo(
        IEnumerable<ModeloRotuloEmbalagemAssociacaoDto> modeloRotuloEmbalagemAssociacaoDtos,
        List<FormaFarmaceutica> formasFarmaceuticas,
        List<ModeloRotulo> modelosRotulo)
    {
        var modelosRotuloAssociacao = ModeloRotuloEmbalagemAssociacao.ToList();

        if (modeloRotuloEmbalagemAssociacaoDtos is not null)
            foreach (var modeloRotuloEmbalagem in modeloRotuloEmbalagemAssociacaoDtos)
            {
                var modeloRotuloId = modelosRotulo
                    .First(c => c.Id == modeloRotuloEmbalagem.ModeloRotuloId).Id;

                var formaFarmaceuticaId = formasFarmaceuticas
                    .SingleOrDefault(c => c.Id == modeloRotuloEmbalagem.FormaFarmaceuticaId.GetValueOrDefault())?.Id;

                var modeloRotuloEmbalagemAssociacao = ModeloRotuloEmbalagemAssociacao
                    .SingleOrDefault(c => c.ModeloRotuloId == modeloRotuloId &&
                                          c.FormaFarmaceuticaId == formaFarmaceuticaId);

                if (modeloRotuloEmbalagemAssociacao is null)
                {
                    modeloRotuloEmbalagemAssociacao = new ModeloRotuloEmbalagemAssociacao(this,
                        formaFarmaceuticaId,
                        modeloRotuloId);

                    ModeloRotuloEmbalagemAssociacao.Add(modeloRotuloEmbalagemAssociacao);

                    continue;
                }

                modelosRotuloAssociacao.Remove(modeloRotuloEmbalagemAssociacao);
            }

        foreach (var modelo in modelosRotuloAssociacao)
            ModeloRotuloEmbalagemAssociacao.Remove(modelo);
    }

    #endregion
}