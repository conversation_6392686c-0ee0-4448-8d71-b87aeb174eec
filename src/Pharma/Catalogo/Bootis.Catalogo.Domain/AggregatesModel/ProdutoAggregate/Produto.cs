using Bootis.Catalogo.Common;
using Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.CasAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.DcbAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Events;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAssociadoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoDiluidoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoMensagemAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.SubGrupoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.TipoCapsulaAggregate;
using Bootis.Catalogo.Domain.Dtos.Produto;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoCapsulaPronta;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoEmbalagem;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoMateriaPrima;
using Bootis.Catalogo.Domain.Dtos.ProdutoDiluido;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Catalogo.Resources;
using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Events;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;

public class Produto() : Entity, ITenant, IAggregateRoot
{
    public Produto(string descricao,
        string descricaoRotulo,
        UnidadeMedidaAbreviacao unidadeEstoqueId,
        Fornecedor fornecedor,
        decimal valorCusto,
        decimal margemLucro,
        bool controlaLote,
        bool usoContinuo,
        bool etiqueta,
        bool controlaQualidade,
        SubGrupo subGrupo,
        TipoClasseProdutoAbreviacao classeProdutoId,
        string codigoBarras,
        string linkImagem,
        bool desativarProjecaoEstoque,
        decimal estoqueMinimo,
        decimal estoqueMaximo) : this()
    {
        Descricao = descricao;
        Ativo = true;
        DescricaoRotulo = descricaoRotulo;
        UnidadeEstoqueId = unidadeEstoqueId;
        Fornecedor = fornecedor;
        MargemLucro = margemLucro;
        ValorCusto = valorCusto;
        ControlaLote = controlaLote;
        UsoContinuo = usoContinuo;
        Etiqueta = etiqueta;
        ControlaQualidade = controlaQualidade;
        SubGrupo = subGrupo;
        ClasseProdutoId = classeProdutoId;
        CodigoBarras = codigoBarras;
        LinkImagem = linkImagem;
        DesativarProjecaoEstoque = desativarProjecaoEstoque;
        EstoqueMinimo = estoqueMinimo;
        EstoqueMaximo = estoqueMaximo;

        CalcularValorVenda();
    }

    public Produto(string descricao,
        string descricaoRotulo,
        UnidadeMedidaAbreviacao unidadeEstoqueId,
        Guid? fornecedorId,
        decimal valorCusto,
        decimal margemLucro,
        bool controlaLote,
        bool usoContinuo,
        bool etiqueta,
        bool controlaQualidade,
        Guid subGrupoId,
        TipoClasseProdutoAbreviacao classeProdutoId,
        Guid tenantId,
        Guid groupTenantId,
        TipoClassificacaoPsicotropicaMedicamento? tipoClassificacaoPsicotropicaMedicamentoId,
        TipoTarjaMedicamento? tipoTarjaMedicamentoId,
        string codigoBarras,
        string linkImagem,
        bool desativarProjecaoEstoque,
        decimal estoqueMinimo,
        decimal estoqueMaximo) : this()
    {
        Descricao = descricao;
        Ativo = true;
        DescricaoRotulo = descricaoRotulo;
        UnidadeEstoqueId = unidadeEstoqueId;
        FornecedorId = fornecedorId;
        MargemLucro = margemLucro;
        ValorCusto = valorCusto;
        ControlaLote = controlaLote;
        UsoContinuo = usoContinuo;
        Etiqueta = etiqueta;
        ControlaQualidade = controlaQualidade;
        SubGrupoId = subGrupoId;
        ClasseProdutoId = classeProdutoId;
        TenantId = tenantId;
        GroupTenantId = groupTenantId;
        TipoClassificacaoPsicotropicaMedicamentoId = tipoClassificacaoPsicotropicaMedicamentoId;
        TipoTarjaMedicamentoId = tipoTarjaMedicamentoId;
        CodigoBarras = codigoBarras;
        LinkImagem = linkImagem;
        DesativarProjecaoEstoque = desativarProjecaoEstoque;
        EstoqueMinimo = estoqueMinimo;
        EstoqueMaximo = estoqueMaximo;

        CalcularValorVenda();
    }

    public int SequenciaGroupTenant { get; private set; }
    public string Descricao { get; private set; }
    public bool Ativo { get; private set; }
    public string CodigoBarras { get; private set; }
    public string LinkImagem { get; private set; }
    public string DescricaoRotulo { get; private set; }
    public UnidadeMedidaAbreviacao UnidadeEstoqueId { get; private set; }
    public Guid? FornecedorId { get; private set; }
    public decimal ValorCusto { get; private set; }
    public decimal MargemLucro { get; private set; }
    public decimal ValorVenda { get; private set; }
    public Guid SubGrupoId { get; private set; }
    public bool ControlaLote { get; private set; }
    public bool UsoContinuo { get; private set; }
    public bool Etiqueta { get; private set; }
    public bool ControlaQualidade { get; private set; }
    public TipoTarjaMedicamento? TipoTarjaMedicamentoId { get; private set; }
    public TipoClassificacaoPsicotropicaMedicamento? TipoClassificacaoPsicotropicaMedicamentoId { get; private set; }
    public TipoClasseProdutoAbreviacao ClasseProdutoId { get; private set; }
    public bool DesativarProjecaoEstoque { get; private set; }
    public decimal? EstoqueMinimo { get; private set; }
    public decimal? EstoqueMaximo { get; private set; }
    public DateOnly? DataUltimaProjecaoAplicada { get; private set; }
    public decimal? MediaConsumoDiaria { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    #region Navigation properties

    public virtual Fornecedor Fornecedor { get; set; }
    public virtual SubGrupo SubGrupo { get; set; }
    public virtual ProdutoMateriaPrima ProdutoMateriaPrima { get; set; }
    public virtual ProdutoEmbalagem ProdutoEmbalagem { get; set; }
    public virtual ProdutoTipoCapsula ProdutoTipoCapsula { get; set; }
    public virtual ProdutoCapsulaPronta ProdutoCapsulaPronta { get; set; }
    public virtual ICollection<ProdutoAssociado> ProdutoAssociado { get; set; } = new List<ProdutoAssociado>();
    public virtual ICollection<ProdutoDiluido> ProdutoDiluido { get; set; } = new List<ProdutoDiluido>();
    public virtual ICollection<ProdutoIncompativel> ProdutoIncompativel { get; set; } = new List<ProdutoIncompativel>();
    public virtual ICollection<ProdutoSinonimo> ProdutoSinonimo { get; set; } = new List<ProdutoSinonimo>();
    public virtual ICollection<ProdutoMensagem> ProdutoMensagem { get; set; } = new List<ProdutoMensagem>();

    #endregion

    #region Operações CRUD Produto

    public void AtualizarProduto(IProdutoDto produtoDto, SubGrupo subGrupo, Fornecedor fornecedor)
    {
        // TODO Validar em outro lugar se o produto já possui lotes cadastrados
        // if (produtoDto.ControlaLote && Lote.Count >= 1)
        //     throw new DomainException(
        //         Localizer.Instance.GetMessage_Produto_PossuiQuantidadeEmEstoque(Descricao));

        if (subGrupo.Grupo.DesativarProjecaoEstoque && !produtoDto.DesativarProjecaoEstoque)
            throw new ValidationException(nameof(subGrupo.Grupo.Descricao),
                Localizer.Instance.GetMessage_Produto_GrupoSemProjecaoEstoque(subGrupo.Grupo.Descricao));

        Descricao = produtoDto.Descricao;
        DescricaoRotulo = produtoDto.DescricaoRotulo;
        UnidadeEstoqueId = produtoDto.UnidadeEstoqueId;
        ControlaLote = produtoDto.ControlaLote;
        UsoContinuo = produtoDto.UsoContinuo;
        Etiqueta = produtoDto.Etiqueta;
        ControlaQualidade = produtoDto.ControlaQualidade;
        SubGrupo = subGrupo;
        Fornecedor = fornecedor;
        CodigoBarras = produtoDto.CodigoBarras;
        LinkImagem = produtoDto.LinkImagem;
        DesativarProjecaoEstoque = produtoDto.DesativarProjecaoEstoque;
        EstoqueMinimo = produtoDto.EstoqueMinimo;
        EstoqueMaximo = produtoDto.EstoqueMaximo;
    }

    public void AtualizarStatus(bool ativo)
    {
        Ativo = ativo;
    }

    public void AtualizarClasseProduto(TipoClasseProdutoAbreviacao classeProduto)
    {
        ClasseProdutoId = classeProduto;
    }

    public void AlterarValorCusto(decimal valorCusto)
    {
        ValorCusto = valorCusto;
        CalcularValorVenda();
    }

    public void AlterarMargemLucro(decimal margemLucro)
    {
        MargemLucro = margemLucro;
    }

    public void AtualizarValorVenda(decimal valorVenda)
    {
        ValorVenda = valorVenda;

        CalcularMargemLucro();
    }

    public void AplicarProjecao(decimal estoqueMinimo, decimal? estoqueMaximo, decimal mediaConsumo,
        DateOnly dataAplicacao)
    {
        if (estoqueMinimo < 0)
            throw new DomainException(Localizer.Instance.GetMessage_Produto_EstoqueMinimoInvalido());

        if (estoqueMaximo.HasValue)
        {
            if (estoqueMaximo.Value < 0)
                throw new DomainException(Localizer.Instance.GetMessage_Produto_EstoqueMaximoInvalido());

            if (estoqueMinimo > estoqueMaximo.Value)
                throw new DomainException(Localizer.Instance.GetMessage_Produto_EstoqueInvalido());
        }

        EstoqueMinimo = estoqueMinimo;
        EstoqueMaximo = estoqueMaximo;
        MediaConsumoDiaria = mediaConsumo;
        DataUltimaProjecaoAplicada = dataAplicacao;
    }

    public override void OnRemoved()
    {
        var produtoRemovido = new RemoverProdutoEvent
        {
            Id = Id
        };

        DomainEvent.RaiseExternal(produtoRemovido);
    }

    public override void OnAdded()
    {
        var novoProduto = new NovoProdutoEvent
        {
            ValorVenda = ValorVenda,
            GroupTenantId = GroupTenantId,
            Id = Id,
            TenantId = TenantId,
            Descricao = Descricao,
            ValorCusto = ValorCusto,
            UnidadeMedoda = UnidadeEstoqueId
        };

        DomainEvent.RaiseExternal(novoProduto);
    }

    public override void OnUpdated()
    {
        var produtoAtualizado = new ProdutoAlteradoEvent
        {
            Id = Id,
            Descricao = Descricao,
            ValorVenda = ValorVenda,
            ValorCusto = ValorCusto,
            GroupTenantId = GroupTenantId
        };

        DomainEvent.RaiseExternal(produtoAtualizado);
    }

    #endregion

    #region Cálculos financeiro Produto

    private void CalcularMargemLucro()
    {
        if (ValorCusto > 0)
            MargemLucro = (ValorVenda / ValorCusto - 1) * 100;
        else
            CalcularValorCusto();
    }

    private void CalcularValorCusto()
    {
        if (MargemLucro > 0)
            ValorCusto = ValorVenda / ((MargemLucro + 100) / 100);
        else
            ValorCusto = ValorVenda;
    }

    private void CalcularValorVenda()
    {
        if (MargemLucro > 0)
            ValorVenda = ValorCusto + ValorCusto / 100 * MargemLucro;
        else
            ValorVenda = ValorCusto;
    }

    #endregion

    #region Operações CRUD das especificações de Produto

    public void AtualizarProdutoEmbalagem(EmbalagemClassificacao embalagemClassificacao,
        decimal? volume,
        IEnumerable<ProdutoEmbalagemAssociacaoDto> produtoEmbalagemAssociacaoDtos,
        IEnumerable<ProdutoEmbalagem> produtosEmbalagem,
        IEnumerable<ProdutoEmbalagemCapsulaTamanhoAssociacaoDto> produtoEmbalagemCapsulaTamanhoAssociacaoDtos,
        IEnumerable<ModeloRotuloEmbalagemAssociacaoDto> modeloRotuloEmbalagemAssociacaoDtos,
        IEnumerable<CapsulaTamanho> capsulasTamanho,
        List<FormaFarmaceutica> formasFarmaceuticas,
        List<ModeloRotulo> modelosRotulo)
    {
        if (ProdutoEmbalagem is not null)
        {
            ProdutoEmbalagem.AtualizarProdutoEmbalagem(volume, embalagemClassificacao, produtoEmbalagemAssociacaoDtos,
                produtosEmbalagem, produtoEmbalagemCapsulaTamanhoAssociacaoDtos, modeloRotuloEmbalagemAssociacaoDtos,
                capsulasTamanho, formasFarmaceuticas, modelosRotulo);
        }
        else
        {
            ProdutoEmbalagem = new ProdutoEmbalagem(this,
                embalagemClassificacao?.Id ?? null,
                volume);

            if (produtoEmbalagemAssociacaoDtos is not null)
                ProdutoEmbalagem.CadastrarAssociacacoesProdutoEmbalagem(produtoEmbalagemAssociacaoDtos,
                    produtosEmbalagem);
            if (produtoEmbalagemCapsulaTamanhoAssociacaoDtos is not null)
                ProdutoEmbalagem.CadastrarAssociacoesCapsulaTamanho(produtoEmbalagemCapsulaTamanhoAssociacaoDtos,
                    capsulasTamanho);
            if (modeloRotuloEmbalagemAssociacaoDtos is not null)
                ProdutoEmbalagem.CadastrarAssociacoesModeloRotulo(modeloRotuloEmbalagemAssociacaoDtos,
                    formasFarmaceuticas, modelosRotulo);
        }

        AtualizarClasseProduto(TipoClasseProdutoAbreviacao.Embalagem);
    }

    public static void ValidarModeloRotuloAssociacao(
        IEnumerable<ModeloRotuloEmbalagemAssociacaoDto> modeloRotuloAssociacao)
    {
        if (modeloRotuloAssociacao.Any(c => c.FormaFarmaceuticaId is null) && modeloRotuloAssociacao.Count(c => c.FormaFarmaceuticaId is null) > 1)
        {
            throw new ValidationException(
                Localizer.Instance.GetMessage_ProdutoEmbalagem_ModeloRotuloAssociacaoExistenteParaTodasAsFormasFarmaceuticas());
        }

        if (modeloRotuloAssociacao
        .GroupBy(c => c.FormaFarmaceuticaId)
        .Where(grupo => grupo.Select(c => c.ModeloRotuloId).Distinct().Count() != grupo.Count())
        .Any())
        {
            throw new ValidationException(
                Localizer.Instance.GetMessage_ProdutoEmbalagem_ModeloRotuloDuplicadoParaFormaFarmaceutica());
        }
    }

    public void AtualizarProdutoTipoCapsula(CapsulaCor capsulaCor, TipoCapsula tipoCapsula,
        CapsulaTamanho capsulaTamanho)
    {
        if (ProdutoTipoCapsula is not null)
            ProdutoTipoCapsula.AtualizarProdutoTipoCapsula(capsulaCor, tipoCapsula, capsulaTamanho);
        else
            ProdutoTipoCapsula = new ProdutoTipoCapsula(this,
                capsulaCor,
                tipoCapsula,
                capsulaTamanho);

        AtualizarClasseProduto(TipoClasseProdutoAbreviacao.TipoCapsula);
    }

    public void AtualizarProdutoCapsulaPronta(CapsulaTamanho capsulaTamanho,
        IEnumerable<ProdutoMateriaPrima> produtosMateriaPrima,
        IEnumerable<ProdutoCapsulaProntaMateriaPrimaAssociacaoDto> produtoCapsulaProntaMateriaPrimaAssociacaoDtos)
    {
        if (ProdutoCapsulaPronta is not null)
        {
            ProdutoCapsulaPronta.AtualizarProdutoCapsulaPronta(capsulaTamanho,
                produtosMateriaPrima,
                produtoCapsulaProntaMateriaPrimaAssociacaoDtos);
        }
        else
        {
            ProdutoCapsulaPronta = new ProdutoCapsulaPronta(this,
                capsulaTamanho);

            if (produtoCapsulaProntaMateriaPrimaAssociacaoDtos is not null)
                ProdutoCapsulaPronta.CadastrarAssociacoesProdutoMateriaPrima(produtosMateriaPrima,
                    produtoCapsulaProntaMateriaPrimaAssociacaoDtos);
        }

        AtualizarClasseProduto(TipoClasseProdutoAbreviacao.CapsulaPronta);
    }

    public void AtualizarProdutoMateriaPrima(IProdutoMateriaPrimaDto produtoMateriaPrimaDto, Cas cas,
        Dcb dcb, ProdutoMateriaPrima produtoExcipiente)
    {
        if (ProdutoMateriaPrima is not null)
            ProdutoMateriaPrima.Atualizar(produtoMateriaPrimaDto, cas, dcb, produtoExcipiente);
        else
            ProdutoMateriaPrima = new ProdutoMateriaPrima(this,
                produtoMateriaPrimaDto,
                cas,
                dcb,
                produtoExcipiente);

        AtualizarClasseProduto(TipoClasseProdutoAbreviacao.MateriaPrima);
    }

    #endregion

    #region Operações Produto Diluído

    public ProdutoDiluido AdicionarProdutoDiluido(IProdutoDiluidoDto produtoDiluidoDto,
        FormaFarmaceutica formaFarmaceutica)
    {
        ValidarNovoProdutoDiluido(produtoDiluidoDto, formaFarmaceutica, null);

        var produtoDiluido = new ProdutoDiluido(this,
            formaFarmaceutica,
            produtoDiluidoDto);

        ProdutoDiluido.Add(produtoDiluido);

        return produtoDiluido;
    }

    public ProdutoDiluido AtualizarProdutoDiluido(IProdutoDiluidoDto produtoDiluidoDto,
        FormaFarmaceutica formaFarmaceutica, ProdutoDiluido produtoDiluido)
    {
        ValidarNovoProdutoDiluido(produtoDiluidoDto, formaFarmaceutica, produtoDiluido.Id);

        produtoDiluido.Atualizar(formaFarmaceutica, produtoDiluidoDto);

        return produtoDiluido;
    }

    private void ValidarNovoProdutoDiluido(IProdutoDiluidoDto dto, FormaFarmaceutica formaFarmaceutica,
        Guid? produtoDiluidoId)
    {
        ValidarClasseProdutoParaProdutoDiluido();

        if (dto.SeTodasFormasFarmaceuticas && dto.SeQualquerDosagem && ProdutoDiluido.Count > 0)
            throw new DomainException(nameof(CatalogoErrorCode),
                (int)CatalogoErrorCode.ProdutoDiluido_ValidationDosagem,
                Descricao);

        var tipoUnidade = UnidadeMedidaCreator.Criar(dto.UnidadeMedidaId).TipoUnidade;

        var validations = new List<string>();

        foreach (var produtoDiluido in ProdutoDiluido)
        {
            if (produtoDiluido.Id == produtoDiluidoId) continue;

            var validation = produtoDiluido.ValidarProdutoDiluido(dto, formaFarmaceutica, tipoUnidade);

            if (validation != null) validations.Add(validation);
        }

        if (validations.Count > 0) throw new DomainException(nameof(CatalogoErrorCode),
                    (int)CatalogoErrorCode.ProdutoDiluido_ValidationDosagem, Descricao);
    }

    private void ValidarClasseProdutoParaProdutoDiluido()
    {
        if (ClasseProdutoId != TipoClasseProdutoAbreviacao.MateriaPrima)
            throw new ValidationException(nameof(Id),
                Localizer.Instance.GetMessage_ProdutoDiluido_ProdutoInvalido(Id));
    }

    public void SetProdutoTipoCapsula(ProdutoTipoCapsula produtoTipoCapsula)
    {
        ProdutoTipoCapsula = produtoTipoCapsula;
    }

    #endregion
}