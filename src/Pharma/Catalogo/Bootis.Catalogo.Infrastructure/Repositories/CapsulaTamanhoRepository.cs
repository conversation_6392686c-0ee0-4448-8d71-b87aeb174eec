using Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;
using Bootis.Shared.Infrastructure;
using Bootis.Shared.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using RabbitMQ.Client;

namespace Bootis.Catalogo.Infrastructure.Repositories;

public class CapsulaTamanhoRepository(IDbContext context) : Repository<CapsulaTamanho>(context), ICapsulaTamanhoRepository
{
    public async Task<List<CapsulaTamanho>> ObterPorIdsAsync(IEnumerable<Guid> ids)
    {
        return await Context.Set<CapsulaTamanho>()
            .Where(c => ids.Contains(c.Id))
            .ToListAsync();
    }
}