using System.Data;
using Bootis.Catalogo.Application.Requests.ProdutoFichaTecnica.Obter;
using Bootis.Shared.Application.Interfaces;
using Dapper;
using MediatR;
using Npgsql.Replication;

namespace Bootis.Catalogo.Infrastructure.Queries.ProdutoFichaTecnica.Obter;

public class ObterProdutoFichaTecnicaRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT pft.id AS produto_ficha_tecnica_id,
                                  pft.nome_cientifico,
                                  pft.nome_popular,
                                  cas.numero_cas AS cas_numero,
                                  dcb.numero_dcb  AS dcb_numero,
                                  pft.formula_molecular,
                                  mp.peso_molecular_sal AS peso_molecular,
                                  pft.informacoes_complementares,
                                  pft.numero_revisao,
                                  pft.data_cadastro,
                                  usr.id AS usuario_id,
                                  CONCAT(usr.nome, ' ', usr.sobrenome) AS usuario_nome_completo
                             FROM produtos prod
                                  LEFT JOIN produtos_ficha_tecnica pft ON
                                            pft.produto_id = prod.id 
                                  INNER JOIN produtos_materia_prima mp ON
                                            mp.produto_id = prod.id
                                  LEFT JOIN cas cas ON
                                            cas.id = mp.cas_id
                                  LEFT JOIN dcbs dcb ON
                                           dcb.id = mp.dcb_id
                                  LEFT JOIN usuarios usr ON
                                            usr.id = pft.usuario_id
                            WHERE prod.id = @produtoId
                           """;

        const string sqlEspecificacoes = """
                                         SELECT pfte.ensaio_controle_qualidade_id AS ensaio_id,
                                                ecq.descricao AS ensaio_descricao,
                                                pfte.especificacao,
                                                pfte.bibliografia_id,
                                                b.descricao AS bibliografia_descricao,
                                                pfte.mostrar_certificado_analise                                           
                                           FROM produtos_ficha_tecnica_especificacao pfte
                                                LEFT JOIN ensaios_controle_qualidade ecq ON
                                                          ecq.id = pfte.ensaio_controle_qualidade_id
                                                LEFT JOIN bibliografias b ON
                                                          b.id = pfte.bibliografia_id
                                          WHERE pfte.produto_ficha_tecnica_id = @id;
                                         """;

        var parameters = new
        {
            produtoId = request.ProdutoId,
            groupTenantId = userContext.GroupTenantId
        };

        var result = await connection.QueryFirstOrDefaultAsync<ObterResponse>(sql, parameters).ConfigureAwait(false);

        if (result is not null && result.ProdutoFichaTecnicaId is not null)
        {
            var especificacoes = await connection.QueryAsync<ObterEspecificacoesResponse>(sqlEspecificacoes, new { id = result.ProdutoFichaTecnicaId }).ConfigureAwait(false);

            result.Especificacoes = especificacoes;
        }

        return result;
    }
}