using System.Data;
using Bootis.Catalogo.Application.Requests.FormaFarmaceutica.Validar;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.FormaFarmaceutica.Validar;

public class
    ValidarVinculoEmbalagemClassificacaoRequestHandler(
        IUserContext userContext,
        IDbConnection connection)
    : IRequestHandler<ValidarVinculoEmbalagemClassificacaoRequest>
{
    public async Task Handle(ValidarVinculoEmbalagemClassificacaoRequest request, CancellationToken cancellationToken)
    {
        var sql = """
                  SELECT true
                  FROM embalagens_classificacao_forma_farmaceutica ecf
                  JOIN formas_farmaceutica ff ON ff.id = ecf.forma_farmaceutica_id
                  JOIN embalagens_classificacao ec ON ec.id = ecf.embalagem_classificacao_id
                  WHERE ff.id = @formaFarmaceuticaId
                    AND ec.id = @embalagemClassificacaoId
                    AND ff.group_tenant_id = @groupTenantId;
                  """;

        var result = await connection.QueryFirstOrDefaultAsync<bool>(sql, new
        {
            formaFarmaceuticaId = request.FormaFarmaceuticaId,
            embalagemClassificacaoId = request.EmbalagemClassificacaoId,
            groupTenantId = userContext.GroupTenantId
        });

        if (result)
            throw new DomainException(
                Localizer.Instance.GetMessage_VinculoEmbalagemFormaFarmaceutica_JaExiste(request
                    .EmbalagemClassificacaoId));
    }
}