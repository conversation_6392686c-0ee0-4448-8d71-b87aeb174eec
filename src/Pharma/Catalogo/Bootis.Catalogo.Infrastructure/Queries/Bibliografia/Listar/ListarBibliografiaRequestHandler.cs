using Bootis.Catalogo.Application.Requests.Bibliografia.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;
using System.Data;

namespace Bootis.Catalogo.Infrastructure.Queries.Bibliografia.Listar;

public class ListarBibliografiaRequestHandler(IUserContext userContext, IDbConnection connection) 
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request, CancellationToken cancellationToken)
    {
        const string sql = """ 
                           SELECT bib.id,
                                  bib.descricao,
                                  bib.ativo
                             FROM bibliografias bib
                            WHERE bib.group_tenant_id = @GroupTenantId
                              AND bib.ativo = true
                                  !@SEARCH_CONDITION@!
                           """;

        var searchDescricao = new StringSearchField
        {
            Field = "bib.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchDescricao)
            .ExecuteAsync();
    }
}