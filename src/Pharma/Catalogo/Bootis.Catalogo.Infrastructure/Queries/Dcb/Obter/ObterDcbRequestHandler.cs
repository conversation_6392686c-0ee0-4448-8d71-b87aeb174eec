using System.Data;
using System.Text;
using Bootis.Catalogo.Application.Requests.Dcb.Obter;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.Dcb.Obter;

public class ObterDcbRequestHandler(
    IDbConnection connection) : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        var sql = new StringBuilder("""
                                    SELECT DCB.id,
                                           DCB.numero_dcb,
                                           DCB.nome_dcb
                                    FROM dcbs DCB
                                    WHERE DCB.Id = @Id
                                    """);

        var result = await connection.QueryFirstOrDefaultAsync<ObterResponse>(sql.ToString(), new { request.Id });

        if (result is null)
        {
            var message = Localizer.Instance.GetMessage_Dcb_IdNaoEncontrado(request.Id);
            throw new DomainException(message);
        }

        return result;
    }
}