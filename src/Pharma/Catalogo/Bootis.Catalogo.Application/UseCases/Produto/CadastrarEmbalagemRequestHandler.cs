using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.Produto.Cadastrar;
using Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Shared.Application.Interfaces;

namespace Bootis.Catalogo.Application.UseCases.Produto;

public class CadastrarEmbalagemRequestHandler(
    IUnitOfWork unitOfWork,
    IModeloRotuloRepository modeloRotuloRepository,
    IFormaFarmaceuticaRepository formaFarmaceuticaRepository,
    ICapsulaTamanhoRepository capsulaTamanhoRepository)
    : CadastrarRequestHandler<CadastrarEmbalagemRequest>(unitOfWork)
{
    protected override async Task InternalCadastrarAsync(CadastrarEmbalagemRequest request,
        Domain.AggregatesModel.ProdutoAggregate.Produto produto, CancellationToken cancellationToken)
    {
        IEnumerable<ProdutoEmbalagem> produtosEmbalagem = null;
        IEnumerable<CapsulaTamanho> capsulasTamanho = null;
        List<Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica> formasFarmaceuticasId = null;
        List<Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo> modelosRotuloId = null;

        var embalagemClassificacaoRepository = unitOfWork.GetRepository<IEmbalagemClassificacaoRepository>();
        
        var embalagemClassificacao = await embalagemClassificacaoRepository
            .ObterEmbalagemClassificacaoAsync(request.ClassificacaoEmbalagemId);

        await ProdutoRepository.ValidarProdutoEmbalagemPorEmbalagemClassificacaoAsync(produto,
            embalagemClassificacao?.Id);

        if (request.NumeroCapsulaAssociacao?.Any() is true)
            capsulasTamanho = await capsulaTamanhoRepository.ObterCapsulasTamanhoAsync(request.NumeroCapsulaAssociacao
                .Select(c => c.NumeroCapsulaId)
                .Where(c => c.HasValue)
                .Cast<Guid>());
        
        if (request.EmbalagemAssociacao?.Any() is true)
            produtosEmbalagem = await ProdutoRepository.ObterProdutosEmbalagemAsync(request.EmbalagemAssociacao
                .Select(c => c.ProdutoEmbalagemId)
                .Where(c => c.HasValue)
                .Cast<Guid>());

        if (request.ModeloRotuloAssociacao?.Any() is true)
        {
            Domain.AggregatesModel.ProdutoAggregate.Produto.ValidarModeloRotuloAssociacao(request.ModeloRotuloAssociacao);

            modelosRotuloId = await modeloRotuloRepository.ObterModelosRotuloAsync(request.ModeloRotuloAssociacao
                .Select(c => c.ModeloRotuloId)
                .Cast<Guid>());

            formasFarmaceuticasId = await formaFarmaceuticaRepository.ObterFormasFarmaceuticasAsync(request.ModeloRotuloAssociacao
                .Select(c => c.FormaFarmaceuticaId)
                .Where(c => c.HasValue)
                .Cast<Guid>());
        }

        produto.AtualizarProdutoEmbalagem(embalagemClassificacao, request.Volume.GetValueOrDefault(),
            request.EmbalagemAssociacao, produtosEmbalagem, request.NumeroCapsulaAssociacao,
            request.ModeloRotuloAssociacao, capsulasTamanho, formasFarmaceuticasId, modelosRotuloId);
    }
}