using Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Catalogo.Application.Extensions;

public static class CapsulaTamanhoRepositoryExtension
{
    public static async Task<CapsulaTamanho?> ObterCapsulaTamanhoAsync(this ICapsulaTamanhoRepository repository,
        Guid? id)
    {
        if (!id.HasValue) return null;

        var capsulaTamanho = await repository.GetByIdAsync(id.Value);

        if (capsulaTamanho == null)
            throw new ValidationException(nameof(id), $"CapsulaTamanho com id {id} não encontrada.");

        return capsulaTamanho;
    }

    public static async Task<IEnumerable<CapsulaTamanho>> ObterCapsulasTamanhoAsync(this ICapsulaTamanhoRepository repository, IEnumerable<Guid> ids)
    {
        var capsulasTamanho = await repository.ObterPorIdsAsync(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !capsulasTamanho.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_CapsulaTamanho_IdNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos),
                idsInvalidos);

        return capsulasTamanho;
    }
}