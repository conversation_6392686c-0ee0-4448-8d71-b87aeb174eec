namespace Bootis.Catalogo.Application.Requests.ProdutoFichaTecnica.Obter;

public class ObterResponse
{
    public Guid? ProdutoFichaTecnicaId { get; set; }
    public string NomeCientifico { get; set; }
    public string NomePopular { get; set; }
    public string CasNumero { get; set; }
    public string Dcb<PERSON>umero { get; set; }
    public string FormulaMolecular { get; set; }
    public decimal PesoMolecular { get; set; }
    public IEnumerable<ObterEspecificacoesResponse> Especificacoes { get; set; }
    public string InformacoesComplementares { get; set; }
    public int NumeroRevisao { get; set; }
    public DateTime? DataCadastro { get; set; }
    public Guid? UsuarioId { get; set; }
    public string UsuarioNomeCompleto { get; set; }
}

public class ObterEspecificacoesResponse
{
    public Guid EnsaioId { get; set; }
    public string EnsaioDescricao { get; set; }
    public string Especificacao { get; set; }
    public Guid BibliografiaId { get; set; }
    public string BibliografiaDescricao { get; set; }
    public bool MostrarCertificadoAnalise { get; set; }
}

