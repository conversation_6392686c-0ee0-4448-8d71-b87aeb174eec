<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Application\Bootis.Shared.Application.csproj" />
        <ProjectReference Include="..\..\Pessoa\Bootis.Pessoa.Application\Bootis.Pessoa.Application.csproj" />
        <ProjectReference Include="..\Bootis.Catalogo.Domain\Bootis.Catalogo.Domain.csproj" />
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Application\Bootis.Shared.Application.csproj" />
        <ProjectReference Include="..\..\Compra\Bootis.Compra.Resources\Bootis.Compra.Resources.csproj" />
        <ProjectReference Include="..\Bootis.Catalogo.Domain\Bootis.Catalogo.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.9"/>
        <PackageReference Include="MediatR" Version="13.0.0" />
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>
