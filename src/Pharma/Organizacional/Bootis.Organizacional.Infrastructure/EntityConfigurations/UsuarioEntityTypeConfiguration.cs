using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Organizacional.Infrastructure.Helpers;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Organizacional.Infrastructure.EntityConfigurations;

public class UsuarioEntityTypeConfiguration : BaseEntityTypeConfiguration<Usuario>
{
    public override void Configure(EntityTypeBuilder<Usuario> builder)
    {
        builder.ToTable("usuarios");

        builder
            .Property(c => c.Nome)
            .NomeDescricao(TamanhoTexto.Cem)
            .IsRequired();

        builder
            .Property(c => c.Sobrenome)
            .NomeDescricao(TamanhoTexto.Cem)
            .IsRequired();

        builder
            .Property(c => c.Cpf)
            .Documento(TamanhoTexto.Vinte)
            .IsRequired(false);

        builder.OwnsOne(b => b.<PERSON>,
            EntityConfigurationHelper
                .GetTelefoneBuildAction<Usuario>("telefone_usuario"));

        builder.OwnsOne(b => b.Preferencias,
            EntityConfigurationHelper
                .GetPreferenciasBuildAction<Usuario>("preferencias_usuario"));

        builder.HasOne<TipoUsuario>()
            .WithMany()
            .HasForeignKey(c => c.TipoId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.Ativo)
            .IsRequired()
            .HasDefaultValue(true);

        builder
            .Property(c => c.DataNascimento)
            .Data()
            .IsRequired(false);

        builder
            .Property(c => c.Email)
            .Web()
            .IsRequired();

        builder
            .Property(c => c.EmailAlternativo)
            .Web()
            .IsRequired(false);

        builder.HasOne(c => c.Empresa)
            .WithMany(c => c.Usuarios)
            .HasForeignKey(c => c.EmpresaId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(c => c.Permissoes)
            .WithOne(c => c.Usuario)
            .HasForeignKey(c => c.UsuarioId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Grupos)
            .WithMany("Usuarios")
            .UsingEntity(c => c.ToTable("usuarios_grupos"))
            .Metadata.SetNavigationAccessMode(PropertyAccessMode.PreferFieldDuringConstruction);

        base.Configure(builder);
    }
}