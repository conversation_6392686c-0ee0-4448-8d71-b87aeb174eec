<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <AssemblyName>Bootis.Organizacional.Api</AssemblyName>
        <RootNamespace>Bootis.Organizacional.Api</RootNamespace>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{062a4b8d-e4d1-45fe-ab82-29943a032bf7}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Api\Bootis.Shared.Api.csproj"/>
        <ProjectReference Include="..\Bootis.Organizacional.Infrastructure\Bootis.Organizacional.Infrastructure.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.9">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <Content Remove="appsettings*.json"/>
    </ItemGroup>

    <ItemGroup Condition="'$(IsPublishable)' == 'true' AND '$(MSBuildProjectName)' == 'Bootis.Pharma.Api'">
        <None Include="appsettings*.json">
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </None>
    </ItemGroup>
</Project>

