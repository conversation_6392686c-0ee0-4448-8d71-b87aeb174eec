using Asp.Versioning;
using Bootis.Organizacional.Application.Requests.Empresa.Atualizar;
using Bootis.Organizacional.Application.Requests.Empresa.Cadastrar;
using Bootis.Organizacional.Application.Requests.Empresa.Listar;
using Bootis.Organizacional.Application.Requests.Empresa.Obter;
using Bootis.Organizacional.Application.Requests.Empresa.Remover;
using Bootis.Organizacional.Application.Requests.Empresa.Validar;
using Bootis.Shared.Api.Attributes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Organizacional.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "AccountManager")]
[Route("accountmanager/v{version:apiVersion}/[controller]")]
public class EmpresaController(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Cadastrar(CadastrarRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPost]
    [Route("ValidarCnpj")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ValidarCnpj(ValidarCnpjRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Administrativo_Empresa_EditarDados, Permissoes.Administrativo_Empresa_EditarEndereco)]
    public async Task<IActionResult> Atualizar(AtualizarRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [Route("AtualizarEmpresaPagadora")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AtualizarEmpresaPagadora(AtualizarEmpresaPagadoraRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [Route("AtualizarEndereco")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Administrativo_Empresa_EditarEndereco)]
    public async Task<IActionResult> AtualizarEndereco(AtualizarEnderecoRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [Route("AtualizarFarmaceuticoResponsavel")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    //TODO adicionar permissao
    public async Task<IActionResult> AtualizarFarmaceuticoResponsavel(AtualizarFarmaceuticoResponsavelRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [Route("AtualizarUsuarioResponsavel")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Administrativo_Empresa_EditarUsuarioResponsavel)]
    public async Task<IActionResult> AtualizarUsuarioResponsavel(AtualizarUsuarioResponsavelRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpDelete]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Remover(RemoverRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpGet]
    [Route("{id:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Administrativo_Empresa_VerDetalhes)]
    public async Task<IActionResult> Obter([FromRoute] Guid id)
    {
        var request = new ObterRequest { Id = id };

        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("Admin/{id:Guid}")]
    [ProducesResponseType(typeof(ObterAdminResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Admin_Manager)]
    public async Task<IActionResult> ObterAdmin([FromRoute] Guid id)
    {
        var request = new ObterAdminRequest { Id = id };

        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterConfiguracao")]
    [ProducesResponseType(typeof(ObterConfiguracaoEmpresaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterConfiguracao([FromServices] IUserContext userContext)
    {
        var request = new ObterConfiguracaoEmpresaRequest
        {
            Id = userContext.TenantId
        };

        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarDetalhado")]
    [ProducesResponseType(typeof(ListarDetalhadoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Listar([FromQuery] ListarDetalhadoRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarNomeFantasia")]
    [ProducesResponseType(typeof(ListarNomeFantasiaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ListarNomeFantasia([FromQuery] ListarNomeFantasiaRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarAdmin")]
    [ProducesResponseType(typeof(ListarAdminResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Admin_Manager)]
    public async Task<IActionResult> ListarAdmin([FromQuery] ListarAdminRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarDetalhadoAdmin")]
    [ProducesResponseType(typeof(ListarDetalhadoAdminResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Admin_Manager)]
    public async Task<IActionResult> ListarDetalhadoAdmin([FromQuery] ListarDetalhadoAdminRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpPut]
    [Route("AtualizarStatus")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Administrativo_Empresa_AlterarStatus)]
    public async Task<IActionResult> AtualizarStatus(AtualizarStatusRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }
}