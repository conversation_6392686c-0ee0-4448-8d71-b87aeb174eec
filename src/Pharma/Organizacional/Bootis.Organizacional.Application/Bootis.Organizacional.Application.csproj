<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{7c2417c9-f1be-43f1-bce5-4ecdd4ba565b}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MediatR" Version="13.0.0"/>
        <PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="9.0.9"/>
        <PackageReference Include="Extensions.FluentValidation.Br" Version="1.0.2"/>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>


    <ItemGroup>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Application\Bootis.Shared.Application.csproj"/>
        <ProjectReference Include="..\Bootis.Organizacional.Common\Bootis.Organizacional.Common.csproj"/>
        <ProjectReference Include="..\Bootis.Organizacional.Contracts\Bootis.Organizacional.Contracts.csproj"/>
        <ProjectReference Include="..\Bootis.Organizacional.Domain\Bootis.Organizacional.Domain.csproj"/>
        <ProjectReference Include="..\Bootis.Organizacional.Resources\Bootis.Organizacional.Resources.csproj"/>
    </ItemGroup>

</Project>

