namespace Bootis.Organizacional.Application.Requests.Empresa.Obter;

public class ObterResponse
{
    public Guid Id { get; set; }
    public string RazaoSocial { get; set; }
    public string NomeFantasia { get; set; }
    public string InscricaoEstadual { get; set; }
    public string InscricaoMunicipal { get; set; }
    public string Cnae { get; set; }
    public string Cnpj { get; set; }
    public int? TipoMoedaId { get; set; }
    public Guid? EmpresaPagadoraId { get; set; }
    public bool Ativa { get; set; }
    public EnderecoResponse Endereco { get; set; }
    public UsuarioResponse Usuario { get; set; }
    public FarmaceuticoResponsavelResponse FarmaceuticoResponsavel { get; set; }
    public string Telefone { get; set; }
    public string Email { get; set; }
    public string Site { get; set; }
    public string NomeConglomerado { get; set; }
    public Guid ConglomeradoId { get; set; }
    public int TipoId { get; set; }

    public class EnderecoResponse
    {
        public string Cep { get; set; }
        public string Logradouro { get; set; }
        public string Complemento { get; set; }
        public int Numero { get; set; }
        public string Bairro { get; set; }
        public string Cidade { get; set; }
        public string Estado { get; set; }
    }

    public class UsuarioResponse
    {
        public string Nome { get; set; }
        public string Sobrenome { get; set; }
        public string Email { get; set; }
        public Guid UsuarioResposavelId { get; set; }
    }

    public class FarmaceuticoResponsavelResponse
    {
        public string Nome_Farmaceutico { get; set; }
        public string Nr_Crf { get; set; }
        public string Uf_Crf { get; set; }
        public string Nr_Cpf { get; set; }
        public string Nr_Rg { get; set; }
        public string Uf_Orgao_Expedidor { get; set; }
    }
}