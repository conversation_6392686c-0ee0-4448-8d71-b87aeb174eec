using Bootis.Organizacional.Domain.Dtos;
using MediatR;

namespace Bootis.Organizacional.Application.Requests.Empresa.Cadastrar;

public class CadastrarRequest : IRequest
{
    public string RazaoSocial { get; init; }
    public string NomeFantasia { get; init; }
    public string NomeConglomerado { get; init; }
    public string Cnpj { get; init; }
    public string InscricaoEstadual { get; init; }
    public string InscricaoMunicipal { get; init; }
    public string Cnae { get; init; }
    public string Email { get; init; }
    public string Site { get; init; }
    public Guid? EmpresaPagadoraId { get; init; }
    public string Telefone { get; init; }
    public EnderecoDto Endereco { get; init; }
    public UsuarioDto Usuario { get; init; }
    public FarmaceuticoResponsavelDto FarmaceuticoResponsavel { get; init; }
}