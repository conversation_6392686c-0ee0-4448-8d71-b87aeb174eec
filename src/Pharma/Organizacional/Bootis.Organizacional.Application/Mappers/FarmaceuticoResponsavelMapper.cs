using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.Dtos;

namespace Bootis.Organizacional.Application.Mappers;

public static class FarmaceuticoResponsavelMapper
{
    public static EmpresaFarmaceuticoResponsavel FarmaceuticoFrom(FarmaceuticoResponsavelDto farmaceuticoResponsavelDto)
    {
        return farmaceuticoResponsavelDto == null
            ? null
            : new EmpresaFarmaceuticoResponsavel(farmaceuticoResponsavelDto.Nome_Farmaceutico, farmaceuticoResponsavelDto.Nr_Crf, farmaceuticoResponsavelDto.Uf_Crf,
                farmaceuticoResponsavelDto.Nr_Cpf, farmaceuticoResponsavelDto.Nr_Rg, farmaceuticoResponsavelDto.Uf_Orgao_Expedidor);
    }
}