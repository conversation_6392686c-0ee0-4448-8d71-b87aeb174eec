using Bootis.Organizacional.Contracts.Empresa;
using Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaPagadoraAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Organizacional.Domain.ValuesObject;
using Bootis.Shared.Common.Events;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;

public class Empresa : Entity, IAggregateRoot
{
    protected Empresa()
    {
    }

    public Empresa(string razaoSocial, string nomeFantasia, string cnpj, int tipoId, Endereco endereco,
        Usuario responsavel, Conglomerado conglomerado, string cnae, string inscricaoEstadual,
        string inscricaoMunicipal, string site, string email, bool ativa, string telefone) : this()
    {
        RazaoSocial = razaoSocial;
        NomeFantasia = nomeFantasia;
        Cnpj = cnpj;
        TipoId = tipoId;
        Cnae = cnae;
        InscricaoEstadual = inscricaoEstadual;
        InscricaoMunicipal = inscricaoMunicipal;
        Email = email;
        Site = site;
        Ativa = ativa;
        Telefone = telefone;
        Initialize(endereco, responsavel, null, conglomerado);
    }

    public Empresa(string razaoSocial, string nomeFantasia, string cnpj, int tipoId, Endereco endereco,
        Usuario responsavel, EmpresaFarmaceuticoResponsavel farmaceuticoResponsavel, Conglomerado conglomerado, string cnae, string inscricaoEstadual,
        string inscricaoMunicipal, string site, string email, bool ativa, string telefone) : this()
    {
        RazaoSocial = razaoSocial;
        NomeFantasia = nomeFantasia;
        Cnpj = cnpj;
        TipoId = tipoId;
        Cnae = cnae;
        InscricaoEstadual = inscricaoEstadual;
        InscricaoMunicipal = inscricaoMunicipal;
        Email = email;
        Site = site;
        Ativa = ativa;
        Telefone = telefone;
        Initialize(endereco, responsavel, farmaceuticoResponsavel, conglomerado);
    }

    public string RazaoSocial { get; private set; }
    public string NomeFantasia { get; private set; }
    public string Cnpj { get; private set; }
    public string InscricaoEstadual { get; private set; }
    public string InscricaoMunicipal { get; private set; }
    public string Cnae { get; private set; }
    public int TipoId { get; private set; }
    public Guid? EmpresaPagadoraId { get; set; }
    public Guid ConglomeradoId { get; set; }
    public string Telefone { get; private set; }
    public string Email { get; private set; }
    public string Site { get; private set; }
    public bool Ativa { get; private set; }

    #region Navigation properties

    public EmpresaFarmaceuticoResponsavel FarmaceuticoResponsavel { get; private set; }
    public virtual EmpresaPagadora EmpresaPagadora { get; set; }
    public virtual Conglomerado Conglomerado { get; set; }
    public virtual EmpresaResponsavel Responsavel { get; set; }
    public virtual Endereco Endereco { get; set; }
    public virtual ConfiguracaoEmpresa Configuracao { get; set; } = new();
    public virtual ICollection<Usuario> Usuarios { get; set; } = new List<Usuario>();
    public virtual ICollection<Telefone> Telefones { get; set; } = new List<Telefone>();

    #endregion

    private void Initialize(Endereco endereco, Usuario responsavel, EmpresaFarmaceuticoResponsavel farmaceuticoResponsavel, Conglomerado conglomerado)
    {
        Endereco = endereco;
        Conglomerado = conglomerado;
        Responsavel = new EmpresaResponsavel(responsavel);
        FarmaceuticoResponsavel = farmaceuticoResponsavel;

        Usuarios.Add(responsavel);
    }


    public override void OnAdded()
    {
        DomainEvent.RaiseExternal(new EmpresaAtualizadoEvent
            { TenantId = Id, TipoMoedaId = Configuracao.TipoMoedaId.GetValueOrDefault() });
    }

    public void AdicionarTelefone(Telefone telefone)
    {
        Telefones.Add(telefone);
    }

    public void AdicionarUsuario(Usuario usuario)
    {
        Usuarios.Add(usuario);
    }

    public void AtualizarTipoId(int id)
    {
        TipoId = id;
    }

    public void AtualizarResponsavel(Usuario responsavel)
    {
        Responsavel.DefineUsuario(responsavel);
    }

    public void AdicionarEmpresaPagadora(EmpresaPagadora empresaPagadora)
    {
        EmpresaPagadora = empresaPagadora;
    }

    public void AtualizarEmpresaPagadoraId(Guid empresaPagadoraId)
    {
        EmpresaPagadora.AtualizarEmpresaId(empresaPagadoraId);
    }

    public void Atualizar(string nomeFantasia, string razaoSocial, string cnpj,
        string telefone, string email, string site, string inscricaoEstadual,
        string inscricaoMunicipal, string cnae, Guid empresaPagadoraId)
    {
        NomeFantasia = nomeFantasia;
        RazaoSocial = razaoSocial;
        Cnpj = cnpj;
        Telefone = telefone;
        Email = email;
        Site = site;
        InscricaoEstadual = inscricaoEstadual;
        InscricaoMunicipal = inscricaoMunicipal;
        Cnae = cnae;

        AtualizarEmpresaPagadoraId(empresaPagadoraId);
    }

    public void AtualizarEndereco(Endereco endereco)
    {
        if (Endereco?.Equals(endereco) == true) return;

        Endereco = endereco;
    }

    public void AtualizarFarmaceuticoResponsavel(EmpresaFarmaceuticoResponsavel farmaceuticoResponsavel)
    {
        if (FarmaceuticoResponsavel?.Equals(farmaceuticoResponsavel) == true) return;

        FarmaceuticoResponsavel = farmaceuticoResponsavel;
    }

    public void AtualizarStatus(bool ativo)
    {
        Ativa = ativo;
    }

    public override void Remove()
    {
        if (TipoId == TipoEmpresa.Matriz.Id)
            throw new ValidationException(nameof(TipoId), "Não é permitido remover uma Matriz");

        base.Remove();
    }
}