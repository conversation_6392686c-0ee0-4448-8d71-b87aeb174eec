using Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.PermissaoAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Organizacional.Domain.Dtos;

namespace Bootis.Organizacional.Domain.Statics;

public static class PermissoesDepedenciasStatic
{
    public static readonly Dictionary<Permissao, ICollection<Permissao>> Requer = new();
    public static readonly Dictionary<Permissao, ICollection<Permissao>> Requerido = new();

    static PermissoesDepedenciasStatic()
    {
        AddDependencia(PermissoesStatic.AdministrativoUsuariosCadastrar,
            PermissoesStatic.AdministrativoGrupoUsuariosVerLista);
        AddDependencia(PermissoesStatic.AdministrativoUsuarioEditarDadosPessoais,
            PermissoesStatic.AdministrativoUsuariosVerDetalhes);
        AddDependencia(PermissoesStatic.AdministrativoUsuarioEditarPermissao,
            PermissoesStatic.AdministrativoUsuariosVerDetalhes);

        AddDependencia(PermissoesStatic.AdministrativoGrupoUsuariosCriar,
            PermissoesStatic.AdministrativoUsuarioVerLista);
        AddDependencia(PermissoesStatic.AdministrativoGrupoUsuariosEditarDetalhes,
            PermissoesStatic.AdministrativoGrupoUsuariosVerDetalhes);
        AddDependencia(PermissoesStatic.AdministrativoGrupoUsuariosEditarPermissao,
            PermissoesStatic.AdministrativoGrupoUsuariosVerDetalhes);

        AddDependencia(PermissoesStatic.AdministrativoEmpresaEditarDados,
            PermissoesStatic.AdministrativoEmpresaVerDetalhes);
        AddDependencia(PermissoesStatic.AdministrativoEmpresaEditarEndereco,
            PermissoesStatic.AdministrativoEmpresaVerDetalhes);
        AddDependencia(PermissoesStatic.AdministrativoEmpresaEditarUsuarioResponsavel,
            PermissoesStatic.AdministrativoEmpresaVerDetalhes);

        AddDependencia(PermissoesStatic.ComprasFornecedoresVerDetalhes,
            PermissoesStatic.ComprasFornecedoresVerLista);
        AddDependencia(PermissoesStatic.ComprasFornecedoresEditarDetalhes,
            PermissoesStatic.ComprasFornecedoresVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasFornecedoresAlterarStatus,
            PermissoesStatic.ComprasFornecedoresVerLista);
        AddDependencia(PermissoesStatic.ComprasFornecedoresEditarContatos,
            PermissoesStatic.ComprasFornecedoresVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasFornecedoresEditarEnderecos,
            PermissoesStatic.ComprasFornecedoresVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasFornecedoresEditarDocumentos,
            PermissoesStatic.ComprasFornecedoresVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasFornecedoresCadastrar,
            PermissoesStatic.ComprasFornecedoresVerLista);
        AddDependencia(PermissoesStatic.ComprasFornecedoresExcluir,
            PermissoesStatic.ComprasFornecedoresVerLista);

        AddDependencia(PermissoesStatic.ComprasPedidoCompraVerDetalhes,
            PermissoesStatic.ComprasPedidoCompraVerLista);
        AddDependencia(PermissoesStatic.ComprasPedidoCompraEditarDetalhes,
            PermissoesStatic.ComprasPedidoCompraVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasPedidoCompraAprovar,
            PermissoesStatic.ComprasPedidoCompraVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasPedidoCompraReprovar,
            PermissoesStatic.ComprasPedidoCompraVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasPedidoCompraCancelar,
            PermissoesStatic.ComprasPedidoCompraVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasPedidoCompraConfirmarComFornecedor,
            PermissoesStatic.ComprasPedidoCompraVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasPedidoCompraEstornarConfirmadoComFornecedor,
            PermissoesStatic.ComprasPedidoCompraVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasPedidoCompraEstornarLiberado,
            PermissoesStatic.ComprasPedidoCompraVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasPedidoCompraCadastrar,
            PermissoesStatic.ComprasPedidoCompraVerLista);
        AddDependencia(PermissoesStatic.ComprasPedidoCompraExcluir,
            PermissoesStatic.ComprasPedidoCompraVerLista);

        AddDependencia(PermissoesStatic.ComprasNotaFiscalEntradaVerDetalhes,
            PermissoesStatic.ComprasNotaFiscalEntradaVerLista);
        AddDependencia(PermissoesStatic.ComprasNotaFiscalEntradaEditarDetalhes,
            PermissoesStatic.ComprasNotaFiscalEntradaVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasNotaFiscalEntradaAlterarStatus,
            PermissoesStatic.ComprasNotaFiscalEntradaVerLista);
        AddDependencia(PermissoesStatic.ComprasNotaFiscalEntradaLancarLotes,
            PermissoesStatic.ComprasNotaFiscalEntradaVerDetalhes);
        AddDependencia(PermissoesStatic.ComprasNotaFiscalEntradaCadastrar,
            PermissoesStatic.ComprasNotaFiscalEntradaVerLista);
        AddDependencia(PermissoesStatic.ComprasNotaFiscalEntradaExcluir,
            PermissoesStatic.ComprasNotaFiscalEntradaVerLista);

        AddDependencia(PermissoesStatic.EstoqueProdutosVerDetalhes,
            PermissoesStatic.EstoqueProdutosVerLista);
        AddDependencia(PermissoesStatic.EstoqueProdutosCadastrar,
            PermissoesStatic.EstoqueGruposVerLista);
        AddDependencia(PermissoesStatic.EstoqueProdutosCadastrar,
            PermissoesStatic.EstoqueSubGruposVerLista);
        AddDependencia(PermissoesStatic.EstoqueProdutosCadastrar,
            PermissoesStatic.ComprasFornecedoresVerLista);
        AddDependencia(PermissoesStatic.EstoqueProdutosEditarInformacoes,
            PermissoesStatic.EstoqueProdutosVerDetalhes);
        AddDependencia(PermissoesStatic.EstoqueProdutosEditarInformacoesFinanceiras,
            PermissoesStatic.EstoqueProdutosVerDetalhes);
        AddDependencia(PermissoesStatic.EstoqueInformacoesTecnicasVisualizar,
            PermissoesStatic.EstoqueProdutosVerDetalhes);

        AddDependencia(PermissoesStatic.EstoqueProdutosAssociadoVisualizar,
            PermissoesStatic.EstoqueInformacoesTecnicasVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosAssociadoCadastrar,
            PermissoesStatic.EstoqueProdutosAssociadoVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosAssociadoEditarDetalhes,
            PermissoesStatic.EstoqueProdutosAssociadoVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosAssociadoExcluir,
            PermissoesStatic.EstoqueProdutosAssociadoVisualizar);

        AddDependencia(PermissoesStatic.EstoqueProdutosDiluidoVisualizar,
            PermissoesStatic.EstoqueInformacoesTecnicasVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosDiluidoCadastrar,
            PermissoesStatic.EstoqueProdutosDiluidoVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosDiluidoEditarDetalhes,
            PermissoesStatic.EstoqueProdutosDiluidoVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosDiluidoExcluir,
            PermissoesStatic.EstoqueProdutosDiluidoVisualizar);

        AddDependencia(PermissoesStatic.EstoqueProdutosIncompativelVisualizar,
            PermissoesStatic.EstoqueInformacoesTecnicasVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosIncompativelCadastrar,
            PermissoesStatic.EstoqueProdutosIncompativelVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosIncompativelEditarDetalhes,
            PermissoesStatic.EstoqueProdutosIncompativelVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosIncompativelExcluir,
            PermissoesStatic.EstoqueProdutosIncompativelVisualizar);

        AddDependencia(PermissoesStatic.EstoqueProdutosSinonimoVisualizar,
            PermissoesStatic.EstoqueInformacoesTecnicasVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosSinonimoCadastrar,
            PermissoesStatic.EstoqueProdutosSinonimoVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosSinonimoEditarDetalhes,
            PermissoesStatic.EstoqueProdutosSinonimoVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosSinonimoExcluir,
            PermissoesStatic.EstoqueProdutosSinonimoVisualizar);

        AddDependencia(PermissoesStatic.EstoqueProdutosFichaTecnicaVisualizar,
            PermissoesStatic.EstoqueInformacoesTecnicasVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosFichaTecnicaEditar,
            PermissoesStatic.EstoqueProdutosFichaTecnicaVisualizar);

        AddDependencia(PermissoesStatic.EstoqueProdutosMensagemVisualizar,
            PermissoesStatic.EstoqueProdutosVerDetalhes);
        AddDependencia(PermissoesStatic.EstoqueProdutosMensagemCadastrar,
            PermissoesStatic.EstoqueProdutosMensagemVisualizar);
        AddDependencia(PermissoesStatic.EstoqueProdutosMensagemExcluir,
            PermissoesStatic.EstoqueProdutosMensagemVisualizar);

        AddDependencia(PermissoesStatic.EstoqueMensagemProdutoVerDetalhes,
            PermissoesStatic.EstoqueMensagemProdutoVerLista);
        AddDependencia(PermissoesStatic.EstoqueMensagemProdutoCadastrar,
            PermissoesStatic.EstoqueMensagemProdutoVerLista);
        AddDependencia(PermissoesStatic.EstoqueMensagemProdutoEditarDetalhes,
            PermissoesStatic.EstoqueMensagemProdutoVerDetalhes);
        AddDependencia(PermissoesStatic.EstoqueMensagemProdutoRemoverProduto,
            PermissoesStatic.EstoqueMensagemProdutoVerDetalhes);

        AddDependencia(PermissoesStatic.EstoqueGruposEditarDetalhes,
            PermissoesStatic.EstoqueGruposVerDetalhes);

        AddDependencia(PermissoesStatic.EstoqueSubGruposCadastrar,
            PermissoesStatic.EstoqueGruposVerLista);
        AddDependencia(PermissoesStatic.EstoqueSubGruposEditarDetalhes,
            PermissoesStatic.EstoqueSubGruposVerDetalhes);

        AddDependencia(PermissoesStatic.EstoqueLotesCadastrar,
            PermissoesStatic.EstoqueGruposVerLista);
        AddDependencia(PermissoesStatic.EstoqueLotesCadastrar,
            PermissoesStatic.EstoqueLocaisEstoqueVerLista);
        AddDependencia(PermissoesStatic.EstoqueLotesCadastrar,
            PermissoesStatic.ComprasFornecedoresVerLista);
        AddDependencia(PermissoesStatic.EstoqueLotesEditarInformacoes,
            PermissoesStatic.EstoqueLotesVerDetalhes);

        AddDependencia(PermissoesStatic.EstoqueLocaisEstoqueCadastrar,
            PermissoesStatic.AdministrativoEmpresaVerLista);
        AddDependencia(PermissoesStatic.EstoqueLocaisEstoqueEditarDetalhes,
            PermissoesStatic.EstoqueLocaisEstoqueVerDetalhes);

        AddDependencia(PermissoesStatic.EstoqueTransferenciasCadastrar,
            PermissoesStatic.EstoqueLocaisEstoqueVerLista);
        AddDependencia(PermissoesStatic.EstoqueTransferenciasCadastrar,
            PermissoesStatic.EstoqueProdutosVerLista);
        AddDependencia(PermissoesStatic.EstoqueTransferenciasCadastrar,
            PermissoesStatic.EstoqueLotesVerLista);

        AddDependencia(PermissoesStatic.EstoquePerdasCadastrar,
            PermissoesStatic.EstoqueProdutosVerLista);
        AddDependencia(PermissoesStatic.EstoquePerdasCadastrar,
            PermissoesStatic.EstoqueLocaisEstoqueVerLista);
        AddDependencia(PermissoesStatic.EstoquePerdasCadastrar,
            PermissoesStatic.EstoqueLotesVerLista);
        AddDependencia(PermissoesStatic.EstoquePerdasEditarDetalhes,
            PermissoesStatic.EstoquePerdasVerDetalhes);

        AddDependencia(PermissoesStatic.EstoqueAjusteSaldoCadastrar,
            PermissoesStatic.EstoqueProdutosVerLista);
        AddDependencia(PermissoesStatic.EstoqueAjusteSaldoCadastrar,
            PermissoesStatic.EstoqueLocaisEstoqueVerLista);
        AddDependencia(PermissoesStatic.EstoqueAjusteSaldoCadastrar,
            PermissoesStatic.EstoqueLotesVerLista);

        AddDependencia(PermissoesStatic.EstoqueInventarioVerDetalhes,
            PermissoesStatic.EstoqueInventarioVerLista);
        AddDependencia(PermissoesStatic.EstoqueInventarioCadastrar,
            PermissoesStatic.EstoqueInventarioVerLista);
        AddDependencia(PermissoesStatic.EstoqueInventarioExcluir,
            PermissoesStatic.EstoqueInventarioExcluir);
        AddDependencia(PermissoesStatic.EstoqueInventarioVerDetalhesConferencia,
            PermissoesStatic.EstoqueInventarioVerDetalhes);

        AddDependencia(PermissoesStatic.EstoqueRastreabilidadeVisualizar,
            PermissoesStatic.ComprasNotaFiscalEntradaVerLista);
        AddDependencia(PermissoesStatic.EstoqueRastreabilidadeVisualizar,
            PermissoesStatic.VendasPedidosVendaVerLista);
        AddDependencia(PermissoesStatic.EstoqueRastreabilidadeVisualizar,
            PermissoesStatic.EstoquePerdasVerLista);
        AddDependencia(PermissoesStatic.EstoqueRastreabilidadeVisualizar,
            PermissoesStatic.ComprasPedidoCompraVerLista);
        AddDependencia(PermissoesStatic.EstoqueRastreabilidadeVisualizar,
            PermissoesStatic.EstoqueAjusteSaldoVerLista);
        AddDependencia(PermissoesStatic.EstoqueRastreabilidadeVisualizar,
            PermissoesStatic.ProducaoReceitaManipuladaVerLista);
        AddDependencia(PermissoesStatic.EstoqueRastreabilidadeVisualizar,
            PermissoesStatic.EstoqueTransferenciasVerLista);

        AddDependencia(PermissoesStatic.EstoqueMovimentacaoVisualizar,
            PermissoesStatic.EstoqueTransferenciasVerLista);
        AddDependencia(PermissoesStatic.EstoqueMovimentacaoVisualizar,
            PermissoesStatic.EstoquePerdasVerLista);
        AddDependencia(PermissoesStatic.EstoqueMovimentacaoVisualizar,
            PermissoesStatic.EstoqueAjusteSaldoVerLista);
        AddDependencia(PermissoesStatic.EstoqueMovimentacaoVisualizar,
            PermissoesStatic.VendasPedidosVendaVerLista);
        AddDependencia(PermissoesStatic.EstoqueMovimentacaoVisualizar,
            PermissoesStatic.ComprasPedidoCompraVerLista);
        AddDependencia(PermissoesStatic.EstoqueMovimentacaoVisualizar,
            PermissoesStatic.ProducaoReceitaManipuladaVerLista);

        AddDependencia(PermissoesStatic.EstoqueProjecaoVisualizar,
            PermissoesStatic.EstoqueProdutosVerLista);
        AddDependencia(PermissoesStatic.EstoqueProjecaoVisualizar,
            PermissoesStatic.EstoqueGruposVerLista);
        AddDependencia(PermissoesStatic.EstoqueProjecaoVisualizar,
            PermissoesStatic.EstoqueSubGruposVerLista);
        AddDependencia(PermissoesStatic.EstoqueProjecaoVisualizar,
            PermissoesStatic.ComprasFornecedoresVerLista);

        AddDependencia(PermissoesStatic.ProducaoClassificacaoEmbalagemVerDetalhes,
            PermissoesStatic.ProducaoClassificacaoEmbalagemVerLista);
        AddDependencia(PermissoesStatic.ProducaoClassificacaoEmbalagemEditarDetalhes,
            PermissoesStatic.ProducaoClassificacaoEmbalagemVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoClassificacaoEmbalagemAlterarStatus,
            PermissoesStatic.ProducaoClassificacaoEmbalagemVerLista);
        AddDependencia(PermissoesStatic.ProducaoClassificacaoEmbalagemCadastrar,
            PermissoesStatic.ProducaoClassificacaoEmbalagemVerLista);
        AddDependencia(PermissoesStatic.ProducaoClassificacaoEmbalagemExcluir,
            PermissoesStatic.ProducaoClassificacaoEmbalagemVerLista);

        AddDependencia(PermissoesStatic.ProducaoCapsulaCorVerDetalhes,
            PermissoesStatic.ProducaoCapsulaCorVerLista);
        AddDependencia(PermissoesStatic.ProducaoCapsulaCorEditarDetalhes,
            PermissoesStatic.ProducaoCapsulaCorVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoCapsulaCorCadastrar,
            PermissoesStatic.ProducaoCapsulaCorVerLista);
        AddDependencia(PermissoesStatic.ProducaoCapsulaCorExcluir,
            PermissoesStatic.ProducaoCapsulaCorVerLista);

        AddDependencia(PermissoesStatic.ProducaoFormaFarmaceuticaVerDetalhes,
            PermissoesStatic.ProducaoFormaFarmaceuticaVerLista);
        AddDependencia(PermissoesStatic.ProducaoFormaFarmaceuticaEditarDetalhes,
            PermissoesStatic.ProducaoFormaFarmaceuticaVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoFormaFarmaceuticaAlterarStatus,
            PermissoesStatic.ProducaoFormaFarmaceuticaVerLista);
        AddDependencia(PermissoesStatic.ProducaoFormaFarmaceuticaCadastrar,
            PermissoesStatic.ProducaoFormaFarmaceuticaVerLista);
        AddDependencia(PermissoesStatic.ProducaoFormaFarmaceuticaExcluir,
            PermissoesStatic.ProducaoFormaFarmaceuticaVerLista);

        AddDependencia(PermissoesStatic.ProducaoFormulaPadraoVerDetalhes,
            PermissoesStatic.ProducaoFormulaPadraoVerLista);
        AddDependencia(PermissoesStatic.ProducaoFormulaPadraoEditarDetalhes,
            PermissoesStatic.ProducaoFormulaPadraoVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoFormulaPadraoCadastrar,
            PermissoesStatic.ProducaoFormulaPadraoVerLista);
        AddDependencia(PermissoesStatic.ProducaoFormulaPadraoExcluir,
            PermissoesStatic.ProducaoFormulaPadraoVerLista);

        AddDependencia(PermissoesStatic.ProducaoLaboratorioVerDetalhes,
            PermissoesStatic.ProducaoLaboratorioVerLista);
        AddDependencia(PermissoesStatic.ProducaoLaboratorioEditarDetalhes,
            PermissoesStatic.ProducaoLaboratorioVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoLaboratorioAlterarStatus,
            PermissoesStatic.ProducaoLaboratorioVerLista);
        AddDependencia(PermissoesStatic.ProducaoLaboratorioCadastrar,
            PermissoesStatic.ProducaoLaboratorioVerLista);
        AddDependencia(PermissoesStatic.ProducaoLaboratorioExcluir,
            PermissoesStatic.ProducaoLaboratorioVerLista);

        AddDependencia(PermissoesStatic.ProducaoPosologiaVerDetalhes,
            PermissoesStatic.ProducaoPosologiaVerLista);
        AddDependencia(PermissoesStatic.ProducaoPosologiaEditarDetalhes,
            PermissoesStatic.ProducaoPosologiaVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoPosologiaAlterarStatus,
            PermissoesStatic.ProducaoPosologiaVerLista);
        AddDependencia(PermissoesStatic.ProducaoPosologiaCadastrar,
            PermissoesStatic.ProducaoPosologiaVerLista);
        AddDependencia(PermissoesStatic.ProducaoPosologiaExcluir,
            PermissoesStatic.ProducaoPosologiaVerLista);

        AddDependencia(PermissoesStatic.ProducaoModeloRotuloVerDetalhes,
            PermissoesStatic.ProducaoModeloRotuloVerLista);
        AddDependencia(PermissoesStatic.ProducaoModeloRotuloEditarDetalhes,
            PermissoesStatic.ProducaoModeloRotuloVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoModeloRotuloAlterarStatus,
            PermissoesStatic.ProducaoModeloRotuloVerLista);
        AddDependencia(PermissoesStatic.ProducaoModeloRotuloCadastrar,
            PermissoesStatic.ProducaoModeloRotuloVerLista);
        AddDependencia(PermissoesStatic.ProducaoModeloRotuloExcluir,
            PermissoesStatic.ProducaoModeloRotuloVerLista);

        AddDependencia(PermissoesStatic.ProducaoModeloOrdemManipulacaoVerDetalhes,
            PermissoesStatic.ProducaoModeloOrdemManipulacaoVerLista);
        AddDependencia(PermissoesStatic.ProducaoModeloOrdemManipulacaoEditarDetalhes,
            PermissoesStatic.ProducaoModeloOrdemManipulacaoVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoModeloOrdemManipulacaoAlterarStatus,
            PermissoesStatic.ProducaoModeloOrdemManipulacaoVerLista);
        AddDependencia(PermissoesStatic.ProducaoModeloOrdemManipulacaoCadastrar,
            PermissoesStatic.ProducaoModeloOrdemManipulacaoVerLista);
        AddDependencia(PermissoesStatic.ProducaoModeloOrdemManipulacaoExcluir,
            PermissoesStatic.ProducaoModeloOrdemManipulacaoVerLista);

        AddDependencia(PermissoesStatic.ProducaoRotuloReceitaManipuladaEmitir,
            PermissoesStatic.ProducaoRotuloReceitaManipuladaVerLista);
        AddDependencia(PermissoesStatic.ProducaoRotuloReceitaManipuladaEditarDetalhes,
            PermissoesStatic.ProducaoRotuloReceitaManipuladaVerLista);

        AddDependencia(PermissoesStatic.ProducaoOrdemManipulacaoReceitaManipuladaEmitir,
            PermissoesStatic.ProducaoOrdemManipulacaoReceitaManipuladaVerLista);

        AddDependencia(PermissoesStatic.ProducaoReceitaManipuladaVerDetalhes,
            PermissoesStatic.ProducaoReceitaManipuladaVerLista);
        AddDependencia(PermissoesStatic.ProducaoReceitaManipuladaCadastrar,
            PermissoesStatic.ProducaoReceitaManipuladaVerLista);
        AddDependencia(PermissoesStatic.ProducaoReceitaManipuladaEditarDetalhes,
            PermissoesStatic.ProducaoReceitaManipuladaVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoReceitaManipuladaAlterarStatus,
            PermissoesStatic.ProducaoReceitaManipuladaVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoReceitaManipuladaExcluir,
            PermissoesStatic.ProducaoReceitaManipuladaVerLista);
        AddDependencia(PermissoesStatic.ProducaoReceitaManipuladaCancelar,
            PermissoesStatic.ProducaoReceitaManipuladaVerDetalhes);
        AddDependencia(PermissoesStatic.ProducaoIncompatibilidadeLiberar,
            PermissoesStatic.ProducaoReceitaManipuladaVerDetalhes);

        AddDependencia(PermissoesStatic.ProducaoCertificadoAnaliseCadastrar,
            PermissoesStatic.ProducaoCertificadoAnaliseVerLista);
        AddDependencia(PermissoesStatic.ProducaoCertificadoAnaliseCadastrar,
            PermissoesStatic.EstoqueProdutosVerLista);
        AddDependencia(PermissoesStatic.ProducaoCertificadoAnaliseCadastrar,
            PermissoesStatic.EstoqueLotesVerLista);
        AddDependencia(PermissoesStatic.ProducaoCertificadoAnaliseVerDetalhes,
            PermissoesStatic.ProducaoCertificadoAnaliseVerLista);
        AddDependencia(PermissoesStatic.ProducaoCertificadoAnaliseVerDetalhes,
            PermissoesStatic.EstoqueProdutosFichaTecnicaVisualizar);

        AddDependencia(PermissoesStatic.VendasClientesVerDetalhes,
            PermissoesStatic.VendasClientesVerLista);
        AddDependencia(PermissoesStatic.VendasClientesEditarDetalhes,
            PermissoesStatic.VendasClientesVerDetalhes);
        AddDependencia(PermissoesStatic.VendasClientesEditarContatos,
            PermissoesStatic.VendasClientesVerDetalhes);
        AddDependencia(PermissoesStatic.VendasClientesEditarEnderecos,
            PermissoesStatic.VendasClientesVerDetalhes);
        AddDependencia(PermissoesStatic.VendasClientesEditarDocumentos,
            PermissoesStatic.VendasClientesVerDetalhes);
        AddDependencia(PermissoesStatic.VendasClientesAlterarStatus,
            PermissoesStatic.VendasClientesVerLista);
        AddDependencia(PermissoesStatic.VendasClientesCadastrar,
            PermissoesStatic.VendasClientesVerLista);
        AddDependencia(PermissoesStatic.VendasClientesExcluir,
            PermissoesStatic.VendasClientesVerLista);

        AddDependencia(PermissoesStatic.VendasPrescritoresVerDetalhes,
            PermissoesStatic.VendasPrescritoresVerLista);
        AddDependencia(PermissoesStatic.VendasPrescritoresEditarDetalhes,
            PermissoesStatic.VendasPrescritoresVerDetalhes);
        AddDependencia(PermissoesStatic.VendasPrescritoresEditarContatos,
            PermissoesStatic.VendasPrescritoresVerDetalhes);
        AddDependencia(PermissoesStatic.VendasPrescritoresEditarEnderecos,
            PermissoesStatic.VendasPrescritoresVerDetalhes);
        AddDependencia(PermissoesStatic.VendasPrescritoresEditarDocumentos,
            PermissoesStatic.VendasPrescritoresVerDetalhes);
        AddDependencia(PermissoesStatic.VendasPrescritoresAlterarStatus,
            PermissoesStatic.VendasPrescritoresVerLista);
        AddDependencia(PermissoesStatic.VendasPrescritoresCadastrar,
            PermissoesStatic.VendasPrescritoresVerLista);
        AddDependencia(PermissoesStatic.VendasPrescritoresExcluir,
            PermissoesStatic.VendasPrescritoresVerLista);

        AddDependencia(PermissoesStatic.VendasEspecialidadePrescritorVerDetalhes,
            PermissoesStatic.VendasEspecialidadePrescritorVerLista);
        AddDependencia(PermissoesStatic.VendasEspecialidadePrescritorEditarDetalhes,
            PermissoesStatic.VendasEspecialidadePrescritorVerDetalhes);
        AddDependencia(PermissoesStatic.VendasEspecialidadePrescritorAlterarStatus,
            PermissoesStatic.VendasEspecialidadePrescritorVerLista);
        AddDependencia(PermissoesStatic.VendasEspecialidadePrescritorCadastrar,
            PermissoesStatic.VendasEspecialidadePrescritorVerLista);
        AddDependencia(PermissoesStatic.VendasEspecialidadePrescritorExcluir,
            PermissoesStatic.VendasEspecialidadePrescritorVerLista);

        AddDependencia(PermissoesStatic.VendasAtendimentosVerDetalhes,
            PermissoesStatic.VendasAtendimentosVerLista);
        AddDependencia(PermissoesStatic.VendasPrescritoresCadastrar,
            PermissoesStatic.VendasAtendimentosVerLista);

        AddDependencia(PermissoesStatic.VendasPedidosVendaVerDetalhes,
            PermissoesStatic.VendasPedidosVendaVerLista);
        AddDependencia(PermissoesStatic.VendasPedidosVendaCadastrar,
            PermissoesStatic.VendasPedidosVendaVerLista);
        AddDependencia(PermissoesStatic.VendasPedidosVendaEditarDetalhes,
            PermissoesStatic.VendasPedidosVendaVerDetalhes);
        AddDependencia(PermissoesStatic.VendasPedidosVendaAprovar,
            PermissoesStatic.VendasPedidosVendaVerDetalhes);
        AddDependencia(PermissoesStatic.VendasPedidosVendaReprovar,
            PermissoesStatic.VendasPedidosVendaVerDetalhes);
        AddDependencia(PermissoesStatic.VendasPedidosVendaCancelar,
            PermissoesStatic.VendasPedidosVendaVerDetalhes);
        AddDependencia(PermissoesStatic.VendasPedidosVendaEstornar,
            PermissoesStatic.VendasPedidosVendaVerDetalhes);
    }

    public static IEnumerable<PermissaoDependenciaDto> ValidarPermissoesRequeridasParaAtivar(Permissao permissao,
        IEnumerable<Permissao> permissoesAtivas)
    {
        var dependencias = GetDependencias(Requer, new List<Permissao>(), permissao);

        if (dependencias.Count == 0)
            return null;

        if (permissoesAtivas.Count(permissao => dependencias.Any(d => d.Id == permissao.Id)) ==
            dependencias.Count)
            return null;

        return dependencias.Select(dependencia => MappingDependencia(dependencia, permissoesAtivas));
    }

    public static IEnumerable<PermissaoDependenciaDto> ValidarPermissoesRequeridasParaInativar(Permissao permissao,
        IEnumerable<Permissao> permissoesAtivas)
    {
        var dependencias = GetDependencias(Requerido, new List<Permissao>(), permissao);

        if (dependencias.Count == 0)
            return null;

        return permissoesAtivas.Any(permissao => dependencias.Any(d => d.Id == permissao.Id))
            ? dependencias.Select(dependencia => MappingDependencia(dependencia, permissoesAtivas))
            : null;
    }

    public static IEnumerable<PermissaoDependenciaDto> MappingDependenciasGrupo(
        IEnumerable<PermissaoDependenciaDto> dependencias)
    {
        return dependencias.Select(x => new PermissaoDependenciaDto
        {
            Ativo = x.Ativo,
            Modulo = new PermissaoDependenciaDto.ModuloDto
            {
                CorDeFundo = x.Modulo.CorDeFundo,
                Icone = x.Modulo.Icone,
                Id = x.Modulo.Id,
                Nome = x.Modulo.Nome
            },
            Nome = x.Nome,
            PermissaoId = x.PermissaoId,
            SubModulo = new PermissaoDependenciaDto.SubModuloDto
            {
                Nome = x.SubModulo.Nome,
                Id = x.SubModulo.Id,
                Icone = x.SubModulo.Icone,
                CorDeFundo = x.SubModulo.CorDeFundo
            }
        });
    }

    public static IEnumerable<PermissaoDependenciaDto> PreencherDependencias(Permissao permissao, bool ativo,
        bool herdado, IEnumerable<PermissaoUsuario> permissoes)
    {
        var dependencias = GetDependencias(ativo && !herdado ? Requer : Requerido, new List<Permissao>(), permissao);

        return dependencias.Select(x => new PermissaoDependenciaDto
        {
            Nome = x.Nome,
            PermissaoId = x.Id,
            Ativo = permissoes.SingleOrDefault(p => p.PermissaoId == x.Id)?.Ativo == true
        });
    }

    public static IEnumerable<PermissaoDependenciaDto> PreencherDependenciasGrupos(Permissao permissao, bool ativo,
        IEnumerable<GrupoPermissao> permissoes)
    {
        var dependencias = GetDependencias(ativo ? Requer : Requerido, new List<Permissao>(), permissao);

        return dependencias.Select(x => new PermissaoDependenciaDto
        {
            Nome = x.Nome,
            PermissaoId = x.Id,
            Ativo = permissoes.SingleOrDefault(p => p.PermissaoId == x.Id) is not null
        });
    }

    public static bool VerificarRequerRequerido(bool ativo, IEnumerable<PermissaoDependenciaDto> dependencias)
    {
        var possuiDependenciasInativas = ativo && dependencias.Any(x => !x.Ativo);
        var outrasDependem = !ativo && dependencias.Any(x => x.Ativo);

        return possuiDependenciasInativas || outrasDependem;
    }

    private static void AddDependencia(Permissao permissaoPrincipal, Permissao permissaoDependente)
    {
        TryAddDependencia(Requer, permissaoPrincipal, permissaoDependente);
        TryAddDependencia(Requerido, permissaoDependente, permissaoPrincipal);
    }

    private static void TryAddDependencia(Dictionary<Permissao, ICollection<Permissao>> dict,
        Permissao permissao, Permissao dependencia)
    {
        if (!dict.TryGetValue(permissao, out var dependencias))
        {
            dependencias = new List<Permissao>();
            dict.Add(permissao, dependencias);
        }

        if (!dependencias.Contains(dependencia)) dependencias.Add(dependencia);
    }

    private static PermissaoDependenciaDto MappingDependencia(Permissao dependencia,
        IEnumerable<Permissao> permissoesAtivas)
    {
        var modulo = GetModuloPorPermissao(dependencia);
        var subModulo = modulo.SubModulos.Single(x => x.Permissoes.Any(p => p.Id == dependencia.Id));

        return new PermissaoDependenciaDto
        {
            PermissaoId = dependencia.Id,
            Nome = dependencia.Nome,
            Ativo = permissoesAtivas.Any(x => x.Id == dependencia.Id),
            Modulo = new PermissaoDependenciaDto.ModuloDto
            {
                CorDeFundo = modulo.CorDeFundo,
                Icone = modulo.Icone,
                Id = modulo.Id,
                Nome = modulo.Nome
            },
            SubModulo = new PermissaoDependenciaDto.SubModuloDto
            {
                CorDeFundo = subModulo.CorDeFundo,
                Icone = subModulo.Icone,
                Id = subModulo.Id,
                Nome = subModulo.Nome
            }
        };
    }

    private static Modulo GetModuloPorPermissao(Permissao permissao)
    {
        var modulos = ModulosStatic.GetModulos();

        var modulo = modulos.SingleOrDefault(x =>
            x.SubModulos.Any(s => s.Permissoes.Any(p => p.Id == permissao.Id)));

        return modulo;
    }

    private static ICollection<Permissao> GetDependencias(IDictionary<Permissao, ICollection<Permissao>> dict,
        ICollection<Permissao> permissoes, Permissao permissao)
    {
        if (!dict.TryGetValue(permissao, out var dependencias))
            return permissoes;

        foreach (var dependencia in dependencias)
        {
            if (permissoes.Contains(dependencia))
                continue;

            permissoes.Add(dependencia);

            GetDependencias(dict, permissoes, dependencia);
        }

        return permissoes;
    }
}