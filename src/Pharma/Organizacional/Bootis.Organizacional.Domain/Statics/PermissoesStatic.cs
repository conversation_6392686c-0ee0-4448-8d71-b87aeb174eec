using System.Reflection;
using System.Runtime.Serialization;
using Bootis.Organizacional.Domain.AggregatesModel.PermissaoAggregate;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Extensions;

namespace Bootis.Organizacional.Domain.Statics;

public static class PermissoesStatic
{
    private static IEnumerable<Permissao> _permissoes;

    #region Compras_NecessidadeCompras

    public static readonly Permissao ComprasNecessidadeComprasVerLista =
        new()
        {
            Id = Permissoes.Compras_NecessidadeCompras_VerLista.ToInt(),
            Nome = "Ver lista necessidade de compras"
        };

    #endregion Compras_NecessidadeCompras

    public static IEnumerable<Permissao> GetPermissoes()
    {
        if (_permissoes != null)
            return _permissoes;

        var type = typeof(PermissoesStatic);

        return
            _permissoes = type
                .GetFields()
                .Where(x => x.GetCustomAttribute<IgnoreDataMemberAttribute>() == null)
                .Select(c => (Permissao)c.GetValue(null));
    }

    public static IEnumerable<FieldInfo> GetPermissoesFields()
    {
        var type = typeof(PermissoesStatic);

        return type.GetFields()
            .Where(x => x.GetCustomAttribute<IgnoreDataMemberAttribute>() == null);
    }

    public static Permissao GetPermissao(int id)
    {
        return GetPermissoes().FirstOrDefault(f => f.Id == id);
    }

    #region Producao_ReceitaManipulada

    public static readonly Permissao ProducaoReceitaManipuladaVerLista =
        new()
        {
            Id = Permissoes.Producao_ReceitaManipulada_VerLista.ToInt(),
            Nome = "Ver lista das receitas"
        };

    public static readonly Permissao ProducaoReceitaManipuladaVerDetalhes =
        new()
        {
            Id = Permissoes.Producao_ReceitaManipulada_VerDetalhes.ToInt(),
            Nome = "Ver detalhes das receitas"
        };

    public static readonly Permissao ProducaoReceitaManipuladaCadastrar =
        new()
        {
            Id = Permissoes.Producao_ReceitaManipulada_Cadastrar.ToInt(),
            Nome = "Cadastrar receitas"
        };

    public static readonly Permissao ProducaoReceitaManipuladaEditarDetalhes =
        new()
        {
            Id = Permissoes.Producao_ReceitaManipulada_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes das receitas"
        };

    public static readonly Permissao ProducaoReceitaManipuladaAlterarStatus =
        new()
        {
            Id = Permissoes.Producao_ReceitaManipulada_AlterarStatus.ToInt(),
            Nome = "Alterar status das receitas"
        };

    public static readonly Permissao ProducaoReceitaManipuladaExcluir =
        new()
        {
            Id = Permissoes.Producao_ReceitaManipulada_Excluir.ToInt(),
            Nome = "Excluir receitas"
        };

    public static readonly Permissao ProducaoReceitaManipuladaCancelar =
        new()
        {
            Id = Permissoes.Producao_ReceitaManipulada_Cancelar.ToInt(),
            Nome = "Cancelar receitas"
        };

    public static readonly Permissao ProducaoIncompatibilidadeLiberar =
        new()
        {
            Id = Permissoes.Producao_Incompatibilidade_Liberar.ToInt(),
            Nome = "Liberar incompatibilidades de receitas"
        };

    #endregion

    #region Admin_SetupTenant

    //Permissões de admin são ignoradas na listagem, para impedir que usuarios comuns consigam ter acesso.

    [IgnoreDataMember] public static readonly Permissao AdminSetupTenantVerIdiomas =
        new()
        {
            Id = Permissoes.Admin_SetupTenant_VerIdiomas.ToInt(),
            Nome = "Ver idiomas disponiveis para uso"
        };

    [IgnoreDataMember] public static readonly Permissao AdminSetupTenantExecutar =
        new()
        {
            Id = Permissoes.Admin_SetupTenant_Executar.ToInt(),
            Nome = "Executa a operação de carga inicial do tenant."
        };

    #endregion

    #region Administrativo_Empresa

    public static readonly Permissao AdministrativoEmpresaVerLista =
        new()
        {
            Id = Permissoes.Administrativo_Empresa_VerLista.ToInt(),
            Nome = "Ver lista de empresas"
        };

    public static readonly Permissao AdministrativoEmpresaVerDetalhes =
        new()
        {
            Id = Permissoes.Administrativo_Empresa_VerDetalhes.ToInt(),
            Nome = "Ver detalhes das empresas"
        };

    public static readonly Permissao AdministrativoEmpresaEditarDados =
        new()
        {
            Id = Permissoes.Administrativo_Empresa_EditarDados.ToInt(),
            Nome = "Editar dados da empresa"
        };

    public static readonly Permissao AdministrativoEmpresaEditarEndereco =
        new()
        {
            Id = Permissoes.Administrativo_Empresa_EditarEndereco.ToInt(),
            Nome = "Editar endereço da empresa"
        };

    public static readonly Permissao AdministrativoEmpresaEditarUsuarioResponsavel =
        new()
        {
            Id = Permissoes.Administrativo_Empresa_EditarUsuarioResponsavel.ToInt(),
            Nome = "Editar usuário responsável da empresa"
        };

    public static readonly Permissao AdministrativoEmpresaEditarStatus =
        new()
        {
            Id = Permissoes.Administrativo_Empresa_AlterarStatus.ToInt(),
            Nome = "Alterar status da empresa"
        };

    [IgnoreDataMember] public static readonly Permissao AdministrativoManager =
        new()
        {
            Id = Permissoes.Admin_Manager.ToInt(),
            Nome = "Acesso as áreas Admin Bootis"
        };

    #endregion Administrativo_Empresa

    #region Administrativo_GrupoUsuarios

    public static readonly Permissao AdministrativoGrupoUsuariosVerLista =
        new()
        {
            Id = Permissoes.Administrativo_GrupoUsuarios_VerLista.ToInt(),
            Nome = "Ver lista dos grupos de usuários"
        };

    public static readonly Permissao AdministrativoGrupoUsuariosVerDetalhes =
        new()
        {
            Id = Permissoes.Administrativo_GrupoUsuarios_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos grupos de usuário"
        };

    public static readonly Permissao AdministrativoGrupoUsuariosCriar =
        new()
        {
            Id = Permissoes.Administrativo_GrupoUsuarios_Criar.ToInt(),
            Nome = "Criar grupos de usuários"
        };

    public static readonly Permissao AdministrativoGrupoUsuariosEditarDetalhes =
        new()
        {
            Id = Permissoes.Administrativo_GrupoUsuarios_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos grupos de usuários"
        };

    public static readonly Permissao AdministrativoGrupoUsuariosAlterarStatus =
        new()
        {
            Id = Permissoes.Administrativo_GrupoUsuarios_AlterarStatus.ToInt(),
            Nome = "Alterar status dos grupos de usuários"
        };

    public static readonly Permissao AdministrativoGrupoUsuariosEditarPermissao =
        new()
        {
            Id = Permissoes.Administrativo_GrupoUsuarios_EditarPermissao.ToInt(),
            Nome = "Editar permissões dos grupos de usuários"
        };

    public static readonly Permissao AdministrativoGrupoUsuariosExcluir =
        new()
        {
            Id = Permissoes.Administrativo_GrupoUsuarios_Excluir.ToInt(),
            Nome = "Excluir grupos de usuários"
        };

    #endregion Administrativo_GrupoUsuarios

    #region Administrativo_Usuario

    public static readonly Permissao AdministrativoUsuarioVerLista =
        new()
        {
            Id = Permissoes.Administrativo_Usuarios_VerLista.ToInt(),
            Nome = "Ver lista de usuários"
        };

    public static readonly Permissao AdministrativoUsuariosVerDetalhes =
        new()
        {
            Id = Permissoes.Administrativo_Usuarios_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos usuários"
        };

    public static readonly Permissao AdministrativoUsuariosCadastrar =
        new()
        {
            Id = Permissoes.Administrativo_Usuarios_Cadastrar.ToInt(),
            Nome = "Cadastrar novos usuários"
        };

    public static readonly Permissao AdministrativoUsuarioEditarDadosPessoais =
        new()
        {
            Id = Permissoes.Administrativo_Usuarios_EditarDadosPessoais.ToInt(),
            Nome = "Editar dados pessoais dos usuários"
        };

    public static readonly Permissao AdministrativoUsuarioAlterarStatus =
        new()
        {
            Id = Permissoes.Administrativo_Usuarios_AlterarStatus.ToInt(),
            Nome = "Alterar status dos usuários"
        };

    public static readonly Permissao AdministrativoUsuarioEditarGrupo =
        new()
        {
            Id = Permissoes.Administrativo_Usuarios_EditarGrupo.ToInt(),
            Nome = "Editar grupos dos usuários"
        };

    public static readonly Permissao AdministrativoUsuarioEditarPermissao =
        new()
        {
            Id = Permissoes.Administrativo_Usuarios_EditarPermissao.ToInt(),
            Nome = "Editar permissões dos usuários"
        };

    public static readonly Permissao AdministrativoUsuarioExcluir =
        new()
        {
            Id = Permissoes.Administrativo_Usuarios_Excluir.ToInt(),
            Nome = "Excluir usuários"
        };

    #endregion Administrativo_Usuario

    #region Compras_Fornecedores

    public static readonly Permissao ComprasFornecedoresVerLista =
        new()
        {
            Id = Permissoes.Compras_Fornecedores_VerLista.ToInt(),
            Nome = "Ver lista de fornecedores"
        };

    public static readonly Permissao ComprasFornecedoresVerDetalhes =
        new()
        {
            Id = Permissoes.Compras_Fornecedores_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos fornecedores"
        };

    public static readonly Permissao ComprasFornecedoresCadastrar =
        new()
        {
            Id = Permissoes.Compras_Fornecedores_Cadastrar.ToInt(),
            Nome = "Cadastrar novos fornecedores"
        };

    public static readonly Permissao ComprasFornecedoresEditarDetalhes =
        new()
        {
            Id = Permissoes.Compras_Fornecedores_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos fornecedores"
        };

    public static readonly Permissao ComprasFornecedoresEditarContatos =
        new()
        {
            Id = Permissoes.Compras_Fornecedores_EditarContatos.ToInt(),
            Nome = "Editar contatos dos fornecedores"
        };

    public static readonly Permissao ComprasFornecedoresEditarEnderecos =
        new()
        {
            Id = Permissoes.Compras_Fornecedores_EditarEnderecos.ToInt(),
            Nome = "Editar endereços dos fornecedores"
        };

    public static readonly Permissao ComprasFornecedoresEditarDocumentos =
        new()
        {
            Id = Permissoes.Compras_Fornecedores_EditarDocumentos.ToInt(),
            Nome = "Editar documentos dos fornecedores"
        };

    public static readonly Permissao ComprasFornecedoresAlterarStatus =
        new()
        {
            Id = Permissoes.Compras_Fornecedores_AlterarStatus.ToInt(),
            Nome = "Alterar status dos fornecedores"
        };

    public static readonly Permissao ComprasFornecedoresExcluir =
        new()
        {
            Id = Permissoes.Compras_Fornecedores_Excluir.ToInt(),
            Nome = "Excluir fornecedores"
        };

    #endregion Compras_Fornecedores

    #region Compras_NotaFiscalEntrada

    public static readonly Permissao ComprasNotaFiscalEntradaVerLista =
        new()
        {
            Id = Permissoes.Compras_NotaFiscalEntrada_VerLista.ToInt(),
            Nome = "Ver lista das notas fiscais de entrada"
        };

    public static readonly Permissao ComprasNotaFiscalEntradaVerDetalhes =
        new()
        {
            Id = Permissoes.Compras_NotaFiscalEntrada_VerDetalhes.ToInt(),
            Nome = "Ver detalhes das notas fiscais de entrada"
        };

    public static readonly Permissao ComprasNotaFiscalEntradaCadastrar =
        new()
        {
            Id = Permissoes.Compras_NotaFiscalEntrada_Cadastrar.ToInt(),
            Nome = "Cadastrar novas notas fiscais de entrada"
        };

    public static readonly Permissao ComprasNotaFiscalEntradaEditarDetalhes =
        new()
        {
            Id = Permissoes.Compras_NotaFiscalEntrada_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes das notas fiscais de entrada"
        };

    public static readonly Permissao ComprasNotaFiscalEntradaAlterarStatus =
        new()
        {
            Id = Permissoes.Compras_NotaFiscalEntrada_AlterarStatus.ToInt(),
            Nome = "Alterar status das notas ficais de entrada"
        };

    public static readonly Permissao ComprasNotaFiscalEntradaExcluir =
        new()
        {
            Id = Permissoes.Compras_NotaFiscalEntrada_Excluir.ToInt(),
            Nome = "Excluir notas fiscais de entrada"
        };

    public static readonly Permissao ComprasNotaFiscalEntradaLancarLotes =
        new()
        {
            Id = Permissoes.Compras_NotaFiscalEntrada_LancarLotes.ToInt(),
            Nome = "Lançar lotes das notas fiscais de entrada"
        };

    #endregion Compras_PedidoCompra

    #region Compras_PedidoCompra

    public static readonly Permissao ComprasPedidoCompraVerLista =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_VerLista.ToInt(),
            Nome = "Ver lista dos pedidos de compra"
        };

    public static readonly Permissao ComprasPedidoCompraVerDetalhes =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos pedidos de compra"
        };

    public static readonly Permissao ComprasPedidoCompraCadastrar =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_Cadastrar.ToInt(),
            Nome = "Cadastrar novos pedidos de compra"
        };

    public static readonly Permissao ComprasPedidoCompraEditarDetalhes =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos pedidos de compra"
        };

    public static readonly Permissao ComprasPedidoCompraAprovar =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_Aprovar.ToInt(),
            Nome = "Aprovar pedidos de compra"
        };

    public static readonly Permissao ComprasPedidoCompraReprovar =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_Reprovar.ToInt(),
            Nome = "Reprovar pedidos de compra"
        };

    public static readonly Permissao ComprasPedidoCompraCancelar =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_Cancelar.ToInt(),
            Nome = "Cancelar pedidos de compra"
        };

    public static readonly Permissao ComprasPedidoCompraConfirmarComFornecedor =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_ConfirmarComFornecedor.ToInt(),
            Nome = "Confirmar pedidos de compra com o fornecedor"
        };

    public static readonly Permissao ComprasPedidoCompraEstornarLiberado =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_EstornarLiberado.ToInt(),
            Nome = "Estornar pedidos de compra aprovados"
        };

    public static readonly Permissao ComprasPedidoCompraEstornarConfirmadoComFornecedor =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_EstornarConfirmadoComFornecedor.ToInt(),
            Nome = "Estornar pedidos de compra confirmados com o fornecedor"
        };

    public static readonly Permissao ComprasPedidoCompraExcluir =
        new()
        {
            Id = Permissoes.Compras_PedidoCompra_Excluir.ToInt(),
            Nome = "Excluir pedidos de compra"
        };

    #endregion Compras_PedidoCompra

    #region Estoque_AjusteSaldo

    public static readonly Permissao EstoqueAjusteSaldoVerLista =
        new()
        {
            Id = Permissoes.Estoque_AjusteSaldo_VerLista.ToInt(),
            Nome = "Ver lista de ajustes de saldo"
        };

    public static readonly Permissao EstoqueAjusteSaldoVerDetalhes =
        new()
        {
            Id = Permissoes.Estoque_AjusteSaldo_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos ajustes de saldo"
        };

    public static readonly Permissao EstoqueAjusteSaldoCadastrar =
        new()
        {
            Id = Permissoes.Estoque_AjusteSaldo_Cadastrar.ToInt(),
            Nome = "Cadastrar novos ajustes de saldo"
        };

    #endregion Estoque_AjusteSaldo

    #region Estoque_Lotes

    public static readonly Permissao EstoqueLotesVerLista =
        new()
        {
            Id = Permissoes.Estoque_Lotes_VerLista.ToInt(),
            Nome = "Ver lista de empresas"
        };

    public static readonly Permissao EstoqueLotesVerDetalhes =
        new()
        {
            Id = Permissoes.Estoque_Lotes_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos lotes"
        };

    public static readonly Permissao EstoqueLotesCadastrar =
        new()
        {
            Id = Permissoes.Estoque_Lotes_Cadastrar.ToInt(),
            Nome = "Cadastrar novos lotes"
        };

    public static readonly Permissao EstoqueLotesEditarInformacoes =
        new()
        {
            Id = Permissoes.Estoque_Lotes_EditarInformacoes.ToInt(),
            Nome = "Editar informações dos lotes"
        };

    public static readonly Permissao EstoqueLotesAlterarStatus =
        new()
        {
            Id = Permissoes.Estoque_Lotes_AlterarStatus.ToInt(),
            Nome = "Alterar status dos lotes"
        };

    public static readonly Permissao EstoqueLotesExcluir =
        new()
        {
            Id = Permissoes.Estoque_Lotes_Excluir.ToInt(),
            Nome = "Excluir lotes"
        };

    #endregion Estoque_Lotes

    #region Estoque_LocaisEstoque

    public static readonly Permissao EstoqueLocaisEstoqueVerLista =
        new()
        {
            Id = Permissoes.Estoque_LocaisEstoque_VerLista.ToInt(),
            Nome = "Ver lista de locais de estoque"
        };

    public static readonly Permissao EstoqueLocaisEstoqueVerDetalhes =
        new()
        {
            Id = Permissoes.Estoque_LocaisEstoque_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos locais de estoque"
        };

    public static readonly Permissao EstoqueLocaisEstoqueCadastrar =
        new()
        {
            Id = Permissoes.Estoque_LocaisEstoque_Cadastrar.ToInt(),
            Nome = "Cadastrar novos locais de estoque"
        };

    public static readonly Permissao EstoqueLocaisEstoqueEditarDetalhes =
        new()
        {
            Id = Permissoes.Estoque_LocaisEstoque_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos locais de estoque"
        };

    public static readonly Permissao EstoqueLocaisEstoqueAlterarStatus =
        new()
        {
            Id = Permissoes.Estoque_LocaisEstoque_AlterarStatus.ToInt(),
            Nome = "Alterar status dos locais de estoque"
        };

    public static readonly Permissao EstoqueLocaisEstoqueExcluir =
        new()
        {
            Id = Permissoes.Estoque_LocaisEstoque_Excluir.ToInt(),
            Nome = "Excluir locais de estoque"
        };

    #endregion Estoque_LocaisEstoque

    #region Estoque_Perdas

    public static readonly Permissao EstoquePerdasVerLista =
        new()
        {
            Id = Permissoes.Estoque_Perdas_VerLista.ToInt(),
            Nome = "Ver lista de perdas"
        };

    public static readonly Permissao EstoquePerdasVerDetalhes =
        new()
        {
            Id = Permissoes.Estoque_Perdas_VerDetalhes.ToInt(),
            Nome = "Ver detalhes das perdas"
        };

    public static readonly Permissao EstoquePerdasCadastrar =
        new()
        {
            Id = Permissoes.Estoque_Perdas_Cadastrar.ToInt(),
            Nome = "Cadastrar novas perdas"
        };

    public static readonly Permissao EstoquePerdasEditarDetalhes =
        new()
        {
            Id = Permissoes.Estoque_Perdas_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes da perda"
        };

    #endregion Estoque_Perdas

    #region Estoque_Grupos

    public static readonly Permissao EstoqueGruposVerLista =
        new()
        {
            Id = Permissoes.Estoque_Grupos_VerLista.ToInt(),
            Nome = "Ver lista de grupos de produtos"
        };

    public static readonly Permissao EstoqueGruposVerDetalhes =
        new()
        {
            Id = Permissoes.Estoque_Grupos_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos grupos de produtos"
        };

    public static readonly Permissao EstoqueGruposCadastrar =
        new()
        {
            Id = Permissoes.Estoque_Grupos_Cadastrar.ToInt(),
            Nome = "Cadastrar novos grupos de produtos"
        };

    public static readonly Permissao EstoqueGruposEditarDetalhes =
        new()
        {
            Id = Permissoes.Estoque_Grupos_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos grupos de produtos"
        };

    public static readonly Permissao EstoqueGruposExcluir =
        new()
        {
            Id = Permissoes.Estoque_Grupos_Excluir.ToInt(),
            Nome = "Excluir grupos de produtos"
        };

    #endregion Estoque_Grupos

    #region Estoque_SubGrupos

    public static readonly Permissao EstoqueSubGruposVerLista =
        new()
        {
            Id = Permissoes.Estoque_SubGrupos_VerLista.ToInt(),
            Nome = "Ver lista de subgrupos de produtos"
        };

    public static readonly Permissao EstoqueSubGruposVerDetalhes =
        new()
        {
            Id = Permissoes.Estoque_SubGrupos_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos subgrupos de produtos"
        };

    public static readonly Permissao EstoqueSubGruposCadastrar =
        new()
        {
            Id = Permissoes.Estoque_SubGrupos_Cadastrar.ToInt(),
            Nome = "Cadastrar novos subgrupos de produtos"
        };

    public static readonly Permissao EstoqueSubGruposEditarDetalhes =
        new()
        {
            Id = Permissoes.Estoque_SubGrupos_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos subgrupos de produtos"
        };

    public static readonly Permissao EstoqueSubGruposExcluir =
        new()
        {
            Id = Permissoes.Estoque_SubGrupos_Excluir.ToInt(),
            Nome = "Excluir subgrupos de produtos"
        };

    #endregion Produtos_SubGrupos

    #region Estoque_Produtos

    public static readonly Permissao EstoqueProdutosVerLista =
        new()
        {
            Id = Permissoes.Estoque_Produtos_VerLista.ToInt(),
            Nome = "Ver lista de produtos"
        };

    public static readonly Permissao EstoqueProdutosVerDetalhes =
        new()
        {
            Id = Permissoes.Estoque_Produtos_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos produtos"
        };

    public static readonly Permissao EstoqueProdutosCadastrar =
        new()
        {
            Id = Permissoes.Estoque_Produtos_Cadastrar.ToInt(),
            Nome = "Cadastrar novos produtos"
        };

    public static readonly Permissao EstoqueProdutosEditarInformacoes =
        new()
        {
            Id = Permissoes.Estoque_Produtos_EditarInformacoes.ToInt(),
            Nome = "Editar informações dos produtos"
        };

    public static readonly Permissao EstoqueProdutosEditarInformacoesFinanceiras =
        new()
        {
            Id = Permissoes.Estoque_Produtos_EditarInformacoesFinanceiras.ToInt(),
            Nome = "Editar informações financeiras dos produtos"
        };

    public static readonly Permissao EstoqueProdutosAlterarStatus =
        new()
        {
            Id = Permissoes.Estoque_Produtos_AlterarStatus.ToInt(),
            Nome = "Alterar status dos produtos"
        };

    public static readonly Permissao EstoqueProdutosExcluir =
        new()
        {
            Id = Permissoes.Estoque_Produtos_Excluir.ToInt(),
            Nome = "Excluir produtos"
        };

    public static readonly Permissao EstoqueInformacoesTecnicasVisualizar =
        new()
        {
            Id = Permissoes.Estoque_InformacoesTecnicas_Visualizar.ToInt(),
            Nome = "Visualizar informações técnicas"
        };

    public static readonly Permissao EstoqueProdutosAssociadoVisualizar =
        new()
        {
            Id = Permissoes.Estoque_ProdutosAssociado_Visualizar.ToInt(),
            Nome = "Visualizar produtos associado"
        };

    public static readonly Permissao EstoqueProdutosAssociadoCadastrar =
        new()
        {
            Id = Permissoes.Estoque_ProdutosAssociado_Cadastrar.ToInt(),
            Nome = "Cadastrar novos produtos associado"
        };

    public static readonly Permissao EstoqueProdutosAssociadoEditarDetalhes =
        new()
        {
            Id = Permissoes.Estoque_ProdutosAssociado_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos produtos associado"
        };

    public static readonly Permissao EstoqueProdutosAssociadoExcluir =
        new()
        {
            Id = Permissoes.Estoque_ProdutosAssociado_Excluir.ToInt(),
            Nome = "Excluir produtos associado"
        };

    public static readonly Permissao EstoqueProdutosDiluidoVisualizar =
        new()
        {
            Id = Permissoes.Estoque_ProdutosDiluido_Visualizar.ToInt(),
            Nome = "Visualizar produtos diluído"
        };

    public static readonly Permissao EstoqueProdutosDiluidoCadastrar =
        new()
        {
            Id = Permissoes.Estoque_ProdutosDiluido_Cadastrar.ToInt(),
            Nome = "Cadastrar novos produtos diluído"
        };

    public static readonly Permissao EstoqueProdutosDiluidoEditarDetalhes =
        new()
        {
            Id = Permissoes.Estoque_ProdutosDiluido_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos produtos diluído"
        };

    public static readonly Permissao EstoqueProdutosDiluidoExcluir =
        new()
        {
            Id = Permissoes.Estoque_ProdutosDiluido_Excluir.ToInt(),
            Nome = "Excluir produtos diluído"
        };

    public static readonly Permissao EstoqueProdutosIncompativelVisualizar =
        new()
        {
            Id = Permissoes.Estoque_ProdutosIncompativel_Visualizar.ToInt(),
            Nome = "Visualizar produtos incompatível"
        };

    public static readonly Permissao EstoqueProdutosIncompativelCadastrar =
        new()
        {
            Id = Permissoes.Estoque_ProdutosIncompativel_Cadastrar.ToInt(),
            Nome = "Cadastrar novos produtos incompatível"
        };

    public static readonly Permissao EstoqueProdutosIncompativelEditarDetalhes =
        new()
        {
            Id = Permissoes.Estoque_ProdutosIncompativel_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos produtos incompatível"
        };

    public static readonly Permissao EstoqueProdutosIncompativelExcluir =
        new()
        {
            Id = Permissoes.Estoque_ProdutosIncompativel_Excluir.ToInt(),
            Nome = "Excluir produtos incompatível"
        };

    public static readonly Permissao EstoqueProdutosSinonimoVisualizar =
        new()
        {
            Id = Permissoes.Estoque_ProdutosSinonimo_Visualizar.ToInt(),
            Nome = "Visualizar produtos sinônimo"
        };

    public static readonly Permissao EstoqueProdutosSinonimoCadastrar =
        new()
        {
            Id = Permissoes.Estoque_ProdutosSinonimo_Cadastrar.ToInt(),
            Nome = "Cadastrar novos produtos sinônimo"
        };

    public static readonly Permissao EstoqueProdutosSinonimoEditarDetalhes =
        new()
        {
            Id = Permissoes.Estoque_ProdutosSinonimo_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos produtos sinônimo"
        };

    public static readonly Permissao EstoqueProdutosSinonimoExcluir =
        new()
        {
            Id = Permissoes.Estoque_ProdutosSinonimo_Excluir.ToInt(),
            Nome = "Excluir produtos sinônimo"
        };

    public static readonly Permissao EstoqueProdutosMensagemVisualizar =
        new()
        {
            Id = Permissoes.Estoque_ProdutosMensagem_Visualizar.ToInt(),
            Nome = "Visualizar mensagens do produto"
        };

    public static readonly Permissao EstoqueProdutosMensagemCadastrar =
        new()
        {
            Id = Permissoes.Estoque_ProdutosMensagem_Cadastrar.ToInt(),
            Nome = "Vincular mensagens ao produto"
        };

    public static readonly Permissao EstoqueProdutosMensagemExcluir =
        new()
        {
            Id = Permissoes.Estoque_ProdutosMensagem_Excluir.ToInt(),
            Nome = "Excluir mensagens do produto"
        };

    #endregion Estoque_Produtos

    #region Estoque_MensagemProduto

    public static readonly Permissao EstoqueMensagemProdutoVerLista =
        new()
        {
            Id = Permissoes.Estoque_MensagemProduto_VerLista.ToInt(),
            Nome = "Ver lista das mensagens do produto"
        };

    public static readonly Permissao EstoqueMensagemProdutoVerDetalhes =
        new()
        {
            Id = Permissoes.Estoque_MensagemProduto_VerDetalhes.ToInt(),
            Nome = "Ver detalhes das mensagens do produto"
        };

    public static readonly Permissao EstoqueMensagemProdutoCadastrar =
        new()
        {
            Id = Permissoes.Estoque_MensagemProduto_Cadastrar.ToInt(),
            Nome = "Cadastrar novas mensagens do produto"
        };

    public static readonly Permissao EstoqueMensagemProdutoEditarDetalhes =
        new()
        {
            Id = Permissoes.Estoque_MensagemProduto_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes das mensagens do produto"
        };

    public static readonly Permissao EstoqueMensagemProdutoRemoverProduto =
        new()
        {
            Id = Permissoes.Estoque_MensagemProduto_RemoverProduto.ToInt(),
            Nome = "Remover produto de uma mensagem do produto"
        };

    public static readonly Permissao EstoqueMensagemProdutoExcluir =
        new()
        {
            Id = Permissoes.Estoque_MensagemProduto_Excluir.ToInt(),
            Nome = "Excluir mensagens do produto"
        };

    #endregion Estoque_MensagemProduto

    #region Estoque_Transferencias

    public static readonly Permissao EstoqueTransferenciasVerLista =
        new()
        {
            Id = Permissoes.Estoque_Transferencias_VerLista.ToInt(),
            Nome = "Ver lista de transferências"
        };

    public static readonly Permissao EstoqueTransferenciasVerDetalhes =
        new()
        {
            Id = Permissoes.Estoque_Transferencias_VerDetalhess.ToInt(),
            Nome = "Ver detalhes das transferências"
        };

    public static readonly Permissao EstoqueTransferenciasCadastrar =
        new()
        {
            Id = Permissoes.Estoque_Transferencias_Cadastrar.ToInt(),
            Nome = "Cadastrar novas transferências"
        };

    #endregion Estoque_Transferencias

    #region Estoque_ProdutoFichaTecnica

    public static readonly Permissao EstoqueProdutosFichaTecnicaEditar =
       new()
       {
           Id = Permissoes.Estoque_ProdutosFichaTecnica_Editar.ToInt(),
           Nome = "Editar ficha técnica do produto"
       };

    public static readonly Permissao EstoqueProdutosFichaTecnicaVisualizar =
       new()
       {
           Id = Permissoes.Estoque_ProdutosFichaTecnica_Visualizar.ToInt(),
           Nome = "Visualizar ficha técnica do produto"
       };

    #endregion

    #region Estoque_Inventarios

    public static readonly Permissao EstoqueInventarioVerLista =
        new()
        {
            Id = Permissoes.Estoque_Inventario_VerLista.ToInt(),
            Nome = "Ver lista de inventários"
        };

    public static readonly Permissao EstoqueInventarioVerDetalhes =
        new()
        {
            Id = Permissoes.Estoque_Inventario_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos inventários"
        };

    public static readonly Permissao EstoqueInventarioCadastrar =
        new()
        {
            Id = Permissoes.Estoque_Inventario_Cadastrar.ToInt(),
            Nome = "Cadastrar novos inventários"
        };

    public static readonly Permissao EstoqueInventarioVerDetalhesConferencia =
        new()
        {
            Id = Permissoes.Estoque_Inventario_VerDetalhesConferencia.ToInt(),
            Nome = "Ver detalhes da conferência"
        };

    public static readonly Permissao EstoqueInventarioExcluir =
        new()
        {
            Id = Permissoes.Estoque_Inventario_Excluir.ToInt(),
            Nome = "Excluir inventários"
        };

    public static readonly Permissao EstoqueRastreabilidadeVisualizar =
        new()
        {
            Id = Permissoes.Estoque_Rastreabilidade_Visualizar.ToInt(),
            Nome = "Visualizar rastreabilidade dos lotes"
        };

    public static readonly Permissao EstoqueMovimentacaoVisualizar =
        new()
        {
            Id = Permissoes.Estoque_Movimentacao_Visualizar.ToInt(),
            Nome = "Visualizar movimentação do estoque"
        };

    public static readonly Permissao EstoqueProjecaoVisualizar =
        new()
        {
            Id = (int)Permissoes.Estoque_Projecao_Visualizar,
            Nome = "Visualizar projeção do estoque"
        };

    #endregion

    #region Producao_CapsulaCor

    public static readonly Permissao ProducaoCapsulaCorVerLista =
        new()
        {
            Id = Permissoes.Producao_CapsulaCor_VerLista.ToInt(),
            Nome = "Ver lista das cores de cápsulas"
        };

    public static readonly Permissao ProducaoCapsulaCorVerDetalhes =
        new()
        {
            Id = Permissoes.Producao_CapsulaCor_VerDetalhes.ToInt(),
            Nome = "Ver detalhes das cores de cápsulas"
        };

    public static readonly Permissao ProducaoCapsulaCorCadastrar =
        new()
        {
            Id = Permissoes.Producao_CapsulaCor_Cadastrar.ToInt(),
            Nome = "Cadastrar novas cores de cápsulas"
        };

    public static readonly Permissao ProducaoCapsulaCorEditarDetalhes =
        new()
        {
            Id = Permissoes.Producao_CapsulaCor_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes das cores de cápsulas"
        };

    public static readonly Permissao ProducaoCapsulaCorExcluir =
        new()
        {
            Id = Permissoes.Producao_CapsulaCor_Excluir.ToInt(),
            Nome = "Excluir cores de cápsulas"
        };

    #endregion Producao_CapsulaCor

    #region Producao_ClassificacaoEmbalagem

    public static readonly Permissao ProducaoClassificacaoEmbalagemVerLista =
        new()
        {
            Id = Permissoes.Producao_ClassificacaoEmbalagem_VerLista.ToInt(),
            Nome = "Ver lista das classificações de embalagem"
        };

    public static readonly Permissao ProducaoClassificacaoEmbalagemVerDetalhes =
        new()
        {
            Id = Permissoes.Producao_ClassificacaoEmbalagem_VerDetalhes.ToInt(),
            Nome = "Ver detalhes das classificações de embalagem"
        };

    public static readonly Permissao ProducaoClassificacaoEmbalagemCadastrar =
        new()
        {
            Id = Permissoes.Producao_ClassificacaoEmbalagem_Cadastrar.ToInt(),
            Nome = "Cadastrar novas classificações de embalagem"
        };

    public static readonly Permissao ProducaoClassificacaoEmbalagemEditarDetalhes =
        new()
        {
            Id = Permissoes.Producao_ClassificacaoEmbalagem_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes das classificações de embalagem"
        };

    public static readonly Permissao ProducaoClassificacaoEmbalagemAlterarStatus =
        new()
        {
            Id = Permissoes.Producao_ClassificacaoEmbalagem_AlterarStatus.ToInt(),
            Nome = "Alterar status das classificações de embalagem"
        };

    public static readonly Permissao ProducaoClassificacaoEmbalagemExcluir =
        new()
        {
            Id = Permissoes.Producao_ClassificacaoEmbalagem_Excluir.ToInt(),
            Nome = "Excluir classificações de embalagem"
        };

    #endregion Producao_ClassificacaoEmbalagem

    #region Producao_FormaFarmaceutica

    public static readonly Permissao ProducaoFormaFarmaceuticaVerLista =
        new()
        {
            Id = Permissoes.Producao_FormaFarmaceutica_VerLista.ToInt(),
            Nome = "Ver lista das formas farmacêuticas"
        };

    public static readonly Permissao ProducaoFormaFarmaceuticaVerDetalhes =
        new()
        {
            Id = Permissoes.Producao_FormaFarmaceutica_VerDetalhes.ToInt(),
            Nome = "Ver detalhes das formas farmacêuticas"
        };

    public static readonly Permissao ProducaoFormaFarmaceuticaCadastrar =
        new()
        {
            Id = Permissoes.Producao_FormaFarmaceutica_Cadastrar.ToInt(),
            Nome = "Cadastrar novas formas farmacêuticas"
        };

    public static readonly Permissao ProducaoFormaFarmaceuticaEditarDetalhes =
        new()
        {
            Id = Permissoes.Producao_FormaFarmaceutica_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes das formas farmacêuticas"
        };

    public static readonly Permissao ProducaoFormaFarmaceuticaAlterarStatus =
        new()
        {
            Id = Permissoes.Producao_FormaFarmaceutica_AlterarStatus.ToInt(),
            Nome = "Alterar status das formas farmacêuticas"
        };

    public static readonly Permissao ProducaoFormaFarmaceuticaExcluir =
        new()
        {
            Id = Permissoes.Producao_FormaFarmaceutica_Excluir.ToInt(),
            Nome = "Excluir formas farmacêuticas"
        };

    #endregion Producao_FormaFarmaceutica

    #region Producao_FormulaPadrao

    public static readonly Permissao ProducaoFormulaPadraoVerLista =
        new()
        {
            Id = Permissoes.Producao_FormulaPadrao_VerLista.ToInt(),
            Nome = "Ver lista das fórmulas padrão"
        };

    public static readonly Permissao ProducaoFormulaPadraoVerDetalhes =
        new()
        {
            Id = Permissoes.Producao_FormulaPadrao_VerDetalhes.ToInt(),
            Nome = "Ver detalhes das fórmulas padrão"
        };

    public static readonly Permissao ProducaoFormulaPadraoCadastrar =
        new()
        {
            Id = Permissoes.Producao_FormulaPadrao_Cadastrar.ToInt(),
            Nome = "Cadastrar novas fórmulas padrão"
        };

    public static readonly Permissao ProducaoFormulaPadraoEditarDetalhes =
        new()
        {
            Id = Permissoes.Producao_FormulaPadrao_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes das fórmulas padrão"
        };

    public static readonly Permissao ProducaoFormulaPadraoExcluir =
        new()
        {
            Id = Permissoes.Producao_FormulaPadrao_Excluir.ToInt(),
            Nome = "Excluir fórmulas padrão"
        };

    #endregion Producao_FormulaPadrao

    #region Producao_Laboratorio

    public static readonly Permissao ProducaoLaboratorioVerLista =
        new()
        {
            Id = Permissoes.Producao_Laboratorio_VerLista.ToInt(),
            Nome = "Ver lista dos laboratórios"
        };

    public static readonly Permissao ProducaoLaboratorioVerDetalhes =
        new()
        {
            Id = Permissoes.Producao_Laboratorio_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos laboratórios"
        };

    public static readonly Permissao ProducaoLaboratorioCadastrar =
        new()
        {
            Id = Permissoes.Producao_Laboratorio_Cadastrar.ToInt(),
            Nome = "Cadastrar novos laboratórios"
        };

    public static readonly Permissao ProducaoLaboratorioEditarDetalhes =
        new()
        {
            Id = Permissoes.Producao_Laboratorio_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos laboratórios"
        };

    public static readonly Permissao ProducaoLaboratorioAlterarStatus =
        new()
        {
            Id = Permissoes.Producao_Laboratorio_AlterarStatus.ToInt(),
            Nome = "Alterar status dos laboratórios"
        };

    public static readonly Permissao ProducaoLaboratorioExcluir =
        new()
        {
            Id = Permissoes.Producao_Laboratorio_Excluir.ToInt(),
            Nome = "Excluir laboratórios"
        };

    #endregion Producao_Laboratorio

    #region Producao_Posologia

    public static readonly Permissao ProducaoPosologiaVerLista =
        new()
        {
            Id = Permissoes.Producao_Posologia_VerLista.ToInt(),
            Nome = "Ver lista das posologias"
        };

    public static readonly Permissao ProducaoPosologiaVerDetalhes =
        new()
        {
            Id = Permissoes.Producao_Posologia_VerDetalhes.ToInt(),
            Nome = "Ver detalhes das posologias"
        };

    public static readonly Permissao ProducaoPosologiaCadastrar =
        new()
        {
            Id = Permissoes.Producao_Posologia_Cadastrar.ToInt(),
            Nome = "Cadastrar novas posologias"
        };

    public static readonly Permissao ProducaoPosologiaEditarDetalhes =
        new()
        {
            Id = Permissoes.Producao_Posologia_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes das posologias"
        };

    public static readonly Permissao ProducaoPosologiaAlterarStatus =
        new()
        {
            Id = Permissoes.Producao_Posologia_AlterarStatus.ToInt(),
            Nome = "Alterar status das posologias"
        };

    public static readonly Permissao ProducaoPosologiaExcluir =
        new()
        {
            Id = Permissoes.Producao_Posologia_Excluir.ToInt(),
            Nome = "Excluir posologias"
        };

    #endregion Producao_Posologia

    #region Producao_ModeloRotulo

    public static readonly Permissao ProducaoModeloRotuloVerLista =
        new()
        {
            Id = Permissoes.Producao_ModeloRotulo_VerLista.ToInt(),
            Nome = "Ver lista dos modelos de rótulos"
        };

    public static readonly Permissao ProducaoModeloRotuloVerDetalhes =
        new()
        {
            Id = Permissoes.Producao_ModeloRotulo_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos modelos de rótulos"
        };

    public static readonly Permissao ProducaoModeloRotuloCadastrar =
        new()
        {
            Id = Permissoes.Producao_ModeloRotulo_Cadastrar.ToInt(),
            Nome = "Cadastrar novos modelos de rótulos"
        };

    public static readonly Permissao ProducaoModeloRotuloEditarDetalhes =
        new()
        {
            Id = Permissoes.Producao_ModeloRotulo_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes dos modelos rótulos"
        };

    public static readonly Permissao ProducaoModeloRotuloExcluir =
        new()
        {
            Id = Permissoes.Producao_ModeloRotulo_Excluir.ToInt(),
            Nome = "Excluir modelos rótulos"
        };

    public static readonly Permissao ProducaoModeloRotuloAlterarStatus =
        new()
        {
            Id = Permissoes.Producao_ModeloRotulo_AlterarStatus.ToInt(),
            Nome = "Alterar Status modelos rótulos"
        };

    #endregion Producao_ModeloRotulo

    #region Producao_ModeloOrdem

    public static readonly Permissao ProducaoModeloOrdemManipulacaoVerLista =
        new()
        {
            Id = Permissoes.Producao_ModeloOrdemManipulacao_VerLista.ToInt(),
            Nome = "Ver lista dos modelos de ordens de manipulação"
        };

    public static readonly Permissao ProducaoModeloOrdemManipulacaoVerDetalhes =
        new()
        {
            Id = Permissoes.Producao_ModeloOrdemManipulacao_VerDetalhes.ToInt(),
            Nome = "Ver detalhes dos modelos de ordens de manipulação"
        };

    public static readonly Permissao ProducaoModeloOrdemManipulacaoCadastrar =
        new()
        {
            Id = Permissoes.Producao_ModeloOrdemManipulacao_Cadastrar.ToInt(),
            Nome = "Cadastrar novos modelos de ordens de manipulação"
        };

    public static readonly Permissao ProducaoModeloOrdemManipulacaoEditarDetalhes =
        new()
        {
            Id = Permissoes.Producao_ModeloOrdemManipulacao_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes de modelos ordens de manipulação"
        };

    public static readonly Permissao ProducaoModeloOrdemManipulacaoExcluir =
        new()
        {
            Id = Permissoes.Producao_ModeloOrdemManipulacao_Excluir.ToInt(),
            Nome = "Excluir modelos de ordens de manipulação"
        };

    public static readonly Permissao ProducaoModeloOrdemManipulacaoAlterarStatus =
        new()
        {
            Id = Permissoes.Producao_ModeloOrdemManipulacao_AlterarStatus.ToInt(),
            Nome = "Alterar Status de modelos ordens de manipulação"
        };

    #endregion Producao_ModeloOrdem

    #region Producao_RotuloReceitaManipulada

    public static readonly Permissao ProducaoRotuloReceitaManipuladaVerLista =
        new()
        {
            Id = Permissoes.Producao_RotuloReceitaManipulada_VerLista.ToInt(),
            Nome = "Ver lista de rótulos da receitas"
        };

    public static readonly Permissao ProducaoRotuloReceitaManipuladaEmitir =
        new()
        {
            Id = Permissoes.Producao_RotuloReceitaManipulada_Emitir.ToInt(),
            Nome = "Emitir rótulo de receita"
        };

    public static readonly Permissao ProducaoRotuloReceitaManipuladaEditarDetalhes =
        new()
        {
            Id = Permissoes.Producao_RotuloReceitaManipulada_EditarDetalhes.ToInt(),
            Nome = "Editar rótulo de receita"
        };

    #endregion

    #region Producao_OrdemManipulacaoReceitaManipulada

    public static readonly Permissao ProducaoOrdemManipulacaoReceitaManipuladaVerLista =
        new()
        {
            Id = Permissoes.Producao_OrdemManipulacaoReceitaManipulada_VerLista.ToInt(),
            Nome = "Ver lista de ordens de manipulação"
        };

    public static readonly Permissao ProducaoOrdemManipulacaoReceitaManipuladaEmitir =
        new()
        {
            Id = Permissoes.Producao_OrdemManipulacaoReceitaManipulada_Emitir.ToInt(),
            Nome = "Emitir ordem de manipulação"
        };

    #endregion

    #region Producao_CertificadoAnalise

    public static readonly Permissao ProducaoCertificadoAnaliseVerLista =
        new()
        {
            Id = Permissoes.Producao_CertificadoAnalise_VerLista.ToInt(),
            Nome = "Ver lista de certificados de análise"
        };

    public static readonly Permissao ProducaoCertificadoAnaliseVerDetalhes =
        new()
        {
            Id = Permissoes.Producao_CertificadoAnalise_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de certificados de análise"
        };

    public static readonly Permissao ProducaoCertificadoAnaliseCadastrar =
        new()
        {
            Id = Permissoes.Producao_CertificadoAnalise_Cadastrar.ToInt(),
            Nome = "Cadastrar certificados de análise"
        };

    #endregion

    #region Vendas_Clientes

    public static readonly Permissao VendasClientesVerLista =
        new()
        {
            Id = Permissoes.Vendas_Clientes_VerLista.ToInt(),
            Nome = "Ver lista de clientes"
        };

    public static readonly Permissao VendasClientesVerDetalhes =
        new()
        {
            Id = Permissoes.Vendas_Clientes_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de clientes"
        };

    public static readonly Permissao VendasClientesCadastrar =
        new()
        {
            Id = Permissoes.Vendas_Clientes_Cadastrar.ToInt(),
            Nome = "Cadastrar novos clientes"
        };

    public static readonly Permissao VendasClientesEditarDetalhes =
        new()
        {
            Id = Permissoes.Vendas_Clientes_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes de clientes"
        };

    public static readonly Permissao VendasClientesEditarContatos =
        new()
        {
            Id = Permissoes.Vendas_Clientes_EditarContatos.ToInt(),
            Nome = "Editar contatos de clientes"
        };

    public static readonly Permissao VendasClientesEditarEnderecos =
        new()
        {
            Id = Permissoes.Vendas_Clientes_EditarEnderecos.ToInt(),
            Nome = "Editar endereços de clientes"
        };

    public static readonly Permissao VendasClientesEditarDocumentos =
        new()
        {
            Id = Permissoes.Vendas_Clientes_EditarDocumentos.ToInt(),
            Nome = "Editar documentos de clientes"
        };

    public static readonly Permissao VendasClientesAlterarStatus =
        new()
        {
            Id = Permissoes.Vendas_Clientes_AlterarStatus.ToInt(),
            Nome = "Alterar status de clientes"
        };

    public static readonly Permissao VendasClientesExcluir =
        new()
        {
            Id = Permissoes.Vendas_Clientes_Excluir.ToInt(),
            Nome = "Excluir clientes"
        };

    #endregion Vendas_Clientes

    #region Vendas_EspecialidadePrescritor

    public static readonly Permissao VendasEspecialidadePrescritorVerLista =
        new()
        {
            Id = Permissoes.Vendas_EspecialidadePrescritor_VerLista.ToInt(),
            Nome = "Ver lista de especialidades do prescritor"
        };

    public static readonly Permissao VendasEspecialidadePrescritorVerDetalhes =
        new()
        {
            Id = Permissoes.Vendas_EspecialidadePrescritor_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de especialidades do prescritor"
        };

    public static readonly Permissao VendasEspecialidadePrescritorCadastrar =
        new()
        {
            Id = Permissoes.Vendas_EspecialidadePrescritor_Cadastrar.ToInt(),
            Nome = "Cadastrar novas de especialidades do prescritor"
        };

    public static readonly Permissao VendasEspecialidadePrescritorEditarDetalhes =
        new()
        {
            Id = Permissoes.Vendas_EspecialidadePrescritor_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes de especialidades do prescritor"
        };

    public static readonly Permissao VendasEspecialidadePrescritorAlterarStatus =
        new()
        {
            Id = Permissoes.Vendas_EspecialidadePrescritor_AlterarStatus.ToInt(),
            Nome = "Alterar status de especialidades do prescritor"
        };

    public static readonly Permissao VendasEspecialidadePrescritorExcluir =
        new()
        {
            Id = Permissoes.Vendas_EspecialidadePrescritor_Excluir.ToInt(),
            Nome = "Excluir especialidades do prescritor"
        };

    #endregion Vendas_EspecialidadePrescritor

    #region Vendas_Prescritores

    public static readonly Permissao VendasPrescritoresVerLista =
        new()
        {
            Id = Permissoes.Vendas_Prescritores_VerLista.ToInt(),
            Nome = "Ver lista de prescritores"
        };

    public static readonly Permissao VendasPrescritoresVerDetalhes =
        new()
        {
            Id = Permissoes.Vendas_Prescritores_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de prescritores"
        };

    public static readonly Permissao VendasPrescritoresCadastrar =
        new()
        {
            Id = Permissoes.Vendas_Prescritores_Cadastrar.ToInt(),
            Nome = "Cadastrar novos prescritores"
        };

    public static readonly Permissao VendasPrescritoresEditarDetalhes =
        new()
        {
            Id = Permissoes.Vendas_Prescritores_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes de prescritores"
        };

    public static readonly Permissao VendasPrescritoresEditarContatos =
        new()
        {
            Id = Permissoes.Vendas_Prescritores_EditarContatos.ToInt(),
            Nome = "Editar contatos de prescritores"
        };

    public static readonly Permissao VendasPrescritoresEditarEnderecos =
        new()
        {
            Id = Permissoes.Vendas_Prescritores_EditarEnderecos.ToInt(),
            Nome = "Editar endereços de prescritores"
        };

    public static readonly Permissao VendasPrescritoresEditarDocumentos =
        new()
        {
            Id = Permissoes.Vendas_Prescritores_EditarDocumentos.ToInt(),
            Nome = "Editar documentos de prescritores"
        };

    public static readonly Permissao VendasPrescritoresAlterarStatus =
        new()
        {
            Id = Permissoes.Vendas_Prescritores_AlterarStatus.ToInt(),
            Nome = "Alterar status de prescritores"
        };

    public static readonly Permissao VendasPrescritoresExcluir =
        new()
        {
            Id = Permissoes.Vendas_Prescritores_Excluir.ToInt(),
            Nome = "Excluir prescritores"
        };

    #endregion

    #region Vendas_Atendimentos

    public static readonly Permissao VendasAtendimentosVerLista =
        new()
        {
            Id = Permissoes.Vendas_Atendimentos_VerLista.ToInt(),
            Nome = "Ver lista de atendimentos"
        };

    public static readonly Permissao VendasAtendimentosVerDetalhes =
        new()
        {
            Id = Permissoes.Vendas_Atendimentos_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de atendimentos"
        };

    public static readonly Permissao VendasAtendimentosCadastrar =
        new()
        {
            Id = Permissoes.Vendas_Atendimentos_Cadastrar.ToInt(),
            Nome = "Cadastrar novos atendimentos"
        };

    #endregion

    #region Vendas_PedidosVenda

    public static readonly Permissao VendasPedidosVendaVerLista =
        new()
        {
            Id = Permissoes.Vendas_PedidosVenda_VerLista.ToInt(),
            Nome = "Ver lista de pedidos de venda"
        };

    public static readonly Permissao VendasPedidosVendaVerDetalhes =
        new()
        {
            Id = Permissoes.Vendas_PedidosVenda_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de pedidos de venda"
        };

    public static readonly Permissao VendasPedidosVendaCadastrar =
        new()
        {
            Id = Permissoes.Vendas_PedidosVenda_Cadastrar.ToInt(),
            Nome = "Cadastrar novos pedidos de venda"
        };

    public static readonly Permissao VendasPedidosVendaEditarDetalhes =
        new()
        {
            Id = Permissoes.Vendas_PedidosVenda_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes de pedidos de venda"
        };

    public static readonly Permissao VendasPedidosVendaAprovar =
        new()
        {
            Id = Permissoes.Vendas_PedidosVenda_Aprovar.ToInt(),
            Nome = "Aprovar pedidos de venda"
        };

    public static readonly Permissao VendasPedidosVendaReprovar =
        new()
        {
            Id = Permissoes.Vendas_PedidosVenda_Reprovar.ToInt(),
            Nome = "Reprovar pedidos de venda"
        };

    public static readonly Permissao VendasPedidosVendaCancelar =
        new()
        {
            Id = Permissoes.Vendas_PedidosVenda_Cancelar.ToInt(),
            Nome = "Cancelar pedidos de venda"
        };

    public static readonly Permissao VendasPedidosVendaEstornar =
        new()
        {
            Id = Permissoes.Vendas_PedidosVenda_Estornar.ToInt(),
            Nome = "Estornar pedidos de venda"
        };

    #endregion

    #region Catalogo_CapsulaCor

    public static readonly Permissao CatalogoCapsulaCorVerLista =
        new()
        {
            Id = Permissoes.Catalogo_CapsulaCor_VerLista.ToInt(),
            Nome = "Ver lista de cores de cápsulas"
        };

    public static readonly Permissao CatalogoCapsulaCorVerDetalhes =
        new()
        {
            Id = Permissoes.Catalogo_CapsulaCor_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de cores de cápsulas"
        };

    public static readonly Permissao CatalogoCapsulaCorCadastrar =
        new()
        {
            Id = Permissoes.Catalogo_CapsulaCor_Cadastrar.ToInt(),
            Nome = "Cadastrar cores de cápsulas"
        };

    public static readonly Permissao CatalogoCapsulaCorEditarDetalhes =
        new()
        {
            Id = Permissoes.Catalogo_CapsulaCor_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes de cores de cápsulas"
        };

    public static readonly Permissao CatalogoCapsulaCorExcluir =
        new()
        {
            Id = Permissoes.Catalogo_CapsulaCor_Excluir.ToInt(),
            Nome = "Excluir cores de cápsulas"
        };

    #endregion

    #region Catalogo_Mensagem

    public static readonly Permissao CatalogoMensagemVerLista =
        new()
        {
            Id = Permissoes.Catalogo_Mensagem_VerLista.ToInt(),
            Nome = "Ver lista de mensagens"
        };

    public static readonly Permissao CatalogoMensagemVerDetalhes =
        new()
        {
            Id = Permissoes.Catalogo_Mensagem_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de mensagens"
        };

    public static readonly Permissao CatalogoMensagemCadastrar =
        new()
        {
            Id = Permissoes.Catalogo_Mensagem_Cadastrar.ToInt(),
            Nome = "Cadastrar mensagens"
        };

    public static readonly Permissao CatalogoMensagemEditarDetalhes =
        new()
        {
            Id = Permissoes.Catalogo_Mensagem_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes de mensagens"
        };

    public static readonly Permissao CatalogoMensagemExcluir =
        new()
        {
            Id = Permissoes.Catalogo_Mensagem_Excluir.ToInt(),
            Nome = "Excluir mensagens"
        };

    #endregion

    #region Catalogo_Ncm

    public static readonly Permissao CatalogoNcmVerLista =
        new()
        {
            Id = Permissoes.Catalogo_Ncm_VerLista.ToInt(),
            Nome = "Ver lista de NCMs"
        };

    public static readonly Permissao CatalogoNcmVerDetalhes =
        new()
        {
            Id = Permissoes.Catalogo_Ncm_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de NCMs"
        };

    #endregion

    #region Catalogo_Cas

    public static readonly Permissao CatalogoCasVerLista =
        new()
        {
            Id = Permissoes.Catalogo_Cas_VerLista.ToInt(),
            Nome = "Ver lista de CAS"
        };

    public static readonly Permissao CatalogoCasVerDetalhes =
        new()
        {
            Id = Permissoes.Catalogo_Cas_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de CAS"
        };

    #endregion

    #region Catalogo_Dcb

    public static readonly Permissao CatalogoDcbVerLista =
        new()
        {
            Id = Permissoes.Catalogo_Dcb_VerLista.ToInt(),
            Nome = "Ver lista de DCBs"
        };

    public static readonly Permissao CatalogoDcbVerDetalhes =
        new()
        {
            Id = Permissoes.Catalogo_Dcb_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de DCBs"
        };

    #endregion

    #region Catalogo_CapsulaTamanho

    public static readonly Permissao CatalogoCapsulaTamanhoVerLista =
        new()
        {
            Id = Permissoes.Catalogo_CapsulaTamanho_VerLista.ToInt(),
            Nome = "Ver lista de tamanhos de cápsulas"
        };

    public static readonly Permissao CatalogoCapsulaTamanhoVerDetalhes =
        new()
        {
            Id = Permissoes.Catalogo_CapsulaTamanho_VerDetalhes.ToInt(),
            Nome = "Ver detalhes de tamanhos de cápsulas"
        };

    public static readonly Permissao CatalogoCapsulaTamanhoCadastrar =
        new()
        {
            Id = Permissoes.Catalogo_CapsulaTamanho_Cadastrar.ToInt(),
            Nome = "Cadastrar tamanhos de cápsulas"
        };

    public static readonly Permissao CatalogoCapsulaTamanhoEditarDetalhes =
        new()
        {
            Id = Permissoes.Catalogo_CapsulaTamanho_EditarDetalhes.ToInt(),
            Nome = "Editar detalhes de tamanhos de cápsulas"
        };

    public static readonly Permissao CatalogoCapsulaTamanhoExcluir =
        new()
        {
            Id = Permissoes.Catalogo_CapsulaTamanho_Excluir.ToInt(),
            Nome = "Excluir tamanhos de cápsulas"
        };

    #endregion
}