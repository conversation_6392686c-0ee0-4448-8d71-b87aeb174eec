using System.Reflection;
using Bootis.Organizacional.Domain.AggregatesModel.PermissaoAggregate;

namespace Bootis.Organizacional.Domain.Statics;

public static class SubModulosStatic
{
    public static FieldInfo[] GetSubModulosFields()
    {
        var type = typeof(SubModulosStatic);

        return type.GetFields();
    }

    #region Administrativo

    public static readonly SubModulo AdministrativoEmpresas =
        new()
        {
            Id = new Guid("57b8da69-1a1e-47fc-9ca1-0e5aa9c1509c"),
            Nome = "Empresas",
            Icone = "buildings",
            Permissoes = new[]
            {
                PermissoesStatic.AdministrativoEmpresaVerLista,
                PermissoesStatic.AdministrativoEmpresaVerDetalhes,
                PermissoesStatic.AdministrativoEmpresaEditarDados,
                PermissoesStatic.AdministrativoEmpresaEditarEndereco,
                PermissoesStatic.AdministrativoEmpresaEditarUsuarioResponsavel,
                PermissoesStatic.AdministrativoEmpresaEditarStatus
            }
        };

    public static readonly SubModulo AdministrativoGrupoUsuario =
        new()
        {
            Id = new Guid("04a35317-070b-4822-8872-7229ed03799d"),
            Nome = "Grupos de Usuários",
            Icone = "users-outline",
            Permissoes = new[]
            {
                PermissoesStatic.AdministrativoGrupoUsuariosVerLista,
                PermissoesStatic.AdministrativoGrupoUsuariosVerDetalhes,
                PermissoesStatic.AdministrativoGrupoUsuariosCriar,
                PermissoesStatic.AdministrativoGrupoUsuariosEditarDetalhes,
                PermissoesStatic.AdministrativoGrupoUsuariosAlterarStatus,
                PermissoesStatic.AdministrativoGrupoUsuariosEditarPermissao,
                PermissoesStatic.AdministrativoGrupoUsuariosExcluir
            }
        };

    public static readonly SubModulo AdministrativoUsuario =
        new()
        {
            Id = new Guid("3706390b-4084-4c85-b533-18eab5f00579"),
            Nome = "Usuários",
            Icone = "account",
            Permissoes = new[]
            {
                PermissoesStatic.AdministrativoUsuarioVerLista,
                PermissoesStatic.AdministrativoUsuariosVerDetalhes,
                PermissoesStatic.AdministrativoUsuariosCadastrar,
                PermissoesStatic.AdministrativoUsuarioEditarDadosPessoais,
                PermissoesStatic.AdministrativoUsuarioAlterarStatus,
                PermissoesStatic.AdministrativoUsuarioEditarGrupo,
                PermissoesStatic.AdministrativoUsuarioEditarPermissao,
                PermissoesStatic.AdministrativoUsuarioExcluir
            }
        };

    #endregion

    #region Compras

    public static readonly SubModulo ComprasFornecedores =
        new()
        {
            Id = new Guid("a0161648-33c1-4356-a8b9-d4bfc2497425"),
            Nome = "Fornecedores",
            Icone = "shipping-truck",
            Permissoes = new[]
            {
                PermissoesStatic.ComprasFornecedoresVerLista,
                PermissoesStatic.ComprasFornecedoresVerDetalhes,
                PermissoesStatic.ComprasFornecedoresCadastrar,
                PermissoesStatic.ComprasFornecedoresEditarDetalhes,
                PermissoesStatic.ComprasFornecedoresEditarContatos,
                PermissoesStatic.ComprasFornecedoresEditarEnderecos,
                PermissoesStatic.ComprasFornecedoresEditarDocumentos,
                PermissoesStatic.ComprasFornecedoresAlterarStatus,
                PermissoesStatic.ComprasFornecedoresExcluir
            }
        };

    public static readonly SubModulo ComprasPedidosCompra =
        new()
        {
            Id = new Guid("f8d4ca66-66c9-455e-b67f-86a6b5edb8b7"),
            Nome = "Pedidos Compra",
            Icone = "basket",
            Permissoes = new[]
            {
                PermissoesStatic.ComprasPedidoCompraVerLista,
                PermissoesStatic.ComprasPedidoCompraVerDetalhes,
                PermissoesStatic.ComprasPedidoCompraCadastrar,
                PermissoesStatic.ComprasPedidoCompraEditarDetalhes,
                PermissoesStatic.ComprasPedidoCompraAprovar,
                PermissoesStatic.ComprasPedidoCompraReprovar,
                PermissoesStatic.ComprasPedidoCompraCancelar,
                PermissoesStatic.ComprasPedidoCompraConfirmarComFornecedor,
                PermissoesStatic.ComprasPedidoCompraEstornarLiberado,
                PermissoesStatic.ComprasPedidoCompraEstornarConfirmadoComFornecedor,
                PermissoesStatic.ComprasPedidoCompraExcluir
            }
        };

    public static readonly SubModulo ComprasNotasFiscaisEntrada =
        new()
        {
            Id = new Guid("4d6dc132-db70-4322-b2a2-7b199954969a"),
            Nome = "Notas Fiscais Entrada",
            Icone = "invoice-list",
            Permissoes = new[]
            {
                PermissoesStatic.ComprasNotaFiscalEntradaVerLista,
                PermissoesStatic.ComprasNotaFiscalEntradaVerDetalhes,
                PermissoesStatic.ComprasNotaFiscalEntradaCadastrar,
                PermissoesStatic.ComprasNotaFiscalEntradaEditarDetalhes,
                PermissoesStatic.ComprasNotaFiscalEntradaAlterarStatus,
                PermissoesStatic.ComprasNotaFiscalEntradaExcluir,
                PermissoesStatic.ComprasNotaFiscalEntradaLancarLotes
            }
        };

    public static readonly SubModulo ComprasNecessidadeCompras =
        new()
        {
            Id = new Guid("ff56bc7a-694f-43a4-a692-ce700f7f63f0"),
            Nome = "Necessidade de Compras",
            Icone = "basket-minus",
            Permissoes = new[]
            {
                PermissoesStatic.ComprasNecessidadeComprasVerLista
            }
        };

    #endregion

    #region Estoque

    public static readonly SubModulo EstoqueLotes =
        new()
        {
            Id = new Guid("9b47cce2-20ee-4553-9864-dcf83f119974"),
            Nome = "Lotes",
            Icone = "cube",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueLotesVerLista,
                PermissoesStatic.EstoqueLotesVerDetalhes,
                PermissoesStatic.EstoqueLotesCadastrar,
                PermissoesStatic.EstoqueLotesEditarInformacoes,
                PermissoesStatic.EstoqueLotesAlterarStatus,
                PermissoesStatic.EstoqueLotesExcluir
            }
        };

    public static readonly SubModulo EstoqueLocaisLotes =
        new()
        {
            Id = new Guid("9ba1db58-d09c-467f-81e7-86586ecb6c8a"),
            Nome = "Locais de Estoque",
            Icone = "simple-cube",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueLocaisEstoqueVerLista,
                PermissoesStatic.EstoqueLocaisEstoqueVerDetalhes,
                PermissoesStatic.EstoqueLocaisEstoqueCadastrar,
                PermissoesStatic.EstoqueLocaisEstoqueEditarDetalhes,
                PermissoesStatic.EstoqueLocaisEstoqueAlterarStatus,
                PermissoesStatic.EstoqueLocaisEstoqueExcluir
            }
        };

    public static readonly SubModulo EstoqueTransferencia =
        new()
        {
            Id = new Guid("40027430-0ed9-4756-b24e-3b23a7494665"),
            Nome = "Transferências",
            Icone = "connection",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueTransferenciasVerLista,
                PermissoesStatic.EstoqueTransferenciasVerDetalhes,
                PermissoesStatic.EstoqueTransferenciasCadastrar
            }
        };

    public static readonly SubModulo EstoquePerdas =
        new()
        {
            Id = new Guid("b4ba91ae-4f70-4ea7-bae4-0041debabf02"),
            Nome = "Perdas",
            Icone = "bin-full",
            Permissoes = new[]
            {
                PermissoesStatic.EstoquePerdasVerLista,
                PermissoesStatic.EstoquePerdasVerDetalhes,
                PermissoesStatic.EstoquePerdasCadastrar,
                PermissoesStatic.EstoquePerdasEditarDetalhes
            }
        };

    public static readonly SubModulo EstoqueAjusteSaldo =
        new()
        {
            Id = new Guid("4ca3e739-06c1-4bff-9eb3-c220eec63157"),
            Nome = "Ajuste de Saldo",
            Icone = "clipboard",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueAjusteSaldoVerLista,
                PermissoesStatic.EstoqueAjusteSaldoVerDetalhes,
                PermissoesStatic.EstoqueAjusteSaldoCadastrar
            }
        };

    public static readonly SubModulo EstoqueGrupoProdutos =
        new()
        {
            Id = new Guid("950b5125-9360-485e-a6f8-7f5c11e870f1"),
            Nome = "Grupos de Produtos",
            Icone = "tag",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueGruposVerLista,
                PermissoesStatic.EstoqueGruposVerDetalhes,
                PermissoesStatic.EstoqueGruposCadastrar,
                PermissoesStatic.EstoqueGruposEditarDetalhes,
                PermissoesStatic.EstoqueGruposExcluir
            }
        };

    public static readonly SubModulo EstoqueSubgruposProdutos =
        new()
        {
            Id = new Guid("a3dd829a-1e8f-4786-b5e8-dc99a6ca8865"),
            Nome = "Subgrupos de Produtos",
            Icone = "tags",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueSubGruposVerLista,
                PermissoesStatic.EstoqueSubGruposVerDetalhes,
                PermissoesStatic.EstoqueSubGruposCadastrar,
                PermissoesStatic.EstoqueSubGruposEditarDetalhes,
                PermissoesStatic.EstoqueSubGruposExcluir
            }
        };

    public static readonly SubModulo EstoqueProdutos =
        new()
        {
            Id = new Guid("7d4f0a7d-d909-4a80-86fe-d65b7fc708c3"),
            Nome = "Produtos",
            Icone = "simple-cube",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueProdutosVerLista,
                PermissoesStatic.EstoqueProdutosVerDetalhes,
                PermissoesStatic.EstoqueProdutosCadastrar,
                PermissoesStatic.EstoqueProdutosEditarInformacoes,
                PermissoesStatic.EstoqueProdutosEditarInformacoesFinanceiras,
                PermissoesStatic.EstoqueProdutosAlterarStatus,
                PermissoesStatic.EstoqueProdutosExcluir,
                PermissoesStatic.EstoqueInformacoesTecnicasVisualizar,
                PermissoesStatic.EstoqueProdutosAssociadoVisualizar,
                PermissoesStatic.EstoqueProdutosAssociadoCadastrar,
                PermissoesStatic.EstoqueProdutosAssociadoEditarDetalhes,
                PermissoesStatic.EstoqueProdutosAssociadoExcluir,
                PermissoesStatic.EstoqueProdutosDiluidoVisualizar,
                PermissoesStatic.EstoqueProdutosDiluidoCadastrar,
                PermissoesStatic.EstoqueProdutosDiluidoEditarDetalhes,
                PermissoesStatic.EstoqueProdutosDiluidoExcluir,
                PermissoesStatic.EstoqueProdutosIncompativelVisualizar,
                PermissoesStatic.EstoqueProdutosIncompativelCadastrar,
                PermissoesStatic.EstoqueProdutosIncompativelEditarDetalhes,
                PermissoesStatic.EstoqueProdutosIncompativelExcluir,
                PermissoesStatic.EstoqueProdutosSinonimoVisualizar,
                PermissoesStatic.EstoqueProdutosSinonimoCadastrar,
                PermissoesStatic.EstoqueProdutosSinonimoEditarDetalhes,
                PermissoesStatic.EstoqueProdutosSinonimoExcluir,
                PermissoesStatic.EstoqueProdutosMensagemVisualizar,
                PermissoesStatic.EstoqueProdutosMensagemCadastrar,
                PermissoesStatic.EstoqueProdutosMensagemExcluir,
                PermissoesStatic.EstoqueProdutosFichaTecnicaEditar,
                PermissoesStatic.EstoqueProdutosFichaTecnicaVisualizar,
            }
        };

    public static readonly SubModulo EstoqueRastreabilidade =
        new()
        {
            Id = new Guid("8aa948cd-99e6-42c4-81f0-fd7d1fe9d0c9"),
            Nome = "Rastreabilidade",
            Icone = "Archery",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueRastreabilidadeVisualizar
            }
        };

    public static readonly SubModulo EstoqueMovimentacao =
        new()
        {
            Id = new Guid("b0dd66f2-1a24-4dbb-a2fa-d500a7d284dc"),
            Nome = "Movimentação",
            Icone = "invoice-list",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueMovimentacaoVisualizar
            }
        };

    public static readonly SubModulo EstoqueMensagemProduto =
        new()
        {
            Id = new Guid("a5aa60e6-4084-48d0-8c63-cd5d687230ef"),
            Nome = "Mensagem do Produto",
            Icone = "landscape-text-file",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueMensagemProdutoVerLista,
                PermissoesStatic.EstoqueMensagemProdutoVerDetalhes,
                PermissoesStatic.EstoqueMensagemProdutoCadastrar,
                PermissoesStatic.EstoqueMensagemProdutoEditarDetalhes,
                PermissoesStatic.EstoqueMensagemProdutoRemoverProduto,
                PermissoesStatic.EstoqueMensagemProdutoExcluir
            }
        };

    public static readonly SubModulo EstoqueInventario =
        new()
        {
            Id = new Guid("fb5cb456-293f-4a48-ade8-d8393f104021"),
            Nome = "Inventário",
            Icone = "inventory",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueInventarioVerLista,
                PermissoesStatic.EstoqueInventarioVerDetalhes,
                PermissoesStatic.EstoqueInventarioCadastrar,
                PermissoesStatic.EstoqueInventarioVerDetalhesConferencia,
                PermissoesStatic.EstoqueInventarioExcluir
            }
        };

    public static readonly SubModulo EstoqueProjecao =
        new()
        {
            Id = new Guid("6daba2b7-a260-420e-8c22-025512018634"),
            Nome = "Projeção",
            Icone = "mathemetics-calculation",
            Permissoes = new[]
            {
                PermissoesStatic.EstoqueProjecaoVisualizar
            }
        };

    #endregion

    #region Producao

    public static readonly SubModulo ProducaoClassificacaoEmbalagem =
        new()
        {
            Id = new Guid("cdc07904-4f0f-4bb5-b599-d6e00a8795b8"),
            Nome = "Classificação Embalagem",
            Icone = "packaging-classification",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoClassificacaoEmbalagemVerLista,
                PermissoesStatic.ProducaoClassificacaoEmbalagemVerDetalhes,
                PermissoesStatic.ProducaoClassificacaoEmbalagemCadastrar,
                PermissoesStatic.ProducaoClassificacaoEmbalagemEditarDetalhes,
                PermissoesStatic.ProducaoClassificacaoEmbalagemAlterarStatus,
                PermissoesStatic.ProducaoClassificacaoEmbalagemExcluir
            }
        };

    public static readonly SubModulo ProducaoCapsulaCor =
        new()
        {
            Id = new Guid("7b73cd75-f357-4898-9268-d062724f456e"),
            Nome = "Cores de cápsulas",
            Icone = "capsules-colors",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoCapsulaCorVerLista,
                PermissoesStatic.ProducaoCapsulaCorVerDetalhes,
                PermissoesStatic.ProducaoCapsulaCorCadastrar,
                PermissoesStatic.ProducaoCapsulaCorEditarDetalhes,
                PermissoesStatic.ProducaoCapsulaCorExcluir
            }
        };

    public static readonly SubModulo ProducaoFormaFarmaceutica =
        new()
        {
            Id = new Guid("061a82d6-8a08-4222-be78-90cceb3ea91f"),
            Nome = "Forma Farmacêutica",
            Icone = "pharmaceutical-form",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoFormaFarmaceuticaVerLista,
                PermissoesStatic.ProducaoFormaFarmaceuticaVerDetalhes,
                PermissoesStatic.ProducaoFormaFarmaceuticaCadastrar,
                PermissoesStatic.ProducaoFormaFarmaceuticaEditarDetalhes,
                PermissoesStatic.ProducaoFormaFarmaceuticaAlterarStatus,
                PermissoesStatic.ProducaoFormaFarmaceuticaExcluir
            }
        };

    public static readonly SubModulo ProducaoFormulaPadrao =
        new()
        {
            Id = new Guid("f9c750b4-cc2f-40e6-b24f-43081dd91542"),
            Nome = "Formula Padrão",
            Icone = "edit-file",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoFormulaPadraoVerLista,
                PermissoesStatic.ProducaoFormulaPadraoVerDetalhes,
                PermissoesStatic.ProducaoFormulaPadraoCadastrar,
                PermissoesStatic.ProducaoFormulaPadraoEditarDetalhes,
                PermissoesStatic.ProducaoFormulaPadraoExcluir
            }
        };

    public static readonly SubModulo ProducaoLaboratorio =
        new()
        {
            Id = new Guid("8b0b8848-4433-4ed9-b0c2-d58c6851a605"),
            Nome = "Laboratório",
            Icone = "conical-flask",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoLaboratorioVerLista,
                PermissoesStatic.ProducaoLaboratorioVerDetalhes,
                PermissoesStatic.ProducaoLaboratorioCadastrar,
                PermissoesStatic.ProducaoLaboratorioEditarDetalhes,
                PermissoesStatic.ProducaoLaboratorioAlterarStatus,
                PermissoesStatic.ProducaoLaboratorioExcluir
            }
        };

    public static readonly SubModulo ProducaoPosologia =
        new()
        {
            Id = new Guid("835c01d7-6ede-4de9-82b3-8e99d73329fa"),
            Nome = "Posologia",
            Icone = "dosage",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoPosologiaVerLista,
                PermissoesStatic.ProducaoPosologiaVerDetalhes,
                PermissoesStatic.ProducaoPosologiaCadastrar,
                PermissoesStatic.ProducaoPosologiaEditarDetalhes,
                PermissoesStatic.ProducaoPosologiaAlterarStatus,
                PermissoesStatic.ProducaoPosologiaExcluir
            }
        };

    public static readonly SubModulo ProducaoReceitaManipulada =
        new()
        {
            Id = new Guid("6249a321-c095-4b69-a002-e4d19dc965be"),
            Nome = "Receita Manipulada",
            Icone = "recipe",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoReceitaManipuladaVerLista,
                PermissoesStatic.ProducaoReceitaManipuladaEditarDetalhes,
                PermissoesStatic.ProducaoReceitaManipuladaVerDetalhes,
                PermissoesStatic.ProducaoReceitaManipuladaCadastrar,
                PermissoesStatic.ProducaoReceitaManipuladaAlterarStatus,
                PermissoesStatic.ProducaoReceitaManipuladaExcluir,
                PermissoesStatic.ProducaoReceitaManipuladaCancelar,
                PermissoesStatic.ProducaoIncompatibilidadeLiberar
            }
        };

    public static readonly SubModulo ProducaoModeloRotulo =
        new()
        {
            Id = new Guid("6da1b033-09fd-4450-bcff-182228d1ca63"),
            Nome = "Modelo Rótulo",
            Icone = "layer",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoModeloRotuloVerLista,
                PermissoesStatic.ProducaoModeloRotuloVerDetalhes,
                PermissoesStatic.ProducaoModeloRotuloCadastrar,
                PermissoesStatic.ProducaoModeloRotuloEditarDetalhes,
                PermissoesStatic.ProducaoModeloRotuloExcluir,
                PermissoesStatic.ProducaoModeloRotuloAlterarStatus
            }
        };

    public static readonly SubModulo ProducaoModeloOrdemManipulacao =
        new()
        {
            Id = new Guid("d1f2f977-1c9e-4d81-8c8d-81e6271d6fe8"),
            Nome = "Modelo Ordem Manipulação",
            Icone = "layer",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoModeloOrdemManipulacaoVerLista,
                PermissoesStatic.ProducaoModeloOrdemManipulacaoVerDetalhes,
                PermissoesStatic.ProducaoModeloOrdemManipulacaoCadastrar,
                PermissoesStatic.ProducaoModeloOrdemManipulacaoEditarDetalhes,
                PermissoesStatic.ProducaoModeloOrdemManipulacaoExcluir,
                PermissoesStatic.ProducaoModeloOrdemManipulacaoAlterarStatus
            }
        };

    public static readonly SubModulo ProducaoRotuloReceitaManipulada =
        new()
        {
            Id = new Guid("26c25a00-1656-4faf-b710-5e7730ea950e"),
            Nome = "Emissão de Rótulo de Receita",
            Icone = "tags",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoRotuloReceitaManipuladaVerLista,
                PermissoesStatic.ProducaoRotuloReceitaManipuladaEditarDetalhes,
                PermissoesStatic.ProducaoRotuloReceitaManipuladaEmitir
            }
        };

    public static readonly SubModulo ProducaoOrdemManipulacaoReceitaManipulada =
        new()
        {
            Id = new Guid("69a2826b-04aa-4642-9dfc-afcddc763731"),
            Nome = "Emissão de Ordem de Manipulação",
            Icone = "landscape-text-files",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoOrdemManipulacaoReceitaManipuladaVerLista,
                PermissoesStatic.ProducaoOrdemManipulacaoReceitaManipuladaEmitir
            }
        };

    //TODO aguardando definição UI/UX para correção do ícone
    public static readonly SubModulo ProducaoCertificadoAnalise =
        new()
        {
            Id = new Guid("663e4758-b7aa-42c9-99df-d322fd2cad0b"),
            Nome = "Certificado de Análise",
            Icone = "landscape-text-files",
            Permissoes = new[]
            {
                PermissoesStatic.ProducaoCertificadoAnaliseCadastrar,
                PermissoesStatic.ProducaoCertificadoAnaliseVerDetalhes,
                PermissoesStatic.ProducaoCertificadoAnaliseVerLista
            }
        };

    #endregion Producao

    #region Vendas

    public static readonly SubModulo VendasClientes =
        new()
        {
            Id = new Guid("f1b769dd-f04f-4447-94b5-377d5857ad63"),
            Nome = "Clientes",
            Icone = "account",
            Permissoes = new[]
            {
                PermissoesStatic.VendasClientesVerLista,
                PermissoesStatic.VendasClientesVerDetalhes,
                PermissoesStatic.VendasClientesCadastrar,
                PermissoesStatic.VendasClientesEditarDetalhes,
                PermissoesStatic.VendasClientesEditarContatos,
                PermissoesStatic.VendasClientesEditarEnderecos,
                PermissoesStatic.VendasClientesEditarDocumentos,
                PermissoesStatic.VendasClientesAlterarStatus,
                PermissoesStatic.VendasClientesExcluir
            }
        };

    public static readonly SubModulo VendasEspecialidadePrescritor =
        new()
        {
            Id = new Guid("f3992a38-e0ef-4100-9c7f-1089b2f1c818"),
            Nome = "Especialidades Prescritor",
            Icone = "prescriber",
            Permissoes = new[]
            {
                PermissoesStatic.VendasEspecialidadePrescritorVerLista,
                PermissoesStatic.VendasEspecialidadePrescritorVerDetalhes,
                PermissoesStatic.VendasEspecialidadePrescritorCadastrar,
                PermissoesStatic.VendasEspecialidadePrescritorEditarDetalhes,
                PermissoesStatic.VendasEspecialidadePrescritorAlterarStatus,
                PermissoesStatic.VendasEspecialidadePrescritorExcluir
            }
        };

    public static readonly SubModulo VendasPrescritores =
        new()
        {
            Id = new Guid("1c083798-597d-487a-b839-0199850e6659"),
            Nome = "Prescritores",
            Icone = "prescriber",
            Permissoes = new[]
            {
                PermissoesStatic.VendasPrescritoresVerLista,
                PermissoesStatic.VendasPrescritoresVerDetalhes,
                PermissoesStatic.VendasPrescritoresCadastrar,
                PermissoesStatic.VendasPrescritoresEditarDetalhes,
                PermissoesStatic.VendasPrescritoresEditarContatos,
                PermissoesStatic.VendasPrescritoresEditarEnderecos,
                PermissoesStatic.VendasPrescritoresEditarDocumentos,
                PermissoesStatic.VendasPrescritoresAlterarStatus,
                PermissoesStatic.VendasPrescritoresExcluir
            }
        };

    public static readonly SubModulo VendasAtendimentos =
        new()
        {
            Id = new Guid("7d9b28ed-7517-461c-a077-ffa6c0815029"),
            Nome = "Atendimentos",
            Icone = "chat-bubble",
            Permissoes = new[]
            {
                PermissoesStatic.VendasAtendimentosVerLista,
                PermissoesStatic.VendasAtendimentosVerDetalhes,
                PermissoesStatic.VendasAtendimentosCadastrar
            }
        };

    public static readonly SubModulo VendasPedidosVenda =
        new()
        {
            Id = new Guid("c9cac530-5b49-427e-8594-86d466a1e1a0"),
            Nome = "Pedidos Venda",
            Icone = "cart",
            Permissoes = new[]
            {
                PermissoesStatic.VendasPedidosVendaVerLista,
                PermissoesStatic.VendasPedidosVendaVerDetalhes,
                PermissoesStatic.VendasPedidosVendaCadastrar,
                PermissoesStatic.VendasPedidosVendaEditarDetalhes,
                PermissoesStatic.VendasPedidosVendaAprovar,
                PermissoesStatic.VendasPedidosVendaReprovar,
                PermissoesStatic.VendasPedidosVendaCancelar,
                PermissoesStatic.VendasPedidosVendaEstornar
            }
        };

    #endregion
}