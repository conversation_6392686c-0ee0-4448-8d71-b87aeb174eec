using System.Reflection;
using Bootis.Organizacional.Domain.AggregatesModel.PermissaoAggregate;

namespace Bootis.Organizacional.Domain.Statics;

public static class ModulosStatic
{
    public static readonly <PERSON><PERSON>lo Administrativo =
        new()
        {
            Id = new Guid("7d310832-762c-436f-93b0-e3eba56ac6f5"),
            Nome = "Administrativo",
            Icone = "setting",
            CorDeFundo = "#5E50F2",
            SubModulos = new[]
            {
                SubModulosStatic.AdministrativoUsuario,
                SubModulosStatic.AdministrativoGrupoUsuario,
                SubModulosStatic.AdministrativoEmpresas
            }
        };

    public static readonly <PERSON><PERSON>lo <PERSON> =
        new()
        {
            Id = new Guid("cd836e2e-2774-4db7-8b02-90d7c074c773"),
            Nome = "Compras",
            Icone = "shopping-bag",
            CorDeFundo = "#0353A4",
            SubModulos = new[]
            {
                SubModulosStatic.ComprasFornecedores,
                SubModulosStatic.ComprasPedidosCompra,
                SubModulosStatic.ComprasNotasFiscaisEntrada,
                SubModulosStatic.ComprasNecessidadeCompras
            }
        };

    public static readonly Modulo Estoque =
        new()
        {
            Id = new Guid("3834adf9-90f7-4fd0-9dbd-aaad705cce7c"),
            Nome = "Estoque",
            Icone = "archive",
            CorDeFundo = "#333333",
            SubModulos = new[]
            {
                SubModulosStatic.EstoqueLotes,
                SubModulosStatic.EstoqueLocaisLotes,
                SubModulosStatic.EstoqueTransferencia,
                SubModulosStatic.EstoquePerdas,
                SubModulosStatic.EstoqueAjusteSaldo,
                SubModulosStatic.EstoqueGrupoProdutos,
                SubModulosStatic.EstoqueSubgruposProdutos,
                SubModulosStatic.EstoqueProdutos,
                SubModulosStatic.EstoqueMensagemProduto,
                SubModulosStatic.EstoqueInventario,
                SubModulosStatic.EstoqueRastreabilidade,
                SubModulosStatic.EstoqueMovimentacao,
                SubModulosStatic.EstoqueProjecao
            }
        };

    public static readonly Modulo Producao =
        new()
        {
            Id = new Guid("ddb15080-a55b-4196-8d95-af9c544d98f2"),
            Nome = "Producao",
            Icone = "conical-flask-gear",
            CorDeFundo = "#4BD0F9",
            SubModulos = new[]
            {
                SubModulosStatic.ProducaoClassificacaoEmbalagem,
                SubModulosStatic.ProducaoCapsulaCor,
                SubModulosStatic.ProducaoFormaFarmaceutica,
                SubModulosStatic.ProducaoFormulaPadrao,
                SubModulosStatic.ProducaoLaboratorio,
                SubModulosStatic.ProducaoPosologia,
                SubModulosStatic.ProducaoReceitaManipulada,
                SubModulosStatic.ProducaoModeloRotulo,
                SubModulosStatic.ProducaoModeloOrdemManipulacao,
                SubModulosStatic.ProducaoRotuloReceitaManipulada,
                SubModulosStatic.ProducaoOrdemManipulacaoReceitaManipulada,
                SubModulosStatic.ProducaoCertificadoAnalise
            }
        };

    public static readonly Modulo Vendas =
        new()
        {
            Id = new Guid("624db2f0-1eb5-4d73-9196-5546573d88ea"),
            Nome = "Vendas",
            Icone = "invoice",
            CorDeFundo = "#34A0A4",
            SubModulos = new[]
            {
                SubModulosStatic.VendasEspecialidadePrescritor,
                SubModulosStatic.VendasClientes,
                SubModulosStatic.VendasPrescritores,
                SubModulosStatic.VendasAtendimentos,
                SubModulosStatic.VendasPedidosVenda
            }
        };

    private static IEnumerable<Modulo> _modulos;

    public static IEnumerable<Modulo> GetModulos()
    {
        if (_modulos != null)
            return _modulos;

        var type = typeof(ModulosStatic);

        return _modulos = type.GetFields()
            .Select(c => (Modulo)c.GetValue(null));
    }

    public static FieldInfo[] GetModulosFields()
    {
        var type = typeof(ModulosStatic);

        return type.GetFields();
    }
}