<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Shared\Bootis.Shared.Domain\Bootis.Shared.Domain.csproj"/>
        <ProjectReference Include="..\Bootis.Identity.Contracts\Bootis.Identity.Contracts.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Identity.Core" Version="9.0.9"/>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.14.0" />
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>
