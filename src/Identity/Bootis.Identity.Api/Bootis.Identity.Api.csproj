<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <AssemblyName>Bootis.Identity.Api</AssemblyName>
        <RootNamespace>Bootis.Identity.Api</RootNamespace>
        <UserSecretsId>8b692a8c-19b5-4f55-ade7-34200ef8110a</UserSecretsId>
        <ProjectGuid>{9495f9e1-b8c1-4696-b301-a123ac225484}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Aspire\Bootis.ServiceDefaults\Bootis.ServiceDefaults.csproj"/>
        <ProjectReference Include="..\..\Pharma\Organizacional\Bootis.Organizacional.Contracts\Bootis.Organizacional.Contracts.csproj"/>
        <ProjectReference Include="..\..\Shared\Bootis.Shared.Api\Bootis.Shared.Api.csproj"/>
        <ProjectReference Include="..\Bootis.Identity.Application\Bootis.Identity.Application.csproj"/>
        <ProjectReference Include="..\Bootis.Identity.Contracts\Bootis.Identity.Contracts.csproj"/>
        <ProjectReference Include="..\Bootis.Identity.Domain\Bootis.Identity.Domain.csproj"/>
        <ProjectReference Include="..\Bootis.Identity.Infrastructure\Bootis.Identity.Infrastructure.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.9">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>



    <ItemGroup>
        <Compile Remove="Migrations\20250515203810_AddIndexOutboxMessages.Designer.cs"/>
    </ItemGroup>
</Project>

