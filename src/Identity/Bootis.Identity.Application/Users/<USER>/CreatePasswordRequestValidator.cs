using FluentValidation;

namespace Bootis.Identity.Application.Users.Validators;

public class CreatePasswordRequestValidator : AbstractValidator<CreatePasswordRequest>
{
    public CreatePasswordRequestValidator()
    {
        RuleFor(c => c.Id)
            .NotEmpty();

        RuleFor(c => c.NewPassword)
            .NotEmpty();

        RuleFor(c => c.ConfirmPassword)
            .Equal(c => c.NewPassword);
    }
}