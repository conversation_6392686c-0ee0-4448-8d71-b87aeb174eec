using Bootis.Identity.Application.Authentication.Models;
using Bootis.Identity.Application.Authentication.Services;
using Bootis.Identity.Domain.Interfaces;
using Bootis.Shared.Common.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bootis.Identity.Application.Authentication;

public class LoginRequestHandler(
    IUserAuthenticationService userAuthenticationService,
    IUserRepository userRepository,
    ILoginManager loginManager,
    ILogger<LoginRequestHandler> logger)
    : IRequestHandler<LoginRequest, LoginResponse>
{
    public async Task<LoginResponse> Handle(LoginRequest request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Processing login request for email: {Email}", request.Email);

            var authResult = await userAuthenticationService.AuthenticateAsync(
                request.Email, request.Password, cancellationToken);

            return await authResult.Match(
                async user =>
                {
                    var currency = await userRepository.GetUserWithCurrencyTypeAsync(request.Email, cancellationToken);
                    var login = await loginManager.GenerateLoginAsync(user, currency?.CurrencyTypeId ?? 1,
                        cancellationToken);

                    logger.LogInformation("User {UserId} logged in successfully", user.Id);
                    return login;
                },
                errorMessage =>
                {
                    logger.LogWarning("Login failed for email {Email}: {Error}", request.Email, errorMessage);
                    throw new ValidationException(nameof(request.Email), errorMessage);
                }
            );
        }
        catch (ValidationException)
        {
            throw;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during login for email {Email}", request.Email);
            throw;
        }
    }
}