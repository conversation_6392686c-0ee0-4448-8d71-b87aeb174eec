using System.Data;
using Bootis.Identity.Domain.Entities;
using Bootis.Identity.Domain.Interfaces;
using Bootis.Shared.Common.Security;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Identity.Infrastructure.Persistence.Repositories;

public class UserRepository(IDbContext context, IDbConnection dbConnection)
    : Repository<User>(context), IUserRepository
{
    #region Basic User Operations

    public async Task<User> GetByEmail(string email, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .FirstOrDefaultAsync(c => c.Email == email, cancellationToken);
    }

    public async Task<bool> Exist(string email, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .AsNoTracking()
            .Where(c => c.Email == email)
            .AnyAsync(cancellationToken);
    }

    public Task UpdateAsync(User user, CancellationToken cancellationToken)
    {
        user.UpdatedAt = DateTimeOffset.Now;
        DbSet.Update(user);
        return Task.CompletedTask;
    }

    public Task RemoveAsync(User user)
    {
        DbSet.Remove(user);
        return Task.CompletedTask;
    }

    #endregion

    #region User with Relations

    public async Task<User> GetUserWithGroupsAndPermissions(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Include(c => c.UserGroups)
            .Include(c => c.UserPermissions)
            .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<(User User, int CurrencyTypeId)?> GetUserWithCurrencyTypeAsync(string email,
        CancellationToken cancellationToken = default)
    {
        var query =
            from u in Context.Set<User>().AsNoTracking().Include(c => c.Preference)
            join t in Context.Set<Tenant>().AsNoTracking() on u.TenantId equals t.TenantId
            where u.Email == email
            select new { User = u, t.CurrencyTypeId };

        var result = await query.FirstOrDefaultAsync(cancellationToken);

        if (result is null)
            return null;

        return (result.User, result.CurrencyTypeId);
    }

    #endregion

    #region Group Management

    public async Task ChangeUsers(Group group, HashSet<Guid> userIds, CancellationToken cancellationToken)
    {
        var requestedUserIds = await DbSet
            .Where(u => userIds.Contains(u.Id))
            .Select(u => u.Id)
            .ToHashSetAsync(cancellationToken);

        var currentUserIds = group.UserGroups.Select(g => g.UserId).ToHashSet();

        var toRemove = group.UserGroups.Where(g => !requestedUserIds.Contains(g.UserId)).ToList();
        var toAdd = requestedUserIds.Except(currentUserIds).ToList();

        foreach (var user in toRemove)
            group.UserGroups.Remove(user);

        foreach (var userId in toAdd)
            group.UserGroups.Add(new UserGroup { UserId = userId });
    }

    public async Task<List<UserGroup>> GetUserGroupAsync(IEnumerable<Guid> userIds, CancellationToken cancellationToken)
    {
        return await DbSet
            .Where(c => userIds.Contains(c.Id))
            .Select(c => new UserGroup { UserId = c.Id })
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<int>> GetGroupUserIdsAsync(Guid groupId,
        CancellationToken cancellationToken = default)
    {
        const string sql = """
                           SELECT user_id 
                           FROM user_groups 
                           WHERE group_id = @groupId
                           """;

        var userIds = await dbConnection.QueryAsync<int>(sql, new { groupId });
        return userIds;
    }

    #endregion

    #region Optimized Data Access (Cache-Friendly)

    public async Task<UserBasicData> GetUserBasicDataAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        const string sql = """
                           SELECT name, email 
                           FROM users 
                           WHERE id = @userId AND is_active = true
                           """;

        return await dbConnection.QueryFirstOrDefaultAsync<UserBasicData>(sql, new { userId });
    }

    public async Task<IEnumerable<int>> GetUserPermissionsAsync(Guid userId,
        CancellationToken cancellationToken = default)
    {
        const string sql = """
                           SELECT DISTINCT up.permission_id
                           FROM user_permissions up
                           WHERE up.user_id = @userId 
                             AND up.is_active = true

                           UNION

                           SELECT DISTINCT gp.permission_id
                           FROM user_groups ug
                           INNER JOIN group_permissions gp ON ug.group_id = gp.group_id
                           INNER JOIN groups g ON ug.group_id = g.id
                           WHERE ug.user_id = @userId
                             AND g.is_active = true
                           """;

        var permissions = await dbConnection.QueryAsync<int>(sql, new { userId });
        return permissions;
    }

    public async Task<UserPreferences> GetUserPreferencesAsync(Guid userId,
        CancellationToken cancellationToken = default)
    {
        const string sql = """
                           SELECT 
                               u.last_login_at,
                               COALESCE(up.language, 'pt-BR') as language,
                               COALESCE(up.theme, 0) as theme,
                               COALESCE(up.time_zone, 'America/Sao_Paulo') as time_zone,
                               COALESCE(up.date_default, 'dd/MM/yyyy') as date_default,
                               COALESCE(up.hour_default, 'HH:mm') as hour_default,
                               COALESCE(up.bold_text, false) as bold_text,
                               COALESCE(up.extended_text, false) as extended_text,
                               COALESCE(up.contrast_increased, false) as contrast_increased,
                               t.currency_type_id
                           FROM users u
                           LEFT JOIN user_preferences up ON u.id = up.id
                           LEFT JOIN tenants t ON u.tenant_id = t.tenant_id
                           WHERE u.id = @userId
                           """;

        var result = await dbConnection.QueryFirstOrDefaultAsync(sql, new { userId });

        if (result == null)
            return new UserPreferences();

        return new UserPreferences
        {
            Language = result.language ?? "pt-BR",
            Theme = result.theme,
            TimeZone = result.time_zone ?? "America/Sao_Paulo",
            DateFormat = result.date_default ?? "dd/MM/yyyy",
            TimeFormat = result.hour_default ?? "HH:mm",
            CurrencyTypeId = result.currency_type_id,
            LastLoginAt = result.last_login_at,
            CustomSettings = new Dictionary<string, object>
            {
                ["BoldText"] = result.bold_text,
                ["ExtendedText"] = result.extended_text,
                ["ContrastIncreased"] = result.contrast_increased
            }
        };
    }

    public async Task<UserCompleteData> GetUserCompleteDataAsync(Guid userId,
        CancellationToken cancellationToken = default)
    {
        var basicData = await GetUserBasicDataAsync(userId, cancellationToken);
        if (basicData == null)
            return new UserCompleteData();

        var permissions = await GetUserPermissionsAsync(userId, cancellationToken);
        var preferences = await GetUserPreferencesAsync(userId, cancellationToken);

        return new UserCompleteData
        {
            Name = basicData.Name,
            Email = basicData.Email,
            Roles = permissions,
            Preferences = preferences
        };
    }

    #endregion
}