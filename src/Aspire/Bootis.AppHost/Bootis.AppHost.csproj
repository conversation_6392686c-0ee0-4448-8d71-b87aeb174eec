<Project Sdk="Microsoft.NET.Sdk">

    <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0"/>
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <IsAspireHost>true</IsAspireHost>
        <UserSecretsId>d8eb083f-73e4-4ee8-b550-fa775e565f83</UserSecretsId>
        <RootNamespace>AppHost</RootNamespace>
        <ProjectGuid>11e53c4b-eb27-4d75-a8a3-d55d0a9a2e98</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Aspire.Hosting.AppHost" Version="9.4.2"/>
        <PackageReference Include="Aspire.Hosting.Azure" Version="9.4.2"/>
        <PackageReference Include="Aspire.Hosting.Azure.Storage" Version="9.4.2"/>
        <PackageReference Include="Aspire.Hosting.PostgreSQL" Version="9.4.2"/>
        <PackageReference Include="Aspire.Hosting.RabbitMQ" Version="9.4.2"/>
        <PackageReference Include="Aspire.Hosting.Redis" Version="9.4.2"/>
        <PackageReference Include="Aspire.Hosting.Seq" Version="9.4.2"/>
        <PackageReference Include="Grpc.AspNetCore" Version="2.71.0"/>
        <PackageReference Include="Grpc.Tools" Version="2.72.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.9"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.9"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.9"/>
        <PackageReference Include="Npgsql" Version="9.0.3"/>
        <PackageReference Include="UUIDNext" Version="4.2.0"/>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\CEP\Bootis.CEP.Api\Bootis.CEP.Api.csproj"/>
        <ProjectReference Include="..\..\Email\Bootis.Email.Api\Bootis.Email.Api.csproj"/>
        <ProjectReference Include="..\..\Identity\Bootis.Identity.Api\Bootis.Identity.Api.csproj"/>
        <ProjectReference Include="..\..\Notification\Bootis.Notification.Api\Bootis.Notification.Api.csproj"/>
        <ProjectReference Include="..\..\Pharma\Bootis.Pharma.Api\Bootis.Pharma.Api.csproj"/>
        <ProjectReference Include="..\..\Pharma\Catalogo\Bootis.Catalogo.Api\Bootis.Catalogo.Api.csproj"/>
        <ProjectReference Include="..\..\Report\Bootis.Report.Web\Bootis.Report.Web.csproj"/>
        <ProjectReference Include="..\..\Setup\Bootis.Setup.Api\Bootis.Setup.Api.csproj"/>
    </ItemGroup>

</Project>
