<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <IsAspireSharedProject>true</IsAspireSharedProject>
        <ProjectGuid>17cf9d68-b3f1-4438-b7b5-a3605cc8228b</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App"/>
        <PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.3.0"/>

        <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="9.9.0"/>
        <PackageReference Include="Microsoft.Extensions.ServiceDiscovery" Version="9.4.2"/>
        <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0"/>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>
